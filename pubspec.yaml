name: automoment
version: 1.29.0+195
publish_to: none
description: A new Flutter project.
environment: {sdk: '>=2.18.0 <3.0.0'}
dependencies:
  email_validator: ^3.0.0
  cupertino_icons: ^1.0.2
  get: 4.6.6
  get_storage: ^2.0.3
  http: ^1.2.2
  pretty_http_logger: ^1.0.4
  shared_preferences: ^2.0.15
  cached_network_image: ^3.4.0
  extended_text: ^15.0.2
  webview_flutter: ^4.10.0
  intl_phone_number_input: ^0.7.1
  country_picker: ^2.0.16
  #flutter_datetime_picker: ^1.5.1
  #temporary fix for flutter_datetime_picker
  flutter_datetime_picker:
    git:
      url: https://github.com/Hasan<PERSON>/flutter_datetime_picker.git
  intl: ^0.19.0
  vibration: ^2.0.1
  url_launcher: ^6.1.6
  share_plus: ^10.0.0
  image_picker: ^1.1.2
  file_picker: ^10.0.0
  signature: ^5.3.0
  flutter: {sdk: flutter}
  flutter_local_notifications: ^17.2.2
  flutter_loadingindicator: ^1.0.1
  visibility_detector: ^0.4.0+2
  device_info_plus: ^10.1.2
  google_sign_in: ^6.2.1
  flutter_stripe: ^11.0.0
  sign_in_with_apple: ^7.0.1
  crypto: ^3.0.2
  app_tracking_transparency: ^2.0.3
  pay: ^2.0.0
#  gallery_saver: 
#    git:
#      url: https://github.com/dodyw/gallery_saver.git
#      ref: master
  flutter_chat_ui: ^1.6.6
  flutter_link_previewer: ^3.2.0
  open_filex: ^4.7.0
  path_provider: ^2.0.13
  carousel_slider: ^5.0.0
  contained_tab_bar_view: ^0.8.0
  in_app_update: ^4.1.4
  package_info_plus: ^8.0.2
  card_swiper: ^3.0.1
  flutter_file_downloader: ^2.0.0
  web: ^1.0.0
  firebase_core: ^3.4.0
  firebase_messaging: ^15.1.0
  firebase_auth: ^5.5.3
  firebase_analytics: ^11.3.0
  firebase_database: ^11.1.0
  firebase_storage: ^12.2.0
  background_downloader: ^8.5.3
  timeline_tile: ^2.0.0
  flutter_inner_drawer: ^1.0.0+1
  group_button: ^5.3.4
  mobile_scanner: ^6.0.7
  easy_rich_text: ^2.1.0
  animated_flip_widget: ^0.0.2
  rotated_corner_decoration: ^2.1.0+1
  coupon_uikit: ^0.2.1
  sentry_flutter: ^8.14.2
  app_links: ^6.4.0
  flutter_slidable: ^3.1.2
  fl_chart: ^0.66.2



dev_dependencies:
  flutter_lints: ^4.0.0
  flutter_test: {sdk: flutter}
flutter:
  assets: [assets/images/]
  uses-material-design: true
  fonts: []
