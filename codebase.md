# AutoMoment Technical Documentation

## Overview

Automoment is a comprehensive platform designed to serve automotive enthusiasts through a dual-interface system:

1. **Mobile Application**: A feature-rich client app for end-users
2. **Admin Dashboard**: A Filament-powered administrative panel for business operations

The platform is built on Laravel 9.x with PHP 8.2 and utilizes Filament v2 for the admin dashboard interface. This document provides a technical overview of the system architecture, key components, and functionality.

## System Architecture

### Tech Stack

- **Backend Framework**: Laravel 9.x
- **PHP Version**: 8.2
- **Admin Dashboard**: Filament v2
- **Authentication**: Laravel Sanctum (API tokens)
- **Database**: MySQL (with Eloquent ORM)
- **Storage**: Dual storage system (local and S3)
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Payment Processing**: Stripe and PayNow integration
- **Email Services**: Mandrill/Mailchimp

### Core Components

#### 1. API Backend

The application exposes a comprehensive RESTful API for the mobile application, secured with Laravel Sanctum token authentication. The API handles:

- User authentication (email, Google, Apple Sign-in)
- Event management and bookings
- Vehicle registration and management
- E-commerce functionality (products, car listings)
- Payment processing
- User profile management
- Notifications
- Leaderboards and lap timing

#### 2. Admin Dashboard

Built with Filament v2, the admin dashboard provides a comprehensive interface for managing:

- Users and permissions
- Events and bookings
- Products and car listings
- Content management (news posts)
- Payments and transactions
- Rewards and loyalty programs

## Key Features

### User Management

- Multi-provider authentication (Email, Google, Apple)
- Profile management with vehicle registration
- Wallet and credit system
- Reward points and badges

### Event Management

- Event creation and scheduling
- Booking and registration system
- Check-in functionality
- Form signing and document management
- Additional driver and passenger registration
- Pit allocation

### E-commerce

- Product catalog management
- Car listings (for sale and rent)
- Shopping cart functionality
- Checkout process
- Order management
- Multi-currency support (SGD, MYR)

### Payment Processing

- Stripe integration
- PayNow integration
- Wallet payments
- Credit package purchases
- Discount coupon system

### Tracking and Analytics

- Lap timing and personal bests
- Leaderboards
- User activity tracking
- Performance analytics

### Content Management

- News posts and categories
- Image management
- Sharing functionality

## Data Models

The system includes numerous data models, including but not limited to:

- User
- Event
- Vehicle
- CarListing
- Product
- Transaction
- Wallet
- NewsPost
- Booking
- AdditionalDriver
- Passenger
- Laps
- Reward

## API Endpoints

The API is organized into logical controllers:

- AuthController - Authentication and user registration
- EventController - Event management and bookings
- VehicleController - User vehicle management
- StoreController - E-commerce functionality
- PaymentController - Payment processing
- HomeController - Dashboard data
- LeaderboardController - Competition and timing data
- UserController - Profile management
- ChatController - Messaging functionality
- CheckoutController - Order processing

## Storage Architecture

The application implements a dual storage system:

- Local storage for immediate access and processing
- S3 cloud storage for redundancy and scalability

This is managed through the `DualStorageUploadService` which handles:
- Uploading files to both storage systems
- Retrieving files from the appropriate storage
- Deleting files from both systems when required

## Security Considerations

- API authentication via Laravel Sanctum
- Password hashing with Bcrypt
- Email verification for new accounts
- CSRF protection for web routes
- Input validation and sanitization
- Secure payment processing

## Integration Points

The system integrates with several third-party services:

- **Firebase** - Authentication and push notifications
- **Stripe** - Payment processing
- **PayNow** - Alternative payment method
- **AWS S3** - Cloud storage
- **Mailchimp/Mandrill** - Email marketing and transactional emails
- **Google/Apple** - OAuth authentication

## Monitoring and Error Handling

- Sentry integration for error tracking


## Conclusion

Automoment is a sophisticated platform that combines event management, e-commerce, and community features for automotive enthusiasts. The system architecture balances performance, security, and scalability while providing a comprehensive feature set for both end-users and administrators.

## Mobile Application Architecture

The mobile application is built with Flutter and follows a GetX-based architecture with clear separation of concerns:

### Key Components

1. **Views Layer**: UI components and screens in the modules/*/views directories
2. **Controllers Layer**: Business logic in the modules/*/controllers directories
3. **Bindings Layer**: Dependency injection in the modules/*/bindings directories
4. **Services Layer**: API clients and external integrations in the services directory
5. **Models Layer**: Data models for the application

### Project Structure

```
lib/
├── app/
│   ├── constants/       # App-wide constants and configuration
│   ├── controllers/     # GetX controllers for state management
│   ├── data/           
│   │   └── models/      # Data models
│   ├── helpers/         # Utility functions
│   ├── modules/         # Feature modules (screens and their controllers)
│   ├── routes/          # App routing configuration
│   └── services/        # Service layer for API and external integrations
│       └── api_client.dart
├── main.dart            # Application entry point
```

### State Management

The application uses GetX for state management, which provides:
- Reactive state management with observable variables
- Dependency injection for controllers and services
- Navigation and routing system
- Lifecycle management

### API Client

The API client (`api_client.dart`) is a centralized service for all backend communication:

- Implements RESTful API calls using HTTP
- Returns raw string responses as per the established pattern
- Handles authentication tokens and headers
- Implements error handling and response validation
- Logging capabilities for debugging (configurable)

### Authentication System

The application implements a hybrid authentication system:

1. **Email/Password Authentication**: Traditional login flow with email verification handled by the app's own user management system

2. **Social Authentication**: 
   - **Google Sign-In**: Implemented using Firebase Authentication
   - **Apple Sign-In**: Implemented using Firebase Authentication with secure nonce generation

### Deep Linking System

The application implements a comprehensive deep linking system (`DeepLinkService`) that:

- Handles both cold starts and app-running scenarios
- Supports multiple link types (events, news, lifestyle, leaderboard, etc.)
- Uses a standardized JSON format for all deep link types: `{"id": id}`
- Manages navigation based on link type and parameters
- Preserves deep link state across app restarts

### Payment Integration

The application integrates with Stripe for payment processing, supporting multiple payment methods:

1. **Credit/Debit Card Payments**:
   - Secure integration with Stripe SDK
   - Client-side payment intent creation
   - Support for different currencies (SGD, MYR)
   - Coupon code application for discounts

2. **PayNow Integration**:
   - Singapore's local payment method integration
   - QR code generation and display via WebView
   - Payment status verification with polling mechanism
   - Automatic saving of QR codes to gallery on Android

3. **Wallet/Credit Balance**:
   - In-app wallet balance management and display
   - Credit balance payment option for purchases
   - Real-time balance updates after transactions
   - Wallet top-up handled in backend through manual payment processing

### Push Notification System

The application implements Firebase Cloud Messaging for push notifications with:

- Foreground and background message handling
- Local notification display using Flutter Local Notifications
- Custom notification channels for Android
- Deep link integration for notification actions
- Notification permission handling for iOS

### Security Considerations

1. **Token Management**: Secure token handling using GetStorage for persistent token storage with appropriate token lifecycle management (storage on login, removal on logout)

2. **API Security**: Implementation of secure API communication with proper headers (content-type, authorization) and HTTPS for all network requests

3. **Social Authentication**: Secure implementation of Google and Apple Sign-In with appropriate error handling

4. **Anti-Replay Protection**: Implementation of cryptographically secure nonce generation and SHA-256 hashing for Apple Sign-In to prevent replay attacks

5. **Error Monitoring**: Integration with Sentry for real-time error tracking, crash reporting, and security issue monitoring