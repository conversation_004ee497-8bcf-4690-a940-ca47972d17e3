// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD65F-3VoZGa7bySWdJrNjEh1LY3z7KvDw',
    appId: '1:1033287199145:android:95fc724e46bea17524bcb8',
    messagingSenderId: '1033287199145',
    projectId: 'automoment',
    databaseURL: 'https://automoment-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'automoment.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCVN3KI31qE9WzAuDOoiGx9112I_SajU0A',
    appId: '1:1033287199145:ios:7024f66654f8a8af24bcb8',
    messagingSenderId: '1033287199145',
    projectId: 'automoment',
    databaseURL: 'https://automoment-default-rtdb.asia-southeast1.firebasedatabase.app',
    storageBucket: 'automoment.appspot.com',
    androidClientId: '1033287199145-5qla9q9gk88g448k74l3a36iaqonoibp.apps.googleusercontent.com',
    iosClientId: '1033287199145-lgnfmvauf4j7uoov350qkpd4bqmkrlq9.apps.googleusercontent.com',
    iosBundleId: 'sg.com.automoment.automoment',
  );

}