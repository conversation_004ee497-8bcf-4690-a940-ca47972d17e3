name: gallery_saver
description: Saves images and videos from network or temporary file to external storage. Both images and videos will be visible in Android Gallery and iOS Photos.
version: 2.4.0
homepage: https://github.com/dodyw/gallery_saver

environment:
  sdk: '>=2.12.0 <3.0.0'
  flutter: ">=1.10.0"

module:
 androidX: true

dependencies:
  flutter:
    sdk: flutter
  path_provider: ^2.1.4
  http: ^1.2.2
  path: ^1.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:

  plugin:
    platforms:
      android:
        package: carnegietechnologies.gallery_saver
        pluginClass: GallerySaverPlugin
      ios:
        pluginClass: GallerySaverPlugin


