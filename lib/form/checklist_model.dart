class ChecklistModel {
  List<Data>? data;

  ChecklistModel({this.data});

  ChecklistModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  List<Questions>? questions;

  Data({
    this.questions,
  });

  Data.fromJson(Map<String, dynamic> json) {
    if (json['questions'] != null) {
      questions = <Questions>[];
      json['questions'].forEach((v) {
        questions!.add(Questions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (questions != null) {
      data['questions'] = questions!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class RepeatEnds {
  String? value;

  RepeatEnds({this.value});

  RepeatEnds.fromJson(Map<String, dynamic> json) {
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['value'] = value;
    return data;
  }
}

class Questions {
  List<String>? fields;
  String? sId;
  String? title;
  String? description;
  bool remark = false;
  String? remarkData;
  String? type;
  int? maxline;
  bool? isMandatory;
  var answer;

  Questions({
    this.fields,
    this.sId,
    this.title,
    this.description,
    required this.remark,
    this.type,
    this.maxline,
    this.isMandatory,
  });

  Questions.fromJson(Map<String, dynamic> json) {
    fields = json['fields'].cast<String>();
    sId = json['_id'];
    title = json['title'];
    description = json['description'];
    remark = json['remark'];
    type = json['type'];
    maxline = json["maxline"] ?? 1;
    isMandatory = json['is_mandatory'];
    if (type == "checkbox") {
      answer = List.generate(fields!.length, (index) => false);
    }
    else {
      answer = json['answer'] ?? "";
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fields'] = type == "text" ? "300" : fields.toString();
    data['_id'] = sId;
    data['title'] = title;
    data['description'] = description;
    data['remark'] = remark;
    data["remark_data"] = remarkData;
    data['type'] = type;
    data['is_mandatory'] = isMandatory;
    data["maxline"] = maxline;
    data["answer"] = answer.toString();

    return data;
  }
}
