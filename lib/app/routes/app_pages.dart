import 'package:get/get.dart';

import '../modules/account/bindings/account_binding.dart';
import '../modules/account/views/account_view.dart';
import '../modules/account_delete/bindings/account_delete_binding.dart';
import '../modules/account_delete/views/account_delete_view.dart';
import '../modules/addon/bindings/addon_binding.dart';
import '../modules/addon/views/addon_view.dart';
import '../modules/booking/bindings/booking_binding.dart';
import '../modules/booking/views/booking_view.dart';
import '../modules/booking_success/bindings/booking_success_binding.dart';
import '../modules/booking_success/views/booking_success_view.dart';
import '../modules/bottombar/bindings/bottombar_binding.dart';
import '../modules/bottombar/views/bottombar_view.dart';
import '../modules/car_listing_detail/bindings/car_listing_detail_binding.dart';
import '../modules/car_listing_detail/views/car_listing_detail_view.dart';
import '../modules/cart/bindings/cart_binding.dart';
import '../modules/cart/views/cart_view.dart';
import '../modules/chat/bindings/chat_binding.dart';
import '../modules/chat/views/chat_view.dart';
import '../modules/chat_list/bindings/chat_list_binding.dart';
import '../modules/chat_list/views/chat_list_view.dart';
import '../modules/check_in/bindings/check_in_binding.dart';
import '../modules/check_in/views/check_in_view.dart';
import '../modules/checkout/bindings/checkout_binding.dart';
import '../modules/checkout/views/checkout_view.dart';
import '../modules/claim_rebate/bindings/claim_rebate_binding.dart';
import '../modules/claim_rebate/views/claim_rebate_view.dart';
import '../modules/events/bindings/events_binding.dart';
import '../modules/events/views/events_view.dart';
import '../modules/events_detail/bindings/events_detail_binding.dart';
import '../modules/events_detail/views/events_detail_view.dart';
import '../modules/events_form/bindings/events_form_binding.dart';
import '../modules/events_form/views/events_form_view.dart';
import '../modules/events_form_sign/bindings/events_form_sign_binding.dart';
import '../modules/events_form_sign/views/events_form_sign_view.dart';
import '../modules/events_form_sign_success/bindings/events_form_sign_success_binding.dart';
import '../modules/events_form_sign_success/views/events_form_sign_success_view.dart';
import '../modules/events_form_signed/bindings/events_form_signed_binding.dart';
import '../modules/events_form_signed/views/events_form_signed_view.dart';
import '../modules/events_form_success/bindings/events_form_success_binding.dart';
import '../modules/events_form_success/views/events_form_success_view.dart';
import '../modules/events_form_update/bindings/events_form_update_binding.dart';
import '../modules/events_form_update/views/events_form_update_view.dart';
import '../modules/events_form_update_success/bindings/events_form_update_success_binding.dart';
import '../modules/events_form_update_success/views/events_form_update_success_view.dart';
import '../modules/force_upgrade/bindings/force_upgrade_binding.dart';
import '../modules/force_upgrade/views/force_upgrade_view.dart';
import '../modules/forgot_password_check_code/bindings/forgot_password_check_code_binding.dart';
import '../modules/forgot_password_check_code/views/forgot_password_check_code_view.dart';
import '../modules/forgot_password_email/bindings/forgot_password_email_binding.dart';
import '../modules/forgot_password_email/views/forgot_password_email_view.dart';
import '../modules/forgot_password_reset/bindings/forgot_password_reset_binding.dart';
import '../modules/forgot_password_reset/views/forgot_password_reset_view.dart';
import '../modules/gallery/bindings/gallery_binding.dart';
import '../modules/gallery/views/gallery_view.dart';
import '../modules/gallery_detail/bindings/gallery_detail_binding.dart';
import '../modules/gallery_detail/views/gallery_detail_view.dart';
import '../modules/group/bindings/group_binding.dart';
import '../modules/group/views/group_view.dart';
import '../modules/guest/bindings/guest_binding.dart';
import '../modules/guest/views/guest_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/join_waiting_list/bindings/join_waiting_list_binding.dart';
import '../modules/join_waiting_list/views/join_waiting_list_view.dart';
import '../modules/leaderboard/bindings/leaderboard_binding.dart';
import '../modules/leaderboard/views/leaderboard_view.dart';
import '../modules/leaderboard_detail/bindings/leaderboard_detail_binding.dart';
import '../modules/leaderboard_detail/views/leaderboard_detail_view.dart';
import '../modules/login/views/login_view.dart';
import '../modules/make_payment/bindings/make_payment_binding.dart';
import '../modules/make_payment/views/make_payment_view.dart';
import '../modules/news/bindings/news_binding.dart';
import '../modules/news/views/news_view.dart';
import '../modules/news_detail/bindings/news_detail_binding.dart';
import '../modules/news_detail/views/news_detail_view.dart';
import '../modules/notification/bindings/notification_binding.dart';
import '../modules/notification/views/notification_view.dart';
import '../modules/notification_detail/bindings/notification_detail_binding.dart';
import '../modules/notification_detail/views/notification_detail_view.dart';
import '../modules/order_status/bindings/order_status_binding.dart';
import '../modules/order_status/views/order_status_view.dart';
import '../modules/order_status_detail/bindings/order_status_detail_binding.dart';
import '../modules/order_status_detail/views/order_status_detail_view.dart';
import '../modules/passenger_form/bindings/passenger_form_binding.dart';
import '../modules/passenger_form/views/passenger_form_view.dart';
import '../modules/perlaps/bindings/perlaps_binding.dart';
import '../modules/perlaps/views/perlaps_view.dart';
import '../modules/personal_results/bindings/personal_results_binding.dart';
import '../modules/personal_results/views/personal_results_view.dart';
import '../modules/product_detail/bindings/product_detail_binding.dart';
import '../modules/product_detail/views/product_detail_view.dart';
import '../modules/product_payment/bindings/product_payment_binding.dart';
import '../modules/product_payment/views/product_payment_view.dart';
import '../modules/profile_edit/bindings/profile_edit_binding.dart';
import '../modules/profile_edit/views/profile_edit_view.dart';
import '../modules/reward/bindings/reward_binding.dart';
import '../modules/reward/views/reward_view.dart';
import '../modules/reward_detail/bindings/reward_detail_binding.dart';
import '../modules/reward_detail/views/reward_detail_view.dart';
import '../modules/reward_lifestyle_detail/bindings/reward_lifestyle_detail_binding.dart';
import '../modules/reward_lifestyle_detail/views/reward_lifestyle_detail_view.dart';
import '../modules/select_coupon_code/bindings/select_coupon_code_binding.dart';
import '../modules/select_coupon_code/views/select_coupon_code_view.dart';
import '../modules/sell_my_slot/bindings/sell_my_slot_binding.dart';
import '../modules/sell_my_slot/views/sell_my_slot_view.dart';
import '../modules/shipping_address/bindings/shipping_address_binding.dart';
import '../modules/shipping_address/views/shipping_address_view.dart';
import '../modules/shipping_address_add/bindings/shipping_address_add_binding.dart';
import '../modules/shipping_address_add/views/shipping_address_add_view.dart';
import '../modules/shipping_address_edit/bindings/shipping_address_edit_binding.dart';
import '../modules/shipping_address_edit/views/shipping_address_edit_view.dart';
import '../modules/social_login_form/bindings/social_login_form_binding.dart';
import '../modules/social_login_form/views/social_login_form_view.dart';
import '../modules/store/bindings/store_binding.dart';
import '../modules/store/views/store_view.dart';
import '../modules/update_my_booking/bindings/update_my_booking_binding.dart';
import '../modules/update_my_booking/views/update_my_booking_view.dart';
import '../modules/vehicles/bindings/vehicles_binding.dart';
import '../modules/vehicles/views/vehicles_view.dart';
import '../modules/vehicles_add/bindings/vehicles_add_binding.dart';
import '../modules/vehicles_add/views/vehicles_add_view.dart';
import '../modules/vehicles_edit/bindings/vehicles_edit_binding.dart';
import '../modules/vehicles_edit/views/vehicles_edit_view.dart';
import '../modules/verify_email/bindings/verify_email_binding.dart';
import '../modules/verify_email/views/verify_email_view.dart';
import '../modules/performance_summary/bindings/performance_summary_binding.dart';
import '../modules/performance_summary/views/performance_summary_page.dart';
import '../modules/performance_comparison/bindings/performance_comparison_binding.dart';
import '../modules/performance_comparison/views/performance_comparison_page.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const initial = Routes.BOTTOMBAR;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => NewsView(),
      binding: NewsBinding(),
      // children: [
      //   GetPage(
      //     name: _Paths.HOME,
      //     page: () => const HomeView(),
      //     binding: HomeBinding(),
      //   ),
      // ],
    ),
    GetPage(
      name: _Paths.BOTTOMBAR,
      page: () => BottomBarView(),
      binding: BottombarBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS,
      page: () => EventsView(),
      binding: EventsBinding(),
    ),
    GetPage(
      name: _Paths.ACCOUNT,
      page: () => AccountView(),
      binding: AccountBinding(),
    ),
    GetPage(
      name: _Paths.GUEST,
      page: () => const GuestView(),
      binding: GuestBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
    ),
    GetPage(
      name: _Paths.HOME_DETAIL,
      page: () => NewsDetailView(),
      binding: NewsDetailBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_DETAIL,
      page: () => EventsDetailView(),
      binding: EventsDetailBinding(),
    ),
    GetPage(
      name: _Paths.BOOKING,
      page: () => BookingView(),
      binding: BookingBinding(),
    ),
    GetPage(
      name: _Paths.CHECK_IN,
      page: () => CheckInView(),
      binding: CheckInBinding(),
    ),
    GetPage(
      name: _Paths.BOOKING_SUCCESS,
      page: () => BookingSuccessView(),
      binding: BookingSuccessBinding(),
    ),
    GetPage(
      name: _Paths.FORGOT_PASSWORD_EMAIL,
      page: () => const ForgotPasswordEmailView(),
      binding: ForgotPasswordEmailBinding(),
    ),
    GetPage(
      name: _Paths.FORGOT_PASSWORD_CHECK_CODE,
      page: () => const ForgotPasswordCheckCodeView(),
      binding: ForgotPasswordCheckCodeBinding(),
    ),
    GetPage(
      name: _Paths.FORGOT_PASSWORD_RESET,
      page: () => const ForgotPasswordResetView(),
      binding: ForgotPasswordResetBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_EDIT,
      page: () => const ProfileEditView(),
      binding: ProfileEditBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_FORM,
      page: () => const EventsFormView(),
      binding: EventsFormBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_FORM_SUCCESS,
      page: () => EventsFormSuccessView(),
      binding: EventsFormSuccessBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_FORM_SIGNED,
      page: () => EventsFormSignedView(),
      binding: EventsFormSignedBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_FORM_UPDATE,
      page: () => EventsFormUpdateView(),
      binding: EventsFormUpdateBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_FORM_UPDATE_SUCCESS,
      page: () => EventsFormUpdateSuccessView(),
      binding: EventsFormUpdateSuccessBinding(),
    ),
    GetPage(
      name: _Paths.VEHICLES,
      page: () => VehiclesView(),
      binding: VehiclesBinding(),
    ),
    GetPage(
      name: _Paths.VEHICLES_ADD,
      page: () => const VehiclesAddView(),
      binding: VehiclesAddBinding(),
    ),
    GetPage(
      name: _Paths.VEHICLES_EDIT,
      page: () => const VehiclesEditView(),
      binding: VehiclesEditBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATION,
      page: () => const NotificationView(),
      binding: NotificationBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATION_DETAIL,
      page: () => const NotificationDetailView(),
      binding: NotificationDetailBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_FORM_SIGN,
      page: () => EventsFormSignView(),
      binding: EventsFormSignBinding(),
    ),
    GetPage(
      name: _Paths.EVENTS_FORM_SIGN_SUCCESS,
      page: () => const EventsFormSignSuccessView(),
      binding: EventsFormSignSuccessBinding(),
    ),
    GetPage(
      name: _Paths.ACCOUNT_DELETE,
      page: () => const AccountDeleteView(),
      binding: AccountDeleteBinding(),
    ),
    GetPage(
      name: _Paths.VERIFY_EMAIL,
      page: () => const VerifyEmailView(),
      binding: VerifyEmailBinding(),
    ),
    GetPage(
      name: _Paths.MAKE_PAYMENT,
      page: () => const MakePaymentView(),
      binding: MakePaymentBinding(),
    ),
    GetPage(
      name: _Paths.SOCIAL_LOGIN_FORM,
      page: () => const SocialLoginFormView(),
      binding: SocialLoginFormBinding(),
    ),
    GetPage(
      name: _Paths.CHAT_LIST,
      page: () => ChatListView(),
      binding: ChatListBinding(),
    ),
    GetPage(
      name: _Paths.CHAT,
      page: () => ChatView(),
      binding: ChatBinding(),
    ),
    GetPage(
      name: _Paths.LEADERBOARD,
      page: () => LeaderboardView(),
      binding: LeaderboardBinding(),
    ),
    GetPage(
      name: _Paths.PERSONAL_RESULTS,
      page: () => const PersonalResultsView(),
      binding: PersonalResultsBinding(),
    ),
    GetPage(
      name: _Paths.LEADERBOARD_DETAIL,
      page: () => const LeaderboardDetailView(),
      binding: LeaderboardDetailBinding(),
    ),
    GetPage(
      name: _Paths.FORCE_UPGRADE,
      page: () => const ForceUpgradeView(),
      binding: ForceUpgradeBinding(),
    ),
    GetPage(
      name: _Paths.PERLAPS,
      page: () => PerlapsView(),
      binding: PerlapsBinding(),
    ),
    GetPage(
      name: _Paths.GALLERY,
      page: () => GalleryView(),
      binding: GalleryBinding(),
    ),
    GetPage(
      name: _Paths.GALLERY_DETAIL,
      page: () => GalleryDetailView(),
      binding: GalleryDetailBinding(),
    ),
    GetPage(
      name: _Paths.JOIN_WAITING_LIST,
      page: () => JoinWaitingListView(),
      binding: JoinWaitingListBinding(),
    ),
    GetPage(
      name: _Paths.ADDON,
      page: () => const AddonView(),
      binding: AddonBinding(),
    ),
    GetPage(
      name: _Paths.UPDATE_MY_BOOKING,
      page: () => UpdateMyBookingView(),
      binding: UpdateMyBookingBinding(),
    ),
    GetPage(
      name: _Paths.SELL_MY_SLOT,
      page: () => SellMySlotView(),
      binding: SellMySlotBinding(),
    ),
    GetPage(
      name: _Paths.REWARD,
      page: () => RewardView(),
      binding: RewardBinding(),
    ),
    GetPage(
      name: _Paths.REWARD_DETAIL,
      page: () => RewardDetailView(),
      binding: RewardDetailBinding(),
    ),
    GetPage(
      name: _Paths.SELECT_COUPON_CODE,
      page: () => SelectCouponCodeView(),
      binding: SelectCouponCodeBinding(),
    ),
    GetPage(
      name: _Paths.STORE,
      page: () => StoreView(),
      binding: StoreBinding(),
    ),
    GetPage(
      name: _Paths.PRODUCT_DETAIL,
      page: () => const ProductDetailView(),
      binding: ProductDetailBinding(),
    ),
    GetPage(
      name: _Paths.CAR_LISTING_DETAIL,
      page: () => const CarListingDetailView(),
      binding: CarListingDetailBinding(),
    ),
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.PASSENGER_FORM,
      page: () => const PassengerFormView(),
      binding: PassengerFormBinding(),
    ),
    GetPage(
      name: _Paths.GROUP,
      page: () => GroupView(),
      binding: GroupBinding(),
    ),
    GetPage(
      name: _Paths.CLAIM_REBATE,
      page: () => ClaimRebateView(),
      binding: ClaimRebateBinding(),
    ),
    GetPage(
      name: _Paths.REWARD_LIFESTYLE_DETAIL,
      page: () => const RewardLifestyleDetailView(),
      binding: RewardLifestyleDetailBinding(),
    ),
    GetPage(
      name: _Paths.CART,
      page: () => const CartView(),
      binding: CartBinding(),
    ),
    GetPage(
      name: _Paths.CHECKOUT,
      page: () => const CheckoutView(),
      binding: CheckoutBinding(),
    ),
    GetPage(
      name: _Paths.SHIPPING_ADDRESS,
      page: () => const ShippingAddressView(),
      binding: ShippingAddressBinding(),
    ),
    GetPage(
      name: _Paths.SHIPPING_ADDRESS_ADD,
      page: () => const ShippingAddressAddView(),
      binding: ShippingAddressAddBinding(),
    ),
    GetPage(
      name: _Paths.SHIPPING_ADDRESS_EDIT,
      page: () => const ShippingAddressEditView(),
      binding: ShippingAddressEditBinding(),
    ),
    GetPage(
      name: _Paths.PRODUCT_PAYMENT,
      page: () => const ProductPaymentView(),
      binding: ProductPaymentBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_STATUS,
      page: () => const OrderStatusView(),
      binding: OrderStatusBinding(),
    ),
    GetPage(
      name: _Paths.ORDER_STATUS_DETAIL,
      page: () => const OrderStatusDetailView(),
      binding: OrderStatusDetailBinding(),
    ),
    GetPage(
      name: _Paths.PERFORMANCE_SUMMARY,
      page: () => const PerformanceSummaryPage(),
      binding: PerformanceSummaryBinding(),
    ),
    GetPage(
      name: _Paths.PERFORMANCE_COMPARISON,
      page: () => const PerformanceComparisonPage(),
      binding: PerformanceComparisonBinding(),
    ),
  ];
}
