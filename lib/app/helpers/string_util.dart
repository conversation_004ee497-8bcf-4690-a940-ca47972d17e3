import 'dart:math';

import 'countries.dart';
import 'package:intl/intl.dart';

class StringUtil {
  static String maskMobileNumber(String number) {
    // Remove any non-digit characters
    number = number.replaceAll(RegExp(r'\D'), '');

    if (number.length < 4) return number; // Return original if too short

    String masked = number.substring(0, 2) +
        '*' * (number.length - 4) +
        number.substring(number.length - 2);

    return masked;
  }

  static String maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return email; // Return original if not a valid email

    String username = parts[0];
    String domain = parts[1];

    // Mask the username part
    if (username.length > 2) {
      username = username.substring(0, 2) + '*' * (username.length - 2);
    }

    return '$username@$domain';
  }

  static String timeAgo(DateTime date) {
    final DateTime now = DateTime.now();
    final Duration diff = now.difference(date);

    if (diff.inDays > 365) {
      return '${(diff.inDays / 365).floor()} years ago';
    } else if (diff.inDays > 30) {
      return '${(diff.inDays / 30).floor()} months ago';
    } else if (diff.inDays > 7) {
      return '${(diff.inDays / 7).floor()} weeks ago';
    } else if (diff.inDays > 0) {
      return '${diff.inDays} days ago';
    } else if (diff.inHours > 0) {
      return '${diff.inHours} hours ago';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes} minutes ago';
    } else {
      return 'just now';
    }
  }

  static String shortenText(String text, int numberOfCharacters) {
    if (text.length > numberOfCharacters) {
      return '${text.substring(0, numberOfCharacters)}...';
    } else {
      return text;
    }
  }

  static String randomString(int length) {
    var r = Random();
    const chars = '0123456789';
    return List.generate(length, (index) => chars[r.nextInt(chars.length)]).join();
  }

  static String formatMoneyWithoutCent(double amount) {
    final formatter = NumberFormat.currency(locale: 'en_SG', symbol: '', decimalDigits: 0);
    return formatter.format(amount);
  }

  static String? getCountryCodeFromPhoneNumber(String phoneNumber) {
    Map<String, String> foundedCountry = {};
    for (var country in Countries.allCountries) {
      String dialCode = country["dial_code"].toString();
      if (phoneNumber.contains(dialCode)) {
        foundedCountry = country;
      }
    }

    if (foundedCountry.isNotEmpty) {
      return foundedCountry["code"];
    }

    return null;
  }

  static String? removeCountryCodeFromPhoneNumber(String phoneNumber) {
    Map<String, String> foundedCountry = {};
    for (var country in Countries.allCountries) {
      String dialCode = country["dial_code"].toString();
      if (phoneNumber.contains(dialCode)) {
        foundedCountry = country;
      }
    }

    if (foundedCountry.isNotEmpty) {
      var newPhoneNumber = phoneNumber.substring(
        foundedCountry["dial_code"]!.length,
      );

      return newPhoneNumber;
    }

    return null;
  }

  static String dateTimeToString(DateTime dateTime) {
    return DateFormat('dd MMMM yyyy').format(dateTime);
  }

  static String getFilenameFromUrl(String url) {
    return url.split('/').last;
  }

  static List<String> findUrls(String str) {
    List<String> urls = [];
    // Regex pattern to match URLs
    RegExp urlPattern = RegExp(
      r'(https?://)?(www\.)?([a-zA-Z0-9]+(\.[a-zA-Z]{2,})+)(/[a-zA-Z0-9@:%_\+~#?&//=]*)?',
      caseSensitive: false,
    );

    // Find matches and add them to the list
    Iterable<Match> matches = urlPattern.allMatches(str);
    for (Match match in matches) {
      urls.add(match.group(0)!); // Add the matched URL (non-null guaranteed by the match)
    }

    return urls;
  }
}