import 'dart:developer' as developer;

class DebugHelper {
  /// Simple log method that works like print but only in debug mode
  /// 
  /// Example usage:
  /// ```dart
  /// DebugHelper.d('User logged in');  // Like a simple print statement
  /// ```
  static void d(dynamic message) {
    developer.log(message.toString());
  }

  /// Enhanced log method with optional parameters
  /// 
  /// Example usage:
  /// ```dart
  /// DebugHelper.log('User logged in');  // Simple usage
  /// DebugHelper.log('API Response', name: 'Network', data: response.body);  // Detailed usage
  /// ```
  static void log(
    dynamic message, {
    String? name,
    DateTime? time,
    Object? error,
    dynamic data,
    StackTrace? stackTrace,
  }) {
    developer.log(
      '${message.toString()}${data != null ? '\nData: $data' : ''}',
      name: name ?? 'DEBUG',
      time: time,
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log an error message
  static void error(
    dynamic message, {
    String? name,
    Object? error,
    StackTrace? stackTrace,
  }) {
    developer.log(
      message.toString(),
      name: name ?? 'ERROR',
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log a warning message
  static void warning(
    dynamic message, {
    dynamic data,
    String? name,
  }) {
    developer.log(
      '${message.toString()}${data != null ? '\nData: $data' : ''}',
      name: name ?? 'WARNING',
    );
  }
}
