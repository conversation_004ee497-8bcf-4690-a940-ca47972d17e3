import 'package:firebase_database/firebase_database.dart';


class FirebaseDatabaseDao {

  static init() {
    FirebaseDatabase.instance.setLoggingEnabled(true);
    FirebaseDatabase.instance.databaseURL = "https://automoment-default-rtdb.asia-southeast1.firebasedatabase.app";
    FirebaseDatabase.instance.setPersistenceEnabled(true);
    FirebaseDatabase.instance.setPersistenceCacheSizeBytes(10000000);
  }

  /*
  static FirebaseDatabaseUtil? _instance;

  static FirebaseDatabaseUtil get instance {
    _instance ??= FirebaseDatabaseUtil._init();
    return _instance!;
  }

  FirebaseDatabaseUtil._init();

  final FirebaseDatabase _database = FirebaseDatabase.instance;

  DatabaseReference get reference => _database.reference();

  DatabaseReference get messagesRef => _database.reference().child('messages');

  DatabaseReference get usersRef => _database.reference().child('users');

  DatabaseReference get userRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}');

  DatabaseReference get userMessagesRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages');

  DatabaseReference get userMessageRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}');

  DatabaseReference get userMessageChatRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat');

  DatabaseReference get userMessageChatMessageRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message');

  DatabaseReference get userMessageChatMessageSenderRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message/sender');

  DatabaseReference get userMessageChatMessageTextRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message/text');

  DatabaseReference get userMessageChatMessageDateRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message/date');

  DatabaseReference get userMessageChatMessageReadRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message/read');

  DatabaseReference get userMessageChatMessageReadDateRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message/readDate');

  DatabaseReference get userMessageChatMessageReadDateSenderRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message/readDate/sender');

  DatabaseReference get userMessageChatMessageReadDateDateRef =>
      _database.reference().child('users/${UserController.instance
          .getUser()
          .id}/messages/${UserController.instance
          .getUser()
          .id}/chat/message/readDate/date');
*/
}