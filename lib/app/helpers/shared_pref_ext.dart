import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../models/user_model.dart';

class SharedPrefExt {
  static Future<void> saveUserModel(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('usermodel', json<PERSON>ncode(user));
  }

  static Future<User> getUserModel() async {
    final prefs = await SharedPreferences.getInstance();
    String json = prefs.getString('usermodel') ?? "";
    return jsonDecode(json);
  }

}