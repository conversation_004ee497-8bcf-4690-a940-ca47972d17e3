import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:pretty_http_logger/pretty_http_logger.dart';

import '../constants/app_config.dart';

class ApiClient {
  static final HttpWithMiddleware httpClient = HttpWithMiddleware.build(
    middlewares: AppConfig.enableHttpLogging
        ? [HttpLogger(logLevel: AppConfig.httpLogLevel)]
        : [],
  );

  static bool isValidResponseCode(code) {
    if (code == 200 || code == 422 || code == 400) {
      return true;
    }

    return false;
  }

  static Future<String> register(
      String name,
      String email,
      String password,
      String passwordConfirm,
      String deviceName,
      String address,
      String country,
      String mobileNumber,
      DateTime? dateOfBirth) async {
    String? birth =
        dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth) : "";

    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}auth/register'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
            },
            body: jsonEncode({
              'name': name,
              'email': email,
              'password': password,
              'password_confirmation': passwordConfirm,
              'device_name': deviceName,
              'address': address,
              'country': country,
              'mobile_number': mobileNumber,
              'birth': birth
            }));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> verifyEmail(
      String email, String code, String deviceName) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}auth/verifyEmail'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode(
            {'email': email, 'code': code, 'device_name': deviceName}));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> login(
      String email, String password, String deviceName) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}auth/login'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode(
            {'email': email, 'password': password, 'device_name': deviceName}));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getUserOrders(String token, {int page = 1}) async {
    http.Response response = await httpClient.get(
      Uri.parse('${AppConfig.baseUrl}checkout/getUserOrders').replace(
        queryParameters: {
          'page': page.toString(),
        },
      ),
      headers: {
        HttpHeaders.authorizationHeader: 'Bearer $token',
        HttpHeaders.acceptHeader: 'application/json',
      },
    );

    if (isValidResponseCode(response.statusCode)) {
      return response.body;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getOrderStatus(String token, int orderId) async {
    http.Response response = await httpClient.get(
      Uri.parse('${AppConfig.baseUrl}checkout/getOrderStatus').replace(
        queryParameters: {
          'order_id': orderId.toString(),
        },
      ),
      headers: {
        HttpHeaders.authorizationHeader: 'Bearer $token',
        HttpHeaders.acceptHeader: 'application/json',
      },
    );

    if (isValidResponseCode(response.statusCode)) {
      return response.body;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> signInWithGoogle(
      String idToken, String deviceName) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}auth/signInWithGoogle'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode({'tokenId': idToken, 'deviceName': deviceName}));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> signInWithApple(
      String idToken, String deviceName, String displayName) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}auth/signInWithApple'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode({
          'tokenId': idToken,
          'deviceName': deviceName,
          'displayName': displayName
        }));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> updateSocialLoginProfile(
      String token, String name, String country, String mobileNumber) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}updateSocialLoginProfile'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(
            {'name': name, 'country': country, 'mobile_number': mobileNumber}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> logout(String token) async {
    http.Response response = await httpClient
        .post(Uri.parse('${AppConfig.baseUrl}auth/logout'), headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.acceptHeader: 'application/json',
      HttpHeaders.authorizationHeader: 'Bearer $token',
    });

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getNews() async {
    http.Response response =
        await httpClient.get(Uri.parse('${AppConfig.baseUrl}getNews'));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getNewsFromId(int id) async {
    http.Response response =
        await httpClient.get(Uri.parse('${AppConfig.baseUrl}getNews/$id'));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getEvents() async {
    http.Response response =
        await httpClient.get(Uri.parse('${AppConfig.baseUrl}getEvents'));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getEventFromId(int id) async {
    http.Response response =
        await httpClient.get(Uri.parse('${AppConfig.baseUrl}getEvent/$id'));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> isRegistered(
      String token, int userId, int eventId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}isRegistered'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'userId': userId, 'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> checkIn(String token, int userId, int eventId,
      String decal, String transponder) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}checkin'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'userId': userId,
          'eventId': eventId,
          'decal': decal,
          'transponder': transponder
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> registerEvent(
      String token,
      int userId,
      int eventId,
      int vehicleId,
      String? plateNumber,
      int? pitNumber,
      int? pitPosition) async {
    // Use a map to store the body parameters
    Map<String, dynamic> bodyMap = {
      'userId': userId,
      'eventId': eventId,
      'vehicleId': vehicleId,
    };

    // Add optional parameters only if they are not null
    if (plateNumber != null) {
      bodyMap['plateNumber'] = plateNumber;
    }
    if (pitNumber != null) {
      bodyMap['pitNumber'] = pitNumber;
    }
    if (pitPosition != null) {
      bodyMap['pitPosition'] = pitPosition;
    }

    // Convert the map to a JSON string
    var body = jsonEncode(bodyMap);

    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}registerEvent'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: body);
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> updateMyBooking(String token, int userId, int eventId,
      int vehicleId, String? plateNumber, int? pitNo, int? pitPos) async {
    var body = jsonEncode({
      'userId': userId,
      'eventId': eventId,
      'vehicleId': vehicleId,
      if (plateNumber != null) 'plateNumber': plateNumber,
      if (pitNo != null) 'pitNumber': pitNo,
      if (pitPos != null) 'pitPosition': pitPos
    });

    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}updateMyBooking'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: body);
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> checkBookingStatus(
      String token, int userId, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}checkBookingStatus'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'userId': userId, 'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> resetPasswordEmail(String email) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}resetPassword/sendResetCode'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode({'email': email}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> resetPasswordCheckCode(String code) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}resetPassword/checkCode'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode({'code': code}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> resetPasswordReset(String code, String password) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}resetPassword/resetPassword'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode({
          'code': code,
          'password': password,
          'password_confirm': password
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> updateProfile(
      String token,
      int id,
      String name,
      String email,
      String address,
      String country,
      String mobilePhone,
      DateTime? dateOfBirth) async {
    String? birth =
        dateOfBirth != null ? DateFormat('yyyy-MM-dd').format(dateOfBirth) : "";

    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}updateProfile'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              "id": id,
              "name": name,
              "email": email,
              "address": address,
              "country": country,
              "mobile_number": mobilePhone,
              'birth': birth
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> uploadPhoto(
      String token, int id, String image64) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}uploadPhoto'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              "id": id,
              "image": image64,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getEventForm(String token, int id) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}getEventForm'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              "eventId": id,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> submitEventForm(String token, int userId, int eventId,
      String formData, String signature) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}submitEventForm'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'userId': userId,
          'eventId': eventId,
          'form': formData,
          'signature': signature
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> isEventFormSigned(String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}isEventFormSigned'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> isEventFormSignedPassenger(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}isEventFormSignedPassenger'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getSignedEventForm(String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getSignedEventForm'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getSignedEventFormPassenger(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getSignedEventFormPassenger'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> registerFcmToken(String token, String fcmToken) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}registerFCMToken'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'fcmToken': fcmToken}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getVehicles(String token, int userId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}getVehicles'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'userId': userId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> deleteVehicles(
      String token, int userId, int vehicleId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}deleteVehicle'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'userId': userId, 'vehicleId': vehicleId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> addVehicles(String token, int userId, String make,
      String model, String year, String type, String image) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}addVehicle'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'userId': userId,
              'make': make,
              'model': model,
              'year': year,
              'type': type,
              'image': image
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> editVehicles(
      String token,
      int userId,
      int vehicleId,
      String make,
      String model,
      String year,
      String type,
      String? image) async {
    var bodyNoImage = {
      'userId': userId,
      'vehicleId': vehicleId,
      'make': make,
      'model': model,
      'year': year,
      'type': type
    };

    var bodyWithImage = {
      'userId': userId,
      'vehicleId': vehicleId,
      'make': make,
      'model': model,
      'year': year,
      'type': type,
      'image': image
    };

    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}editVehicle'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: (image == null)
            ? jsonEncode(bodyNoImage)
            : jsonEncode(bodyWithImage));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> uploadVehiclePhoto(
      String token, int userId, int vehicleId, String image) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}uploadVehiclePhoto'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(
            {'userId': userId, 'vehicleId': vehicleId, 'image': image}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getNotifications(String token, int userId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}getNotifications'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'userId': userId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getNotificationDetail(
      String token, int notificationId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getNotificationDetail'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'notificationId': notificationId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getNotificationsPublicUser() async {
    http.Response response = await httpClient
        .post(Uri.parse('${AppConfig.baseUrl}getNotificationsPublicUser'));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getRebate(String token, int eventId) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}getRebate'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
      body: jsonEncode({
        'eventId': eventId,
      }),
    );

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> claimRebate(
      String token, int eventId, String link) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}claimRebate'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
      body: jsonEncode({
        'eventId': eventId,
        'link': link,
      }),
    );

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getUnreadNotificationCount(
      String token, int userId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getUnreadNotificationCount'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'userId': userId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> markNotificationAsRead(
      String token, int userId, int notificationId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}markNotificationAsRead'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'userId': userId, 'notificationId': notificationId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> userDeleteRequest(String token, int userId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}userDeleteRequest'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'userId': userId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> freeBooking(String token, int eventId, String code,
      bool additionalDriver, String currency) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}freeBooking'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'eventId': eventId,
              'couponCode': code,
              'additionalDriver': additionalDriver,
              'currency': currency
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> freeBookingForAdditionalDriver(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}freeBookingForAdditionalDriver'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> stripePayment(String token, int eventId, String code,
      bool additionalDriver, String currency) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}stripePayment'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'eventId': eventId,
              'couponCode': code,
              'additionalDriver': additionalDriver,
              'currency': currency
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> payNowPayment(
      String token, int eventId, String code, bool additionalDriver) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}payNowPayment'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
          'couponCode': code,
          'additionalDriver': additionalDriver
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> stripePaymentPassenger(
      String token, int eventId, String code, String currency) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}stripePaymentPassenger'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(
            {'eventId': eventId, 'couponCode': code, 'currency': currency}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> stripePaymentAdditionalDriver(
      String token, int eventId, String code, String currency) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}stripePaymentAdditionalDriver'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(
            {'eventId': eventId, 'couponCode': code, 'currency': currency}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> stripePaymentGroup(String token, int eventId,
      int groupId, String code, String currency) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}stripePaymentGroup'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
          'couponCode': code,
          'currency': currency,
          'groupId': groupId
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> payNowPaymentPassenger(
      String token, int eventId, String code) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}payNowPaymentPassenger'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'eventId': eventId, 'couponCode': code}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> payNowPaymentAdditionalDriver(
      String token, int eventId, String code) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}payNowPaymentAdditionalDriver'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'eventId': eventId, 'couponCode': code}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> payNowPaymentGroup(
      String token, int eventId, int groupId, String code) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}payNowPaymentGroup'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(
            {'eventId': eventId, 'groupId': groupId, 'couponCode': code}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> checkDiscountCoupon(
      String token, int eventId, String code) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}checkDiscountCoupon'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({'eventId': eventId, 'couponCode': code}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> myEvents(String token) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}myEvents'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
    );
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> leaderboards(
      String scoringType, List<String> filter) async {
    debugPrint(
        'ApiClient.leaderboards called with scoringType: $scoringType, filter: $filter');
    debugPrint('Request URL: ${AppConfig.baseUrl}leaderboards');
    final requestBody =
        jsonEncode({'scoringType': scoringType, 'filter': filter});
    debugPrint('Request body: $requestBody');

    try {
      http.Response response =
          await httpClient.post(Uri.parse('${AppConfig.baseUrl}leaderboards'),
              headers: {
                HttpHeaders.contentTypeHeader: 'application/json',
                HttpHeaders.acceptHeader: 'application/json',
              },
              body: requestBody);

      debugPrint('Response status code: ${response.statusCode}');

      if (isValidResponseCode(response.statusCode)) {
        var jsonString = response.body;
        debugPrint('Response body length: ${jsonString.length}');
        debugPrint(
            'Response body preview: ${jsonString.substring(0, jsonString.length > 100 ? 100 : jsonString.length)}...');
        return jsonString;
      } else {
        debugPrint('Invalid response code: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
        throw Exception(
            "Something wrong with the server. Status code: ${response.statusCode}");
      }
    } catch (e) {
      debugPrint('Exception in leaderboards API call: $e');
      throw Exception("API call failed: $e");
    }
  }

  static Future<String> leaderboardDetail(
      int eventId, String scoringType) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}leaderboardDetail'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
        },
        body: jsonEncode({'eventId': eventId, 'scoringType': scoringType}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getNewsSlider() async {
    http.Response response =
        await httpClient.get(Uri.parse('${AppConfig.baseUrl}getNewsSlider'));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> checkAppVersions() async {
    http.Response response = await httpClient
        .post(Uri.parse('${AppConfig.baseUrl}checkAppVersions'));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> perLaps(String token, int eventId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}perLaps'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getMyPhotos(String token) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}getMyPhotos'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
    );
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getSharablePhoto(
      int photoId, int eventId, String token) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}getSharablePhoto'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'photoId': photoId, 'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getAddons(String token, int eventId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}getAddons'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({'eventId': eventId}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> orderAddon(String token, int eventId, List<int> addons,
      String currency, String paymentMethod) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}orderAddon'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'event_id': eventId,
              'addons': addons,
              'currency': currency,
              'payment_method': paymentMethod
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> checkUserExists(String token, String email) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}checkUserExists'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'email': email,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> sellMySlot(
      String token, int eventId, String email) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}sellMySlot'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'email': email,
              'eventId': eventId,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getUserVehiclesAndAvailablePits(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getUserVehiclesAndAvailablePits'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getRewards(String token) async {
    http.Response response = await httpClient
        .post(Uri.parse('${AppConfig.baseUrl}getRewards'), headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.acceptHeader: 'application/json',
      HttpHeaders.authorizationHeader: 'Bearer $token',
    });
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> pointExchange(String token, int rewardId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}pointExchange'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'rewardId': rewardId,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getProductsAndCarListings(String token) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getProductsAndCarListings'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        });
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getProducts(String token) async {
    http.Response response = await httpClient
        .post(Uri.parse('${AppConfig.baseUrl}getProducts'), headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.acceptHeader: 'application/json',
      HttpHeaders.authorizationHeader: 'Bearer $token',
    });
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getProductDetail(String token, int productId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}getProductDetail'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'productId': productId,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getCarListings(String token) async {
    http.Response response = await httpClient
        .post(Uri.parse('${AppConfig.baseUrl}getCarListings'), headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.acceptHeader: 'application/json',
      HttpHeaders.authorizationHeader: 'Bearer $token',
    });
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getCarListingDetail(
      String token, int carListingId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getCarListingDetail'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'carListingId': carListingId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getHome(String token) async {
    http.Response response = await httpClient
        .post(Uri.parse('${AppConfig.baseUrl}getHome'), headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.acceptHeader: 'application/json',
      HttpHeaders.authorizationHeader: 'Bearer $token',
    });
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> cancelMyBooking(String token, int eventId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}cancelMyBooking'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'eventId': eventId,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> checkCancelMyBookingStatus(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}checkCancelMyBookingStatus'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> registerAsPassenger(String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}registerAsPassenger'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getFormAsPassenger(String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getFormAsPassenger'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> signFormAsPassenger(
      String token, int eventId, String form, String signature) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}signFormAsPassenger'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(
            {'eventId': eventId, 'form': form, 'signature': signature}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> registerAsAdditionalDriver(
      String token,
      int eventId,
      int mainDriverId,
      int vehicleId,
      String? plateNumber,
      int? pitNumber,
      int? pitPosition) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}registerAsAdditionalDriver'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
          'mainDriverId': mainDriverId,
          'vehicleId': vehicleId,
          'plateNumber': plateNumber ?? '',
          'pitNumber': pitNumber ?? '',
          'pitPosition': pitPosition ?? ''
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> registerAsGroup(
      String token,
      int eventId,
      String participants,
      int vehicleId,
      String? plateNumber,
      int? pitNumber,
      int? pitPosition) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}registerAsGroup'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'eventId': eventId,
              'participants': participants,
              'vehicleId': vehicleId,
              'plateNumber': plateNumber ?? '',
              'pitNumber': pitNumber ?? '',
              'pitPosition': pitPosition ?? ''
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getFormAsAdditionalDriver(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}getFormAsAdditionalDriver'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> signFormAsAdditionalDriver(
      String token, int eventId, String form, String signature) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}signFormAsAdditionalDriver'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(
            {'eventId': eventId, 'form': form, 'signature': signature}));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> lookupMainDriver(String token, int eventId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}lookupMainDriver'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: jsonEncode({
              'eventId': eventId,
            }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> lookupAllDriver(String token) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}lookupAllDriver'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
    );
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> cancelNoPaymentDriverRegistration(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}cancelNoPaymentDriverRegistration'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> cancelNoPaymentAdditionalDriverRegistration(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse(
            '${AppConfig.baseUrl}cancelNoPaymentAdditionalDriverRegistration'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> cancelNoPaymentPassengerRegistration(
      String token, int eventId) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}cancelNoPaymentPassengerRegistration'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'eventId': eventId,
        }));
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<Map<String, dynamic>> addToCart(
      String token, int productId, int quantity) async {
    try {
      final response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}cart/add'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'product_id': productId,
          'quantity': quantity,
        }),
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      if (responseData['success'] == true) {
        return responseData['data'];
      } else {
        throw Exception(responseData['message'] ?? 'Failed to add to cart');
      }
    } catch (e) {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<Map<String, dynamic>> fetchCart(String token) async {
    try {
      final response = await httpClient.get(
        Uri.parse('${AppConfig.baseUrl}cart'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      if (responseData['success'] == true) {
        return responseData['data'];
      } else {
        throw Exception(responseData['message'] ?? 'Failed to fetch cart data');
      }
    } catch (e) {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<Map<String, dynamic>> updateCartItemQuantity(
      String token, int itemId, int quantity) async {
    try {
      final response = await httpClient.put(
        Uri.parse('${AppConfig.baseUrl}cart/update/$itemId'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'quantity': quantity,
        }),
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      if (responseData['success'] == true) {
        return responseData['data'];
      } else {
        throw Exception(
            responseData['message'] ?? 'Failed to update cart item');
      }
    } catch (e) {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<Map<String, dynamic>> removeCartItem(
      String token, int itemId) async {
    try {
      final response = await httpClient.delete(
        Uri.parse('${AppConfig.baseUrl}cart/remove/$itemId'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      if (responseData['success'] == true) {
        return responseData['data'];
      } else {
        throw Exception(
            responseData['message'] ?? 'Failed to remove cart item');
      }
    } catch (e) {
      throw Exception("Something wrong with the server.");
    }
  }

  // Shipping Address APIs
  static Future<dynamic> getShippingAddresses(String token) async {
    try {
      final response = await httpClient.get(
        Uri.parse('${AppConfig.baseUrl}checkout/addresses'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      return jsonDecode(response.body);
    } catch (e) {
      rethrow;
    }
  }

  static Future<dynamic> addShippingAddress(
      String token, Map<String, dynamic> address) async {
    try {
      final response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}checkout/addresses'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(address),
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      return jsonDecode(response.body);
    } catch (e) {
      rethrow;
    }
  }

  static Future<dynamic> updateShippingAddress(
      String token, int id, Map<String, dynamic> address) async {
    try {
      final response = await httpClient.put(
        Uri.parse('${AppConfig.baseUrl}checkout/addresses/$id'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(address),
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      return jsonDecode(response.body);
    } catch (e) {
      rethrow;
    }
  }

  static Future<dynamic> deleteShippingAddress(String token, int id) async {
    try {
      final response = await httpClient.delete(
        Uri.parse('${AppConfig.baseUrl}checkout/addresses/$id'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      return jsonDecode(response.body);
    } catch (e) {
      rethrow;
    }
  }

  static Future<dynamic> setDefaultShippingAddress(String token, int id) async {
    try {
      final response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}checkout/addresses/$id/default'),
        headers: {
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
      );

      if (response.statusCode == 401) {
        throw Exception('Unauthorized access');
      }

      return jsonDecode(response.body);
    } catch (e) {
      rethrow;
    }
  }

  static Future<String> makeProductOrder(String token, int? shippingAddressId,
      {Map<String, dynamic>? orderData}) async {
    final Map<String, dynamic> requestBody = {};

    if (shippingAddressId != null) {
      requestBody['shipping_address_id'] = shippingAddressId;
    }

    if (orderData != null) {
      requestBody.addAll(orderData);
    }

    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}checkout/makeProductOrder'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode(requestBody));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> stripePaymentCart(
      String token, int orderId, String currency) async {
    http.Response response = await httpClient.post(
        Uri.parse('${AppConfig.baseUrl}stripePaymentCart'),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          HttpHeaders.acceptHeader: 'application/json',
          HttpHeaders.authorizationHeader: 'Bearer $token',
        },
        body: jsonEncode({
          'order_id': orderId,
          'currency': currency,
        }));

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getOrderDetail(String token, String orderId) async {
    http.Response response = await httpClient.get(
      Uri.parse('${AppConfig.baseUrl}checkout/getOrderDetail').replace(
        queryParameters: {'order_id': orderId},
      ),
      headers: {
        HttpHeaders.authorizationHeader: 'Bearer $token',
        HttpHeaders.acceptHeader: 'application/json',
      },
    );

    if (isValidResponseCode(response.statusCode)) {
      return response.body;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getWalletBalance(String token) async {
    http.Response response = await httpClient.get(
      Uri.parse('${AppConfig.baseUrl}wallet/balance'),
      headers: {
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
    );

    if (isValidResponseCode(response.statusCode)) {
      return response.body;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> payWithCredit(
      String token, int eventId, String paymentType, String code) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}payWithCredit'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
      body: jsonEncode({
        'event_id': eventId,
        'payment_type': paymentType,
        'coupon_code': code
      }),
    );

    return response.body;
  }

  static Future<String> getVehicleLeaderboard(String token, int eventId) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}vehicle-leaderboard'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
      body: jsonEncode({
        'eventId': eventId,
      }),
    );

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getCarMakers(String token, int? eventId) async {
    http.Response response =
        await httpClient.post(Uri.parse('${AppConfig.baseUrl}getCarMakers'),
            headers: {
              HttpHeaders.contentTypeHeader: 'application/json',
              HttpHeaders.acceptHeader: 'application/json',
              HttpHeaders.authorizationHeader: 'Bearer $token',
            },
            body: eventId != null
                ? jsonEncode({
                    'eventId': eventId,
                  })
                : null);
    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }

  static Future<String> getLifestyleRewardDetail(
      String token, int lifestyleId) async {
    http.Response response = await httpClient.post(
      Uri.parse('${AppConfig.baseUrl}getLifestyleRewardDetail'),
      headers: {
        HttpHeaders.contentTypeHeader: 'application/json',
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.authorizationHeader: 'Bearer $token',
      },
      body: jsonEncode({
        'lifestyleId': lifestyleId,
      }),
    );

    if (isValidResponseCode(response.statusCode)) {
      var jsonString = response.body;
      return jsonString;
    } else {
      throw Exception("Something wrong with the server.");
    }
  }
}
