import 'package:firebase_analytics/firebase_analytics.dart';

class AnalyticsService {
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Screen tracking
  Future<void> logScreenView(
      {required String screenName, String? screenClass}) async {
    await _analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClass,
    );
  }

  // User properties
  Future<void> setUserProperties({
    required String userId,
    String? userRole,
    String? country,
  }) async {
    await _analytics.setUserId(id: userId);

    if (userRole != null) {
      await _analytics.setUserProperty(name: 'user_role', value: userRole);
    }

    if (country != null) {
      await _analytics.setUserProperty(name: 'country', value: country);
    }
  }

  // Event tracking - Authentication
  Future<void> logLogin({String? method}) async {
    await _analytics.logLogin(loginMethod: method ?? 'email');
  }

  Future<void> logSignUp({String? method}) async {
    await _analytics.logSignUp(signUpMethod: method ?? 'email');
  }

  // Event tracking - E-commerce
  Future<void> logPurchase({
    required String transactionId,
    required double value,
    String? currency,
    String? itemId,
    String? itemName,
  }) async {
    await _analytics.logPurchase(
      currency: currency ?? 'USD',
      value: value,
      transactionId: transactionId,
      items: itemId != null && itemName != null
          ? [
              AnalyticsEventItem(
                itemId: itemId,
                itemName: itemName,
              ),
            ]
          : null,
    );
  }

  // Event tracking - Content
  Future<void> logSelectContent({
    required String contentType,
    required String itemId,
  }) async {
    await _analytics.logSelectContent(
      contentType: contentType,
      itemId: itemId,
    );
  }

  // Custom events
  Future<void> logCustomEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logEvent(
      name: name,
      parameters: parameters,
    );
  }

  // Specific app events
  Future<void> logCheckIn({required String eventId}) async {
    await _analytics.logEvent(
      name: 'check_in',
      parameters: {'event_id': eventId},
    );
  }

  Future<void> logAddVehicle({required String vehicleType}) async {
    await _analytics.logEvent(
      name: 'add_vehicle',
      parameters: {'vehicle_type': vehicleType},
    );
  }

  Future<void> logJoinWaitingList({required String eventId}) async {
    await _analytics.logEvent(
      name: 'join_waiting_list',
      parameters: {'event_id': eventId},
    );
  }

  // Booking related events
  Future<void> logBookingStarted({
    required String eventId,
    String? eventName,
    String? eventType,
  }) async {
    await _analytics.logEvent(
      name: 'booking_started',
      parameters: {
        'event_id': eventId,
        if (eventName != null) 'event_name': eventName,
        if (eventType != null) 'event_type': eventType,
      },
    );
  }

  Future<void> logBookingCompleted({
    required String eventId,
    required String bookingId,
    double? amount,
    String? currency,
  }) async {
    await _analytics.logEvent(
      name: 'booking_completed',
      parameters: {
        'event_id': eventId,
        'booking_id': bookingId,
        if (amount != null) 'amount': amount,
        if (currency != null) 'currency': currency,
      },
    );
  }

  // Store related events
  Future<void> logViewItem({
    required String itemId,
    required String itemName,
    String? category,
  }) async {
    await _analytics.logViewItem(
      items: [
        AnalyticsEventItem(
          itemId: itemId,
          itemName: itemName,
          itemCategory: category,
        ),
      ],
      currency: 'USD',
      value: 0.0,
    );
  }

  Future<void> logAddToCart({
    required String itemId,
    required String itemName,
    double? price,
    int quantity = 1,
  }) async {
    await _analytics.logAddToCart(
      items: [
        AnalyticsEventItem(
          itemId: itemId,
          itemName: itemName,
          quantity: quantity,
          price: price,
        ),
      ],
      currency: 'USD',
      value: price != null ? price * quantity : 0.0,
    );
  }
}
