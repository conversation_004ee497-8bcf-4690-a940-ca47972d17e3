import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class FirebaseAuthService {
  static logout() async {
    await GoogleSignIn().disconnect();
    await FirebaseAuth.instance.signOut();
  }

  static Future<String?> signInWithGoogle() async {
    // Trigger the authentication flow
    final GoogleSignInAccount? googleUser = await GoogleSignIn(
      scopes: ["profile", "email"],
    ).signIn();

    // Add null check for googleUser
    if (googleUser == null) {
      // User cancelled the sign-in or an error occurred
      throw Exception('Google Sign-In cancelled or failed.');
    }

    // Obtain the auth details from the request
    final GoogleSignInAuthentication googleAuth =
        await googleUser.authentication;

    // Create a new credential
    final credential = GoogleAuthProvider.credential(
      accessToken: googleAuth.accessToken,
      idToken: googleAuth.idToken,
    );

    // Once signed in, return the UserCredential
    var loginResult =
        await FirebaseAuth.instance.signInWithCredential(credential);

    return await loginResult.user?.getIdToken();
  }

  /// Generates a cryptographically secure random nonce, to be included in a
  /// credential request.
  static String generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)])
        .join();
  }

  /// Returns the sha256 hash of [input] in hex notation.
  static String sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  static Future<UserCredential> signInWithApple() async {
    try {
      debugPrint('[Apple Sign In] Starting sign in process');

      // To prevent replay attacks with the credential returned from Apple, we
      // include a nonce in the credential request. When signing in with
      // Firebase, the nonce in the id token returned by Apple, is expected to
      // match the sha256 hash of `rawNonce`.
      final rawNonce = generateNonce();
      final nonce = sha256ofString(rawNonce);
      debugPrint('[Apple Sign In] Generated nonce');

      // Request credential for the currently signed in Apple account.
      debugPrint('[Apple Sign In] Requesting Apple credentials...');
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );
      debugPrint(
          '[Apple Sign In] Received credentials - Identity Token exists: ${appleCredential.identityToken != null}');
      debugPrint('[Apple Sign In] User ID: ${appleCredential.userIdentifier}');
      debugPrint('[Apple Sign In] Email: ${appleCredential.email}');
      debugPrint(
          '[Apple Sign In] Full name: ${appleCredential.givenName} ${appleCredential.familyName}');

      if (appleCredential.identityToken == null) {
        throw Exception('Identity Token is null');
      }

      // Create an `OAuthCredential` from the credential returned by Apple.
      debugPrint('[Apple Sign In] Creating OAuth credential');
      // final oauthCredential = OAuthProvider("apple.com").credential(
      //   idToken: appleCredential.identityToken,
      //   rawNonce: rawNonce,
      // );
      final oauthCredential = AppleAuthProvider.credentialWithIDToken(
        appleCredential.identityToken ?? '',
        rawNonce,
        AppleFullPersonName(
          givenName: appleCredential.givenName ?? '',
          familyName: appleCredential.familyName ?? '',
        ),
      );

      // Sign in the user with Firebase. If the nonce we generated earlier does
      // not match the nonce in `appleCredential.identityToken`, sign in will fail.
      debugPrint('[Apple Sign In] Attempting Firebase sign in');
      var loginResult =
          await FirebaseAuth.instance.signInWithCredential(oauthCredential);
      debugPrint(
          '[Apple Sign In] Firebase sign in successful - User ID: ${loginResult.user?.uid}');

      return loginResult;
    } catch (e, stackTrace) {
      debugPrint('[Apple Sign In] Error during sign in: $e');
      debugPrint('[Apple Sign In] Stack trace: $stackTrace');
      rethrow;
    }
  }
}
