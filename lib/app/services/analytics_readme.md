# Firebase Analytics Implementation Guide

This document provides guidelines for implementing Firebase Analytics throughout the Automoment app.

## Overview

We've implemented a centralized analytics service to standardize tracking across the app. This service provides methods for:

1. Screen tracking
2. User property tracking
3. Standard events (login, signup, purchases)
4. Custom events

## How to Use Analytics in Your Controllers

### 1. Extend BaseScreenController

All controllers that need analytics should extend `BaseScreenController` instead of `GetxController`:

```dart
import '../../../controllers/base_screen_controller.dart';

class YourController extends BaseScreenController {
  // Your controller code
}
```

### 2. Set Screen Name

Override the `screenName` getter to provide the screen name:

```dart
@override
String get screenName => 'Your Screen Name';
```

This will automatically log the screen view when the controller's `onReady()` method is called. The `BaseScreenController` handles this for you.

### 3. Track Events

Use the `analytics` property to access the analytics service:

```dart
// Track a login event
await analytics.logLogin(method: 'email');

// Track a custom event
await analytics.logCustomEvent(
  name: 'button_clicked',
  parameters: {
    'button_name': 'submit',
    'screen': 'profile',
  },
);
```

### 4. Set User Properties

Set user properties when users log in or update their profile:

```dart
await analytics.setUserProperties(
  userId: user.id.toString(),
  country: user.country,
  userRole: user.role,
);
```

## Event Naming Conventions

Follow these conventions for event names:

1. Use snake_case (e.g., `button_clicked`, `form_submitted`)
2. Group related events with prefixes (e.g., `auth_login`, `auth_signup`)
3. Be descriptive but concise
4. Avoid using dynamic values in event names (put those in parameters)

## Standard Events to Track

### Authentication
- Login (`logLogin`)
- Signup (`logSignUp`)
- Password reset

### Navigation
- Screen views (automatic with `BaseScreenController`)
- Tab changes
- Menu selections

### Content
- View content details (`logSelectContent`)
- Share content

### E-commerce
- View item (`logViewItem`)
- Add to cart (`logAddToCart`)
- Begin checkout
- Purchase (`logPurchase`)

### App-specific
- Booking started/completed
- Vehicle added
- Join event
- Check-in

## Parameters to Include

Include these parameters when relevant:

1. IDs (user_id, event_id, item_id)
2. Names (event_name, item_name)
3. Types (event_type, vehicle_type)
4. Values (price, quantity)
5. Sources (where the action originated)

## Testing Analytics

To verify your analytics implementation:

1. Use the Firebase Debug View in the Firebase console
2. Check the DebugView tab in the Firebase Analytics console
3. Filter by your test device

## Best Practices

1. Don't over-track - focus on meaningful events
2. Be consistent with naming
3. Document new events you add
4. Group related events
5. Include relevant parameters
6. Don't include PII (Personally Identifiable Information) in event parameters

## Need Help?

If you need help implementing analytics in your feature, refer to the examples in:
- `login_controller.dart`
- `booking_controller.dart`