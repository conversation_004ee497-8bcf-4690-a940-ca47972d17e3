import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../modules/bottombar/controllers/bottombar_controller.dart';
import '../routes/app_pages.dart';

/// Enum representing the different types of deep link destinations
enum DeepLinkType {
  // checking: run, terminated, back / bottombar / navbar
  event, // ok, ok, ok
  news, // ok, ok, ok
  lifestyle, // ok, ok, ok
  prodDetail, // ok, ok
  car, // ok, ok
  leaderboardDetail, // ok, cold start back problem
  leaderboardAllTime, // ok, ok
  leaderboardThisYear, // ok
  upcomingEvents, // ok, ok
  latestNews, // ok, ok
  account, // ok, ok
  reward, // ok, ok
  store,
  unknown
}

/// Model class for deep link data
class DeepLinkData {
  final DeepLinkType type;
  final int? id;
  final String? viewType;

  const DeepLinkData({
    required this.type,
    this.id,
    this.viewType,
  });

  @override
  String toString() =>
      'DeepLinkData(type: $type, id: $id, viewType: $viewType)';
}

class DeepLinkService extends GetxService {
  final AppLinks _appLinks = AppLinks();
  final StreamController<String> _deepLinkStreamController =
      StreamController<String>.broadcast();
  final storage = GetStorage();
  static const String deepLinkKey = 'deep_link_key';

  // Mapping from deep link type to storage key for pending IDs
  static const Map<DeepLinkType, String> _pendingIdKeys = {
    DeepLinkType.event: 'pending_event_id',
    DeepLinkType.news: 'pending_news_id',
    DeepLinkType.lifestyle: 'pending_lifestyle_id',
    DeepLinkType.prodDetail: 'pending_prod_detail_id',
    DeepLinkType.car: 'pending_car_id',
    DeepLinkType.leaderboardDetail: 'pending_leaderboard_detail_id',
  };

  // Mapping from deep link type to route for navigation
  static const Map<DeepLinkType, String> _routeMap = {
    DeepLinkType.event: Routes.EVENTS_DETAIL,
    DeepLinkType.news: Routes.HOME_DETAIL,
    DeepLinkType.lifestyle: Routes.REWARD_LIFESTYLE_DETAIL,
    DeepLinkType.prodDetail: Routes.PRODUCT_DETAIL,
    DeepLinkType.car: Routes.CAR_LISTING_DETAIL,
    DeepLinkType.leaderboardDetail: Routes.LEADERBOARD_DETAIL,
  };

  // Tab indices for navigation
  static const Map<DeepLinkType, int> _tabIndices = {
    DeepLinkType.upcomingEvents: 2,
    DeepLinkType.latestNews: 1,
    DeepLinkType.account: 6,
    DeepLinkType.reward: 4,
    DeepLinkType.leaderboardAllTime: 3,
    DeepLinkType.leaderboardThisYear: 3,
    DeepLinkType.store: 5
  };

  // Keys for tab navigation flags
  static const Map<DeepLinkType, String> _tabNavigationKeys = {
    DeepLinkType.upcomingEvents: 'navigate_to_upcoming_events_tab',
    DeepLinkType.latestNews: 'navigate_to_latest_news_tab',
    DeepLinkType.account: 'navigate_to_account_tab',
    DeepLinkType.reward: 'navigate_to_reward_tab',
    DeepLinkType.leaderboardAllTime: 'navigate_to_leaderboard_all_time_tab',
    DeepLinkType.leaderboardThisYear: 'navigate_to_leaderboard_this_year_tab',
    DeepLinkType.store: 'navigate_to_store_tab'
  };

  // Stream that other parts of the app can listen to for deep link events
  Stream<String> get deepLinkStream => _deepLinkStreamController.stream;

  // Store the latest link for cold starts
  String? _latestLink;
  String? get latestLink => _latestLink;

  // Flag to indicate if we're handling a cold start deep link
  bool _hasPendingDeepLink = false;
  bool get hasPendingDeepLink => _hasPendingDeepLink;

  // Get the pending event ID if one exists
  int? getPendingEventId() {
    return storage.read<int>(_pendingIdKeys[DeepLinkType.event]!);
  }

  Future<DeepLinkService> init() async {
    debugPrint('DeepLinkService: Initializing');

    // Handle app links while the app is already started
    _appLinks.uriLinkStream.listen((uri) {
      debugPrint(
          'DeepLinkService: Received link from stream: ${uri.toString()}');
      _handleDeepLink(uri.toString(), false);
    });

    // Get the latest link that launched the app (cold start)
    try {
      debugPrint('DeepLinkService: Checking for initial link (cold start)');
      final appLink = await _appLinks.getInitialLink();
      debugPrint('DeepLinkService: Initial link result: $appLink');

      if (appLink != null) {
        _latestLink = appLink.toString();
        _hasPendingDeepLink = true;
        debugPrint('DeepLinkService: Got initial link: $_latestLink');

        // Store the deep link for later processing
        storage.write(deepLinkKey, _latestLink);
        debugPrint('DeepLinkService: Stored deep link for cold start handling');
      } else {
        // Check if we have a stored deep link from a previous cold start
        final storedLink = storage.read<String>(deepLinkKey);
        if (storedLink != null) {
          debugPrint('DeepLinkService: Found stored deep link: $storedLink');
          _latestLink = storedLink;
          _hasPendingDeepLink = true;
        } else {
          debugPrint('DeepLinkService: No initial link found');
        }
      }
    } catch (e) {
      debugPrint('DeepLinkService: Error getting initial app link: $e');
    }

    return this;
  }

  void _handleDeepLink(String link, bool isColdStart) {
    debugPrint('Deep link received: $link, isColdStart: $isColdStart');
    _latestLink = link;
    _deepLinkStreamController.add(link);

    // For cold start, just store the link and handle later
    if (isColdStart) {
      storage.write(deepLinkKey, link);
      _hasPendingDeepLink = true;
      debugPrint('Stored deep link for processing after app initialization');
    } else {
      // Parse the link and navigate accordingly
      final deepLinkData = _parseDeepLinkData(link);
      _navigateBasedOnDeepLinkData(deepLinkData, false);
    }
  }

  // Parse deep link into structured data
  DeepLinkData _parseDeepLinkData(String link) {
    // Example: https://automoment.com.sg/event/123?t=1746521084048
    // or automoment://event/123

    final uri = Uri.parse(link);
    final pathSegments = uri.pathSegments;

    if (pathSegments.isEmpty) {
      debugPrint('No path segments found in link: $link');
      return const DeepLinkData(type: DeepLinkType.unknown);
    }

    debugPrint('Path segments: $pathSegments');

    // Handle detail pages with IDs
    if (pathSegments.length >= 2) {
      final type = pathSegments[0].trim();
      final idStr = pathSegments[1].trim();

      if (idStr.isNotEmpty) {
        try {
          final id = int.parse(idStr);

          switch (type) {
            case 'event':
              return DeepLinkData(type: DeepLinkType.event, id: id);
            case 'news':
              return DeepLinkData(type: DeepLinkType.news, id: id);
            case 'lifestyle':
              return DeepLinkData(type: DeepLinkType.lifestyle, id: id);
            case 'prod-detail':
              return DeepLinkData(type: DeepLinkType.prodDetail, id: id);
            case 'car':
              return DeepLinkData(type: DeepLinkType.car, id: id);
            case 'lb-detail':
              return DeepLinkData(type: DeepLinkType.leaderboardDetail, id: id);
          }
        } catch (e) {
          debugPrint('Error parsing ID: $e');
        }
      }
    }

    // Handle tab navigations
    final firstSegment = pathSegments[0];
    switch (firstSegment) {
      case 'leaderboard-all-time':
        return const DeepLinkData(
            type: DeepLinkType.leaderboardAllTime, viewType: 'all_time');
      case 'leaderboard-this-year':
        return const DeepLinkData(
            type: DeepLinkType.leaderboardThisYear, viewType: 'this_year');
      case 'upcoming-events':
        return const DeepLinkData(type: DeepLinkType.upcomingEvents);
      case 'latest-news':
        return const DeepLinkData(type: DeepLinkType.latestNews);
      case 'account':
        return const DeepLinkData(type: DeepLinkType.account);
      case 'reward':
        return const DeepLinkData(type: DeepLinkType.reward);
      case 'storehome':
        return const DeepLinkData(type: DeepLinkType.store);
      default:
        return const DeepLinkData(type: DeepLinkType.latestNews);
    }
  }

  // Generic method to navigate based on deep link data
  void _navigateBasedOnDeepLinkData(DeepLinkData data, bool isColdStart) {
    debugPrint(
        'Navigating based on deep link data: $data, isColdStart: $isColdStart');

    // Handle detail pages with IDs
    if (data.id != null && _routeMap.containsKey(data.type)) {
      if (isColdStart) {
        // Store ID for later processing
        final key = _pendingIdKeys[data.type]!;
        storage.write(key, data.id);
        debugPrint(
            'Stored ${data.type} ID ${data.id} for processing after app initialization');
      } else {
        _navigateToDetailPage(data.type, data.id!);
      }
      return;
    }

    // Handle tab navigations
    if (_tabIndices.containsKey(data.type)) {
      _navigateToTab(data.type, data.viewType, isColdStart);
    }
  }

  // Generic method to navigate to tabs
  void _navigateToTab(DeepLinkType type, String? viewType, bool isColdStart) {
    final storageKey = 'navigate_to_${type.toString().split('.').last}_tab';

    debugPrint(
        'Handling navigation to ${type.toString().split('.').last} tab, isColdStart: $isColdStart');

    if (isColdStart) {
      // For cold start, store a flag to indicate we should navigate to the tab
      storage.write(storageKey, true);

      // Store view type if provided (e.g., for leaderboard)
      if (viewType != null) {
        storage.write('${type.toString().split('.').last}_view_type', viewType);
      }

      debugPrint(
          'Stored flag to navigate to ${type.toString().split('.').last} tab after app initialization');
    } else {
      // If app is already running, navigate to tab directly
      _directNavigateToTab(type, viewType);
    }
  }

  // Helper method to set view type preferences (e.g., for leaderboard)
  // This is called from _navigateToTabAfterBottomBar when a specific view is needed
  void _setViewTypePreference(DeepLinkType type, String? viewType) {
    if (viewType == null) return;

    final typeStr = type.toString().split('.').last;
    try {
      debugPrint('Setting $typeStr view type to: $viewType');
      storage.write('preferred_${typeStr}_view', viewType);
    } catch (e) {
      debugPrint('Error setting $typeStr view type: $e');
    }
  }

  // Direct navigation with more reliable approach
  void _directNavigateToTab(DeepLinkType type, String? viewType) {
    final tabIndex = _tabIndices[type]!;
    final route = _routeMap[type] ?? Routes.BOTTOMBAR;

    debugPrint('Direct navigation to $route with tabIndex: $tabIndex');

    // Use a longer delay to ensure the app is fully initialized
    Future.delayed(const Duration(seconds: 3), () {
      try {
        // Make sure we're on the bottom bar first
        if (Get.currentRoute != Routes.BOTTOMBAR) {
          debugPrint('Current route is not BOTTOMBAR, navigating there first');
          Get.offAllNamed(Routes.BOTTOMBAR);

          // Add extra delay to ensure the bottom bar is loaded
          Future.delayed(const Duration(seconds: 2), () {
            _navigateToTabAfterBottomBar(type, tabIndex, viewType);
          });
        } else {
          _navigateToTabAfterBottomBar(type, tabIndex, viewType);
        }
      } catch (e) {
        debugPrint('Error during direct navigation to tab: $e');
        // Store the tab index in a key that BottombarController will check
        storage.write('initial_tab_index', tabIndex);
      }
    });
  }

  // Helper method to navigate to a tab after ensuring we're on the bottom bar
  void _navigateToTabAfterBottomBar(
      DeepLinkType type, int tabIndex, String? viewType) {
    try {
      // Use Get.put to find or create the controller as needed
      final bottombarController = Get.find<BottombarController>();
      bottombarController.changePage(tabIndex);
      debugPrint(
          'Changed to ${type.toString().split('.').last} tab index $tabIndex');

      // Handle view type if provided (e.g., for leaderboard)
      if (viewType != null) {
        _setViewTypePreference(type, viewType);
      }

      // Clear the stored flags
      final storageKey = 'navigate_to_${type.toString().split('.').last}_tab';
      storage.remove(storageKey);

      if (viewType != null) {
        storage.remove('${type.toString().split('.').last}_view_type');
      }
    } catch (e) {
      debugPrint(
          'Error during navigation to ${type.toString().split('.').last} tab: $e');
    }
  }

  // Generic method to navigate to detail pages
  void _navigateToDetailPage(DeepLinkType type, int id) {
    final route = _routeMap[type]!;
    final key = _pendingIdKeys[type]!;

    debugPrint('Attempting to navigate to $route with ID: $id');

    // Use Future.delayed to ensure navigation happens after the current frame
    Future.delayed(const Duration(seconds: 1), () {
      try {
        // Format the argument appropriately based on type
        dynamic arguments;

        // Different types might need different argument formats
        switch (type) {
          case DeepLinkType.event:
          case DeepLinkType.news:
          case DeepLinkType.lifestyle:
          case DeepLinkType.leaderboardDetail:
          case DeepLinkType.prodDetail: // Add prodDetail to use JSON format
            arguments = '{"id": $id}';
            break;
          default:
            arguments = id;
            break;
        }

        debugPrint('Navigating to $route with arguments: $arguments');
        Get.toNamed(route, arguments: arguments);

        // Clear the stored deep link after successful navigation
        storage.remove(deepLinkKey);
        storage.remove(key);
        _hasPendingDeepLink = false;
        debugPrint(
            'Navigation completed successfully and cleared stored deep link');
      } catch (e) {
        debugPrint('Error during navigation: $e');
        // As a fallback, store the ID for the BottombarController to handle
        storage.write('initial_${type.toString().split('.').last}_id', id);
      }
    });
  }

  // Direct navigation with more reliable approach
  void _directNavigateToDetailPage(DeepLinkType type, int id) {
    final route = _routeMap[type]!;
    debugPrint('Direct navigation to $route with ID: $id');

    // Use a longer delay to ensure the app is fully initialized
    Future.delayed(const Duration(seconds: 3), () {
      try {
        // Make sure we're on the bottom bar first
        if (Get.currentRoute != Routes.BOTTOMBAR) {
          debugPrint('Current route is not BOTTOMBAR, navigating there first');
          Get.offAllNamed(Routes.BOTTOMBAR);

          // Add extra delay to ensure the bottom bar is loaded
          Future.delayed(const Duration(seconds: 2), () {
            _navigateToDetailPageAfterBottomBar(type, id);
          });
        } else {
          _navigateToDetailPageAfterBottomBar(type, id);
        }
      } catch (e) {
        debugPrint('Error during direct navigation: $e');
        // Store the ID in a key that BottombarController will check
        storage.write('initial_${type.toString().split('.').last}_id', id);
      }
    });
  }

  // Helper method for navigation after ensuring we're on the bottom bar
  void _navigateToDetailPageAfterBottomBar(DeepLinkType type, int id) {
    final route = _routeMap[type]!;
    final key = _pendingIdKeys[type]!;

    try {
      // Format the argument appropriately based on type
      dynamic arguments;

      // Different types might need different argument formats
      switch (type) {
        case DeepLinkType.event:
        case DeepLinkType.news:
        case DeepLinkType.lifestyle:
        case DeepLinkType.leaderboardDetail:
        case DeepLinkType.prodDetail: // Add prodDetail to use JSON format
          arguments = '{"id": $id}';
          break;
        default:
          arguments = id;
          break;
      }

      debugPrint('Navigating to $route with arguments: $arguments');
      Get.toNamed(route, arguments: arguments);

      // Clear the stored deep link after successful navigation
      storage.remove(deepLinkKey);
      storage.remove(key);
      _hasPendingDeepLink = false;
      debugPrint('Direct navigation completed successfully');
    } catch (e) {
      debugPrint('Error during navigation after bottom bar: $e');
      // As a fallback, store the ID for the BottombarController to handle
      storage.write('initial_${type.toString().split('.').last}_id', id);
    }
  }

  // Method to process any pending deep links after the app is fully initialized
  void processPendingDeepLinks() {
    debugPrint(
        'Processing pending deep links, hasPendingDeepLink: $_hasPendingDeepLink');
    if (!_hasPendingDeepLink) return;

    // Check for pending detail page IDs
    for (final entry in _pendingIdKeys.entries) {
      final type = entry.key;
      final key = entry.value;
      final pendingId = storage.read<int>(key);

      if (pendingId != null) {
        debugPrint(
            'Found pending ${type.toString().split('.').last} ID: $pendingId');
        _directNavigateToDetailPage(type, pendingId);
        return;
      }
    }

    // Check for tab navigation flags
    for (final entry in _tabNavigationKeys.entries) {
      final type = entry.key;
      final key = entry.value;
      final navigateToTab = storage.read<bool>(key);

      if (navigateToTab == true) {
        debugPrint(
            'Found flag to navigate to ${type.toString().split('.').last} tab');
        String? viewType;

        // For leaderboard tabs, also get the view type
        if (type == DeepLinkType.leaderboardAllTime ||
            type == DeepLinkType.leaderboardThisYear) {
          viewType = storage.read<String>('leaderboard_view_type');
        }

        _directNavigateToTab(type, viewType);
        return;
      }
    }

    // If no specific IDs or flags, try to process the latest link
    if (_latestLink != null) {
      debugPrint('Processing stored deep link: $_latestLink');
      // Parse the link directly
      final deepLinkData = _parseDeepLinkData(_latestLink!);
      _navigateBasedOnDeepLinkData(deepLinkData, false);
    }
  }

  @override
  void onClose() {
    _deepLinkStreamController.close();
    super.onClose();
  }

  // Detail navigation methods have been refactored to use the generic methods:
  // - _navigateToDetailPage()
  // - _directNavigateToDetailPage()
  // - _navigateToDetailPageAfterBottomBar()

  // Tab navigation methods have been refactored to use the generic methods:
  // - _navigateToTab()
  // - _directNavigateToTab()
  // - _navigateToTabAfterBottomBar()

  // All detail navigation methods have been refactored to use the generic methods:
  // - _navigateToDetailPage()
  // - _directNavigateToDetailPage()
  // - _navigateToDetailPageAfterBottomBar()

  /**
   * REFACTORING SUMMARY
   * 
   * All the specific navigation methods have been refactored into generic methods:
   * 
   * 1. Detail page navigation:
   *    - _navigateToDetailPage(DeepLinkType type, int id)
   *    - _directNavigateToDetailPage(DeepLinkType type, int id)
   *    - _navigateToDetailPageAfterBottomBar(DeepLinkType type, int id)
   * 
   * 2. Tab navigation:
   *    - _navigateToTab(DeepLinkType type, String? viewType, bool isColdStart)
   *    - _directNavigateToTab(DeepLinkType type, String? viewType)
   *    - _navigateToTabAfterBottomBar(DeepLinkType type, int tabIndex, String? viewType)
   * 
   * The redundant methods have been removed to improve code maintainability.
   * 
   * If you need to add a new deep link type:
   * 1. Add it to the DeepLinkType enum
   * 2. Add appropriate entries to _pendingIdKeys, _routeMap, and _tabIndices maps
   * 3. Update _parseDeepLinkData() to handle the new type
   */
}
