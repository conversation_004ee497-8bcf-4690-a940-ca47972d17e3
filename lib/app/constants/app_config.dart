import 'package:flutter/material.dart';
import 'package:pretty_http_logger/pretty_http_logger.dart';

class AppConfig {
  static bool isProduction = false;
  static bool enableHttpLogging = true;

  static LogLevel httpLogLevel = LogLevel.BODY;

  static String get webUrl => isProduction
      ? "https://app.automoment.com.sg/"
      : "https://testapp.automoment.com.sg/";

  static String get firebaseDatabaseUrl => isProduction
      ? "https://automoment-default-rtdb.asia-southeast1.firebasedatabase.app"
      : "https://automoment-dev.asia-southeast1.firebasedatabase.app";

  static String get stripePublishableKey => isProduction
      ? "pk_live_51MRtPpC5kOdFxJkMO2q8TQY41cn7QLFqi4Er3X5D75oi27yFplhHiXN6PhxNaoPQgc8xofNQCouKyN1gm25M2OmR00iU7tGVUJ"
      : "pk_test_51MRtPpC5kOdFxJkMCDsKzW3fCTq8AOXf80IR4wNX4R13lh8MlOBjXPsjyVYMUXXNbR9a0v9nDxMIEy21qHm9XuR800RhRrjt81";

  static String storageUrl =
      "http://d293g79pe3sczv.cloudfront.net/"; //"${webUrl}storage/";
  static String baseUrl = "${webUrl}api/";
  static const defaultPadding = 10.0;
  static String genericDevice = "Generic";

  static const kShadow = [
    BoxShadow(
        offset: Offset(0, -6),
        blurRadius: 6,
        spreadRadius: -4.0,
        color: Colors.black26)
  ];

  static String stripeMerchantIdentifier = 'Automoment';
  static bool googlePayTestEnvironment = false;
}
