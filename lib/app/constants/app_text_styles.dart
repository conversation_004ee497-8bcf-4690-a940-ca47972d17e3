import 'package:flutter/material.dart';

import 'app_color.dart';

class AppTextStyles {
  static const titleText = TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.bold);
  static const smallTitleBold = TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold);
  static const bigHeaderText = TextStyle(color: Colors.black, fontSize: 30,fontWeight: FontWeight.bold);
  static const normalText = TextStyle(color: Colors.black, fontSize: 14,fontWeight: FontWeight.normal);
  static const normalTextBold = TextStyle(color: Colors.black, fontSize: 14,fontWeight: FontWeight.bold);
  static const smallText = TextStyle(color: Colors.white, fontSize: 13);

  static const ticketFieldHeader = TextStyle(
      fontSize: 17,
      color: AppColor.kTextGrayColor,
      fontStyle: FontStyle.normal,
      fontWeight: FontWeight.bold
  );

  static const ticketFieldValue = TextStyle(
      fontSize: 17,
      color: AppColor.kTextColor,
      fontStyle: FontStyle.normal,
      fontWeight: FontWeight.bold
  );

}

