// To parse this JSON data, do
//
//     final couponReward = couponRewardFromJson(jsonString);

import 'dart:convert';

CouponReward couponRewardFromJson(String str) => CouponReward.fromJson(json.decode(str));

String couponRewardToJson(CouponReward data) => json.encode(data.toJson());

class CouponReward {
  int? id;
  String? code;
  String? type;
  String? value;
  int? valueMyr;
  int? limitUsage;
  int? remainingUsage;
  String? description;
  DateTime? startDate;
  DateTime? endDate;
  bool? allEvents;
  int? userId;
  bool? isUsed;
  DateTime? usedDate;

  CouponReward({
    this.id,
    this.code,
    this.type,
    this.value,
    this.valueMyr,
    this.limitUsage,
    this.remainingUsage,
    this.description,
    this.startDate,
    this.endDate,
    this.allEvents,
    this.userId,
    this.isUsed,
    this.usedDate,
  });

  factory CouponReward.fromJson(Map<String, dynamic> json) => CouponReward(
    id: json["id"],
    code: json["code"],
    type: json["type"],
    value: json["value"],
    valueMyr: json["value_myr"],
    limitUsage: json["limit_usage"],
    remainingUsage: json["remaining_usage"],
    description: json["description"],
    startDate: json["start_date"] == null ? null : DateTime.parse(json["start_date"]),
    endDate: json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
    allEvents: json["all_events"],
    userId: json["user_id"],
    isUsed: json["is_used"],
    usedDate: json["used_date"] == null ? null : DateTime.parse(json["used_date"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "code": code,
    "type": type,
    "value": value,
    "value_myr": valueMyr,
    "limit_usage": limitUsage,
    "remaining_usage": remainingUsage,
    "description": description,
    "start_date": startDate?.toIso8601String(),
    "end_date": endDate?.toIso8601String(),
    "all_events": allEvents,
    "user_id": userId,
    "is_used": isUsed,
    "used_date": usedDate?.toIso8601String(),
  };
}
