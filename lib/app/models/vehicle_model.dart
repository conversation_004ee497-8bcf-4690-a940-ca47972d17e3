class Vehicle {
  int? id;
  String? make;
  String? model;
  String? year;
  String? type;
  String? image;
  String? plateNumber;
  List<int>? events;

  Vehicle({this.id, this.make, this.model, this.year, this.type, this.image, this.plateNumber, this.events});

  Vehicle.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    make = json['make'];
    model = json['model'];
    year = json['year'];
    type = json['type'];
    image = json['image'];
    plateNumber = json['plate_number'];
    events = json['events'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['make'] = make;
    data['model'] = model;
    data['year'] = year;
    data['type'] = type;
    data['image'] = image;
    data['plate_number'] = plateNumber;
    data['events'] = events;
    return data;
  }
}
