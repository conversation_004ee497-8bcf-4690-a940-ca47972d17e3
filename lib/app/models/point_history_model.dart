// To parse this JSON data, do
//
//     final pointHistory = pointHistoryFromJson(jsonString);

import 'dart:convert';

PointHistory pointHistoryFromJson(String str) => PointHistory.fromJson(json.decode(str));

String pointHistoryToJson(PointHistory data) => json.encode(data.toJson());

class PointHistory {
  DateTime? createdAt;
  String? type;
  int? points;
  String? activity;

  PointHistory({
    this.createdAt,
    this.type,
    this.points,
    this.activity,
  });

  factory PointHistory.fromJson(Map<String, dynamic> json) => PointHistory(
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    type: json["type"],
    points: json["points"],
    activity: json["activity"],
  );

  Map<String, dynamic> toJson() => {
    "created_at": createdAt?.toIso8601String(),
    "type": type,
    "points": points,
    "activity": activity,
  };
}
