class ChatList {
  String? eventId;
  String? updatedAt;
  int? isOpen;
  int? isCs;
  String? name;
  String? image;
  String? createdAt;
  int? id;
  int? isEvent;
  int? isGroup;

  ChatList(
      {this.eventId,
        this.updatedAt,
        this.isOpen,
        this.isCs,
        this.name,
        this.image,
        this.createdAt,
        this.id,
        this.isEvent,
        this.isGroup});

  ChatList.fromJson(Map<dynamic, dynamic> json) {
    eventId = json['event_id'];
    updatedAt = json['updated_at'];
    isOpen = json['is_open'];
    isCs = json['is_cs'];
    name = json['name'];
    image = json['image'];
    createdAt = json['created_at'];
    id = json['id'];
    isEvent = json['is_event'];
    isGroup = json['is_group'];
  }

  Map<dynamic, dynamic> toJson() {
    final Map<dynamic, dynamic> data = <dynamic, dynamic>{};
    data['event_id'] = eventId;
    data['updated_at'] = updatedAt;
    data['is_open'] = isOpen;
    data['is_cs'] = isCs;
    data['name'] = name;
    data['image'] = image;
    data['created_at'] = createdAt;
    data['id'] = id;
    data['is_event'] = isEvent;
    data['is_group'] = isGroup;
    return data;
  }
}