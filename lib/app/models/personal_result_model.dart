class PersonalResult {
  PersonalResult({
    this.id,
    this.title,
    this.eventDate,
    this.detail,
    this.image,
    this.blogUrl,
    this.price,
    this.position,
    this.time,
    this.createdAt,
  });

  int? id;
  String? title;
  DateTime? eventDate;
  String? detail;
  String? image;
  String? blogUrl;
  double? price;
  int? position;
  String? time;
  DateTime? createdAt;

  factory PersonalResult.fromJson(Map<String, dynamic> json) => PersonalResult(
    id: json["id"],
    title: json["title"],
    eventDate: DateTime.parse(json["event_date"]),
    detail: json["detail"],
    image: json["image"],
    blogUrl: json["blog_url"] ?? "https://automoment.com.sg",
    price: json["price"].toDouble(),
    position: json["position"],
    time: json["time"],
    createdAt: DateTime.parse(json["created_at"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "title": title,
    "event_date": eventDate?.toIso8601String(),
    "detail": detail,
    "image": image,
    "blog_url": blogUrl,
    "price": price,
    "position": position,
    "time": time,
    "created_at": createdAt?.toIso8601String(),
  };
}