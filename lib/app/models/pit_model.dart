// class Pit {
//   int number;
//   List<bool> availability;
//
//   Pit({required this.number, required this.availability});
// }

import 'dart:convert';

Pit pitFromJson(String str) => Pit.fromJson(json.decode(str));

String pitToJson(Pit data) => json.encode(data.toJson());

class Pit {
  int? number;
  List<bool>? availability;

  Pit({
    this.number,
    this.availability,
  });

  factory Pit.fromJson(Map<String, dynamic> json) => Pit(

    number: int.parse(json["number"]),
    availability: json["availability"] == null ? [] : List<bool>.from(json["availability"]!.map((x) => x)),
  );

  Map<String, dynamic> toJson() => {
    "number": number,
    "availability": availability == null ? [] : List<dynamic>.from(availability!.map((x) => x)),
  };
}
