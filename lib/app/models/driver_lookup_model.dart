import 'dart:convert';

DriverLookup driverLookupFromJson(String str) => DriverLookup.fromJson(json.decode(str));

String driverLookupTo<PERSON>son(DriverLookup data) => json.encode(data.toJson());

class DriverLookup {
  bool? success;
  List<MainDriver>? mainDrivers;

  DriverLookup({
    this.success,
    this.mainDrivers,
  });

  factory DriverLookup.fromJson(Map<String, dynamic> json) => DriverLookup(
    success: json["success"],
    mainDrivers: json["mainDrivers"] == null ? [] : List<MainDriver>.from(json["mainDrivers"]!.map((x) => MainDriver.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "mainDrivers": mainDrivers == null ? [] : List<dynamic>.from(mainDrivers!.map((x) => x.toJson())),
  };
}

class MainDriver {
  int? id;
  String? name;
  String? email;
  String? photo;
  String? mobileNumber;
  List<SimpleVehicle>? vehicles;

  MainDriver({
    this.id,
    this.name,
    this.email,
    this.photo,
    this.mobileNumber,
    this.vehicles,
  });

  factory MainDriver.fromJson(Map<String, dynamic> json) => MainDriver(
    id: json["id"],
    name: json["name"],
    email: json["email"],
    photo: json["photo"],
    mobileNumber: json["mobile_number"],
    vehicles: json["vehicles"] == null ? [] : List<SimpleVehicle>.from(json["vehicles"]!.map((x) => SimpleVehicle.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "email": email,
    "photo": photo,
    "mobile_number": mobileNumber,
    "vehicles": vehicles == null ? [] : List<dynamic>.from(vehicles!.map((x) => x.toJson())),
  };
}

class SimpleVehicle {
  int? id;
  String? name;

  SimpleVehicle({
    this.id,
    this.name,
  });

  factory SimpleVehicle.fromJson(Map<String, dynamic> json) => SimpleVehicle(
    id: json["id"],
    name: json["name"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
  };
}