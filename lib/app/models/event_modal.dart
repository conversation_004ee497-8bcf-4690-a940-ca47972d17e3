import 'addon_model.dart';

class Event {
  Event({
    this.id,
    this.title,
    this.eventDate,
    this.detail,
    this.image,
    this.blogUrl,
    this.price,
    this.additionalDriver,
    this.additionalDriverPrice,
    this.firebaseDynamicLink,
    this.createdAt,
    this.isExternalEvent,
    this.isRequireVehiclePlateNumber,
    this.priceMyr,
    this.additionalDriverPriceMyr,
    this.defaultCurrency,
    this.organizer,
    this.venue,
    this.endTime,
    this.programs,
    this.addonsNotRequireBooking,
    this.passengerPrice,
    this.passengerPriceMyr,
    this.groupPrice,
    this.groupPriceMyr,
    this.group,
    this.passenger,
    this.isRequiredPitNumber,
    this.leaderboardShortUrl,
  });

  int? id;
  String? title;
  DateTime? eventDate;
  String? detail;
  String? image;
  String? blogUrl;
  double? price;
  bool? additionalDriver;
  double? additionalDriverPrice;
  String? firebaseDynamicLink;
  DateTime? createdAt;
  bool? isExternalEvent;
  bool? isRequireVehiclePlateNumber;
  double? priceMyr;
  double? additionalDriverPriceMyr;
  String? defaultCurrency;
  String? organizer;
  String? venue;
  DateTime? endTime;
  List<Program>? programs;
  List<Addon>? addonsNotRequireBooking;
  double? passengerPrice;
  double? passengerPriceMyr;
  double? groupPrice;
  double? groupPriceMyr;
  bool? group;
  bool? passenger;
  bool? isRequiredPitNumber;
  String? leaderboardShortUrl;

  factory Event.fromJson(Map<String, dynamic> json) {
    // Create a safe version of the fromJson method that handles missing fields
    // This is especially important for the leaderboard API which returns simplified event data
    try {
      return Event(
        id: json["id"],
        title: json["title"] ?? '',
        eventDate: json["event_date"] != null
            ? DateTime.parse(json["event_date"])
            : null,
        detail: json["detail"],
        image: json["image"],
        blogUrl: json["blog_url"] ?? "https://automoment.com.sg",
        // Handle all numeric fields safely
        price: _safeDouble(json, "price"),
        additionalDriver: json["additional_driver"] ?? false,
        additionalDriverPrice: _safeDouble(json, "additional_driver_price"),
        firebaseDynamicLink: json["firebase_dynamic_link"],
        createdAt: json["created_at"] != null
            ? DateTime.parse(json["created_at"])
            : null,
        isExternalEvent: json["is_external_event"] ?? false,
        isRequireVehiclePlateNumber:
            json["is_require_vehicle_plate_number"] ?? false,
        priceMyr: _safeDouble(json, "price_myr"),
        additionalDriverPriceMyr:
            _safeDouble(json, "additional_driver_price_myr"),
        defaultCurrency: json["default_currency"],
        organizer: json["organizer"],
        venue: json["venue"],
        endTime:
            json["end_time"] == null ? null : DateTime.parse(json["end_time"]),
        programs: json["programs"] == null
            ? []
            : List<Program>.from(
                json["programs"]!.map((x) => Program.fromJson(x))),
        addonsNotRequireBooking: json["addons_not_require_booking"] == null
            ? []
            : List<Addon>.from(json["addons_not_require_booking"]!
                .map((x) => Addon.fromJson(x))),
        passengerPrice: _safeDouble(json, "passenger_price"),
        passengerPriceMyr: _safeDouble(json, "passenger_price_myr"),
        groupPrice: _safeDouble(json, "group_price"),
        groupPriceMyr: _safeDouble(json, "group_price_myr"),
        group: json["group"] ?? false,
        passenger: json["passenger"] ?? false,
        isRequiredPitNumber: json["require_select_pit_no"] ?? false,
        leaderboardShortUrl: json["leaderboard_short_url"] ?? '',
      );
    } catch (e) {
      // Return a minimal valid Event object with just the essential fields
      return Event(
        id: json["id"] ?? 0,
        title: json["title"] ?? 'Unknown Event',
        eventDate: json["event_date"] != null
            ? DateTime.parse(json["event_date"])
            : null,
      );
    }
  }

  // Helper method to safely convert a field to double
  static double _safeDouble(Map<String, dynamic> json, String key) {
    if (json[key] == null) return 0.0;
    if (json[key] is double) return json[key];
    if (json[key] is int) return json[key].toDouble();
    if (json[key] is String) {
      try {
        return double.parse(json[key]);
      } catch (_) {
        return 0.0;
      }
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "event_date": eventDate?.toIso8601String(),
        "detail": detail,
        "image": image,
        "blog_url": blogUrl,
        "price": price,
        "additional_driver": additionalDriver,
        "additional_driver_price": additionalDriverPrice,
        "firebase_dynamic_link": firebaseDynamicLink,
        "created_at": createdAt?.toIso8601String(),
        "is_external_event": isExternalEvent,
        "is_require_vehicle_plate_number": isRequireVehiclePlateNumber,
        "price_myr": priceMyr,
        "additional_driver_price_myr": additionalDriverPriceMyr,
        'default_currency': defaultCurrency,
        'organizer': organizer,
        'venue': venue,
        'end_time': endTime?.toIso8601String(),
        "programs": programs == null
            ? []
            : List<dynamic>.from(programs!.map((x) => x.toJson())),
        "addons_not_require_booking": addonsNotRequireBooking == null
            ? []
            : List<dynamic>.from(
                addonsNotRequireBooking!.map((x) => x.toJson())),
        "passenger_price": passengerPrice,
        "passenger_price_myr": passengerPriceMyr,
        "group_price": groupPrice,
        "group_price_myr": groupPriceMyr,
        "group": group,
        "passenger": passenger,
        "require_select_pit_no": isRequiredPitNumber,
        "leaderboard_short_url": leaderboardShortUrl,
      };
}

class Program {
  int? id;
  String? title;
  String? time;
  String? description;

  Program({
    this.id,
    this.title,
    this.time,
    this.description,
  });

  factory Program.fromJson(Map<String, dynamic> json) => Program(
        id: json["id"],
        title: json["title"],
        time: json["time"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "time": time,
        "description": description,
      };
}
