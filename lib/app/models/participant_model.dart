class Participant {
  String? name;
  String? email;
  String? mobileNumber;
  String? vehicle;
  String? userPhoto;
  int? userId;
  int? vehicleId;

  Participant({this.name, this.email, this.mobileNumber, this.vehicle, this.userId, this.vehicleId, this.userPhoto});

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'mobileNumber': mobileNumber,
      'vehicle': vehicle,
      'userId': userId,
      'vehicleId': vehicleId,
    };
  }
}