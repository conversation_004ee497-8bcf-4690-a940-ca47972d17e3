import 'lap_performance_model.dart';

class PerformanceComparison {
  final List<PerformanceSummary> eventSummaries;
  final String trackName;
  final String driverName;
  final String vehicleInfo;

  // Calculated metrics
  final Map<String, dynamic> improvements;

  PerformanceComparison({
    required this.eventSummaries,
    required this.trackName,
    required this.driverName,
    required this.vehicleInfo,
  }) : improvements = _calculateImprovements(eventSummaries);

  static Map<String, dynamic> _calculateImprovements(
      List<PerformanceSummary> summaries) {
    if (summaries.length < 2) {
      return {
        'bestLapImprovement': 0.0,
        'avgLapImprovement': 0.0,
        'topSpeedImprovement': 0.0,
        'bestSectorsImprovement': {
          's1': 0.0,
          's2': 0.0,
          's3': 0.0,
          's4': 0.0,
        }
      };
    }

    // Sort summaries by date (assuming event.eventDate is in a consistent format)
    final sortedSummaries = List<PerformanceSummary>.from(summaries);
    sortedSummaries.sort((a, b) {
      if (a.event.eventDate == null || b.event.eventDate == null) {
        return 0;
      }
      return a.event.eventDate!.compareTo(b.event.eventDate!);
    });

    // Get first and last event for comparison
    final firstEvent = sortedSummaries.first;
    final lastEvent = sortedSummaries.last;

    // Calculate best lap improvement
    double bestLapImprovement = 0.0;
    if (firstEvent.bestLapTime != null && lastEvent.bestLapTime != null) {
      final firstBestLap = _convertLapTimeToSeconds(firstEvent.bestLapTime!);
      final lastBestLap = _convertLapTimeToSeconds(lastEvent.bestLapTime!);
      if (firstBestLap > 0 && lastBestLap > 0) {
        bestLapImprovement =
            ((firstBestLap - lastBestLap) / firstBestLap) * 100;
      }
    }

    // Calculate average lap improvement
    double avgLapImprovement = 0.0;
    if (firstEvent.averageLapTime != null && lastEvent.averageLapTime != null) {
      final firstAvgLap = _convertLapTimeToSeconds(firstEvent.averageLapTime!);
      final lastAvgLap = _convertLapTimeToSeconds(lastEvent.averageLapTime!);
      if (firstAvgLap > 0 && lastAvgLap > 0) {
        avgLapImprovement = ((firstAvgLap - lastAvgLap) / firstAvgLap) * 100;
      }
    }

    // Calculate top speed improvement
    double topSpeedImprovement = 0.0;
    if (firstEvent.topSpeed != null && lastEvent.topSpeed != null) {
      topSpeedImprovement = ((lastEvent.topSpeed! - firstEvent.topSpeed!) /
              firstEvent.topSpeed!) *
          100;
    }

    // Calculate sector improvements
    Map<String, double> sectorImprovements = {
      's1': 0.0,
      's2': 0.0,
      's3': 0.0,
      's4': 0.0,
    };

    // S1 improvement
    if (firstEvent.bestS1 != null && lastEvent.bestS1 != null) {
      final firstS1 = _convertSectorTimeToSeconds(firstEvent.bestS1!);
      final lastS1 = _convertSectorTimeToSeconds(lastEvent.bestS1!);
      if (firstS1 > 0 && lastS1 > 0) {
        sectorImprovements['s1'] = ((firstS1 - lastS1) / firstS1) * 100;
      }
    }

    // S2 improvement
    if (firstEvent.bestS2 != null && lastEvent.bestS2 != null) {
      final firstS2 = _convertSectorTimeToSeconds(firstEvent.bestS2!);
      final lastS2 = _convertSectorTimeToSeconds(lastEvent.bestS2!);
      if (firstS2 > 0 && lastS2 > 0) {
        sectorImprovements['s2'] = ((firstS2 - lastS2) / firstS2) * 100;
      }
    }

    // S3 improvement
    if (firstEvent.bestS3 != null && lastEvent.bestS3 != null) {
      final firstS3 = _convertSectorTimeToSeconds(firstEvent.bestS3!);
      final lastS3 = _convertSectorTimeToSeconds(lastEvent.bestS3!);
      if (firstS3 > 0 && lastS3 > 0) {
        sectorImprovements['s3'] = ((firstS3 - lastS3) / firstS3) * 100;
      }
    }

    // S4 improvement
    if (firstEvent.bestS4 != null && lastEvent.bestS4 != null) {
      final firstS4 = _convertSectorTimeToSeconds(firstEvent.bestS4!);
      final lastS4 = _convertSectorTimeToSeconds(lastEvent.bestS4!);
      if (firstS4 > 0 && lastS4 > 0) {
        sectorImprovements['s4'] = ((firstS4 - lastS4) / firstS4) * 100;
      }
    }

    return {
      'bestLapImprovement': bestLapImprovement,
      'avgLapImprovement': avgLapImprovement,
      'topSpeedImprovement': topSpeedImprovement,
      'bestSectorsImprovement': sectorImprovements,
    };
  }

  static double _convertLapTimeToSeconds(String lapTime) {
    try {
      // Format: "MM:SS.mmm" or "MM:SS:mmm"
      final parts = lapTime.replaceAll(':', '.').split('.');
      if (parts.length >= 2) {
        final minutes = int.parse(parts[0]);
        final seconds = double.parse(parts[1]);
        final milliseconds =
            parts.length > 2 ? double.parse(parts[2]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      }
    } catch (e) {
      print('Error parsing lap time: $e');
    }
    return 0;
  }

  static double _convertSectorTimeToSeconds(String sectorTime) {
    try {
      // Handle formats like "1:02.471" or "40.896"
      if (sectorTime.contains(':')) {
        final parts = sectorTime.split(':');
        final minutes = int.parse(parts[0]);
        final secondsPart = parts[1].split('.');
        final seconds = double.parse(secondsPart[0]);
        final milliseconds =
            secondsPart.length > 1 ? double.parse(secondsPart[1]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      } else {
        final parts = sectorTime.split('.');
        final seconds = double.parse(parts[0]);
        final milliseconds =
            parts.length > 1 ? double.parse(parts[1]) / 1000 : 0;
        return seconds + milliseconds;
      }
    } catch (e) {
      print('Error parsing sector time: $e');
    }
    return 0;
  }

  // Get the most improved sector
  String getMostImprovedSector() {
    final sectorImprovements =
        improvements['bestSectorsImprovement'] as Map<String, double>;
    String bestSector = 's1';
    double bestImprovement = sectorImprovements['s1'] ?? 0.0;

    sectorImprovements.forEach((sector, improvement) {
      if (improvement > bestImprovement) {
        bestImprovement = improvement;
        bestSector = sector;
      }
    });

    return bestSector;
  }

  // Get the most recent event
  PerformanceSummary get mostRecentEvent {
    if (eventSummaries.isEmpty) {
      throw Exception('No event summaries available');
    }

    final sortedSummaries = List<PerformanceSummary>.from(eventSummaries);
    sortedSummaries.sort((a, b) {
      if (a.event.eventDate == null || b.event.eventDate == null) {
        return 0;
      }
      return b.event.eventDate!.compareTo(a.event.eventDate!);
    });

    return sortedSummaries.first;
  }

  // Get the first event
  PerformanceSummary get firstEvent {
    if (eventSummaries.isEmpty) {
      throw Exception('No event summaries available');
    }

    final sortedSummaries = List<PerformanceSummary>.from(eventSummaries);
    sortedSummaries.sort((a, b) {
      if (a.event.eventDate == null || b.event.eventDate == null) {
        return 0;
      }
      return a.event.eventDate!.compareTo(b.event.eventDate!);
    });

    return sortedSummaries.first;
  }
}
