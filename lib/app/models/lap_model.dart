// To parse this JSON data, do
//
//     final lap = lapFromJson(jsonString);

import 'dart:convert';

Lap lapFromJson(String str) => Lap.fromJson(json.decode(str));

String lapToJson(Lap data) => json.encode(data.toJson());

class Lap {
  int? id;
  int? userId;
  int? eventId;
  int? lap;
  String? timeOfDay;
  String? lapTime;

  Lap({
    this.id,
    this.userId,
    this.eventId,
    this.lap,
    this.timeOfDay,
    this.lapTime,
  });

  factory Lap.fromJson(Map<String, dynamic> json) => Lap(
    id: json["id"],
    userId: json["user_id"],
    eventId: json["event_id"],
    lap: json["lap"],
    timeOfDay: json["time_of_day"],
    lapTime: json["lap_time"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "user_id": userId,
    "event_id": eventId,
    "lap": lap,
    "time_of_day": timeOfDay,
    "lap_time": lapTime,
  };
}
