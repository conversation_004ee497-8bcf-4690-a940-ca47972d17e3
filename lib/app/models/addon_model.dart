import 'dart:convert';

Addon addonFromJson(String str) => Addon.fromJson(json.decode(str));

String addonToJson(Addon data) => json.encode(data.toJson());

class Addon {
  int? id;
  String? name;
  bool? requireBooking;
  int? price;
  int? priceMyr;

  Addon({
    this.id,
    this.name,
    this.requireBooking,
    this.price,
    this.priceMyr,
  });

  factory Addon.fromJson(Map<String, dynamic> json) => Addon(
    id: json["id"],
    name: json["name"],
    requireBooking: json["require_booking"] == 1 ? true : false,
    price: json["price"],
    priceMyr: json["price_myr"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "require_booking": requireBooking,
    "price": price,
    "price_myr": priceMyr,
  };
}
