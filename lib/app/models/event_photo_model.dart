// To parse this JSON data, do
//
//     final eventPhoto = eventPhotoFromJson(jsonString);

import 'dart:convert';

EventPhoto eventPhotoFromJson(String str) => EventPhoto.fromJson(json.decode(str));

String eventPhotoToJson(EventPhoto data) => json.encode(data.toJson());

class EventPhoto {
  int? id;
  int? eventId;
  String? title;
  DateTime? eventDate;
  String? image;
  List<Photo>? photos;

  EventPhoto({
    this.id,
    this.eventId,
    this.title,
    this.eventDate,
    this.image,
    this.photos,
  });

  factory EventPhoto.fromJson(Map<String, dynamic> json) => EventPhoto(
    id: json["id"],
    eventId: json["event_id"],
    title: json["title"],
    eventDate: json["event_date"] == null ? null : DateTime.parse(json["event_date"]),
    image: json["image"],
    photos: json["photos"] == null ? [] : List<Photo>.from(json["photos"]!.map((x) => Photo.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "event_id": eventId,
    "title": title,
    "event_date": eventDate?.toIso8601String(),
    "image": image,
    "photos": photos == null ? [] : List<dynamic>.from(photos!.map((x) => x.toJson())),
  };
}

class Photo {
  int? id;
  String? thumbnail;
  String? photo;
  String? caption;
  int? eventId;
  String? eventName;

  Photo({
    this.id,
    this.thumbnail,
    this.photo,
    this.caption,
    this.eventId,
    this.eventName,
  });

  factory Photo.fromJson(Map<String, dynamic> json) => Photo(
    id: json["id"],
    thumbnail: json["thumbnail"],
    photo: json["photo"],
    caption: json["caption"],
    eventId: json["event_id"],
    eventName: json["event_name"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "thumbnail": thumbnail,
    "photo": photo,
    "caption": caption,
    "event_id": eventId,
    "event_name": eventName,
  };
}
