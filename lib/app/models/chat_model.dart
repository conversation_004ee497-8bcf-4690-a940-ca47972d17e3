class Chat {
  String? id;
  int? senderId;
  String? senderName;
  String? senderImageUrl;
  String? name;
  String? image;
  String? message;
  int? timestamp;
  bool? isMessageRead;

  bool? isImage;
  String? imageUrl;
  int? imageHeight;
  int? imageWidth;
  int? imageSize;
  String? imageFilename;

  bool? isFile;
  String? fileUrl;
  int? fileSize;
  String? fileFilename;
  String? fileStoragePath;

  Chat(
      {this.id,
        this.senderId,
        this.senderName,
        this.senderImageUrl,
        this.name,
        this.image,
        this.message,
        this.timestamp,
        this.isMessageRead,
        this.isImage,
        this.imageUrl,
        this.imageHeight,
        this.imageWidth,
        this.imageSize,
        this.imageFilename,
        this.isFile,
        this.fileUrl,
        this.fileSize,
        this.fileFilename,
        this.fileStoragePath
      });

  Chat.fromJson(Map<dynamic, dynamic> json) {
    id = json['id'];
    senderId = json['senderId'];
    senderName = json['senderName'];
    senderImageUrl = json['senderImageUrl'];
    name = json['name'];
    image = json['image'];
    message = json['message'];
    timestamp = json['timestamp'];
    isMessageRead = json['isMessageRead'];
    isImage = json['isImage'];
    imageUrl = json['imageUrl'];
    imageHeight = json['imageHeight'];
    imageWidth = json['imageWidth'];
    imageSize = json['imageSize'];
    imageFilename = json['imageFilename'];
    isFile = json['isFile'];
    fileUrl = json['fileUrl'];
    fileSize = json['fileSize'];
    fileFilename = json['fileFilename'];
    fileStoragePath = json['fileStoragePath'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['senderId'] = senderId;
    data['senderName'] = senderName;
    data['senderImageUrl'] = senderImageUrl;
    data['name'] = name;
    data['image'] = image;
    data['message'] = message;
    data['timestamp'] = timestamp;
    data['isMessageRead'] = isMessageRead;
    data['isImage'] = isImage;
    data['imageUrl'] = imageUrl;
    data['imageHeight'] = imageHeight;
    data['imageWidth'] = imageWidth;
    data['imageSize'] = imageSize;
    data['imageFilename'] = imageFilename;
    data['isFile'] = isFile;
    data['fileUrl'] = fileUrl;
    data['fileSize'] = fileSize;
    data['fileFilename'] = fileFilename;
    data['fileStoragePath'] = fileStoragePath;
    return data;
  }
}