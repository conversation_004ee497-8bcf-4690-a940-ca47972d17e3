import 'package:flutter/material.dart';

class ResultPosition {
  ResultPosition(
      {this.userId,
      this.userName,
      this.userPhoto,
      this.vehicle,
      this.number,
      this.time,
      this.position,
      this.s1,
      this.s2,
      this.s3,
      this.s4,
      this.speed});

  int? userId;
  String? userName;
  String? userPhoto;
  String? vehicle;
  String? number;
  String? time;
  int? position;
  String? s1;
  String? s2;
  String? s3;
  String? s4;
  String? speed;

  factory ResultPosition.fromJson(Map<String, dynamic> json) {
    try {
      return ResultPosition(
        userId: json["user_id"],
        userName: json["user_name"] ?? '',
        userPhoto: json["user_photo"],
        vehicle: json["vehicle"] ?? '',
        number: json["number"] ?? '',
        time: json["time"] ?? '',
        position: json["position"] ?? 0,
        s1: json["s1"] ?? '',
        s2: json["s2"] ?? '',
        s3: json["s3"] ?? '',
        s4: json["s4"] ?? '',
        speed: json["speed"] ?? '',
      );
    } catch (e) {
      debugPrint('Error parsing ResultPosition: $e');
      // Return a minimal valid ResultPosition object
      return ResultPosition(
        userId: json["user_id"] ?? 0,
        userName: json["user_name"] ?? 'Unknown',
        userPhoto: null,
        vehicle: '',
        number: '',
        time: '',
        position: json["position"] ?? 0,
        s1: json["s1"] ?? '',
        s2: json["s2"] ?? '',
        s3: json["s3"] ?? '',
        s4: json["s4"] ?? '',
        speed: json["speed"] ?? '',
      );
    }
  }

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "user_name": userName,
        "user_photo": userPhoto,
        "vehicle": vehicle,
        "number": number,
        "time": time,
        "position": position,
        "s1": s1,
        "s2": s2,
        "s3": s3,
        "s4": s4,
        "speed": speed
      };
}
