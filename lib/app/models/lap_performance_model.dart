import 'dart:convert';

class LapPerformance {
  final int? lapNumber;
  final String? lapTime;
  final String? s1;
  final String? s2;
  final String? s3;
  final String? s4;
  final double? speed;
  final String? timeOfDay;

  LapPerformance({
    this.lapNumber,
    this.lapTime,
    this.s1,
    this.s2,
    this.s3,
    this.s4,
    this.speed,
    this.timeOfDay,
  });

  factory LapPerformance.fromJson(Map<String, dynamic> json) => LapPerformance(
        lapNumber: json["lap_number"],
        lapTime: json["lap_time"],
        s1: json["s1"],
        s2: json["s2"],
        s3: json["s3"],
        s4: json["s4"],
        speed: json["speed"]?.toDouble(),
        timeOfDay: json["time_of_day"],
      );

  Map<String, dynamic> toJson() => {
        "lap_number": lapNumber,
        "lap_time": lapTime,
        "s1": s1,
        "s2": s2,
        "s3": s3,
        "s4": s4,
        "speed": speed,
        "time_of_day": timeOfDay,
      };

  // Helper method to convert lap time string to seconds for chart calculations
  double? lapTimeToSeconds() {
    if (lapTime == null) return null;

    try {
      // Format: "MM:SS.mmm" or "MM:SS:mmm"
      final parts = lapTime!.replaceAll(':', '.').split('.');
      if (parts.length >= 2) {
        final minutes = int.parse(parts[0]);
        final seconds = double.parse(parts[1]);
        final milliseconds =
            parts.length > 2 ? double.parse(parts[2]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      }
    } catch (e) {
      print('Error parsing lap time: $e');
    }
    return null;
  }

  // Helper method to convert sector time string to seconds
  double? sectorTimeToSeconds(String? sectorTime) {
    if (sectorTime == null || sectorTime.isEmpty) return null;

    try {
      // Handle formats like "1:02.471" or "40.896"
      if (sectorTime.contains(':')) {
        final parts = sectorTime.split(':');
        final minutes = int.parse(parts[0]);
        final secondsPart = parts[1].split('.');
        final seconds = double.parse(secondsPart[0]);
        final milliseconds =
            secondsPart.length > 1 ? double.parse(secondsPart[1]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      } else {
        final parts = sectorTime.split('.');
        final seconds = double.parse(parts[0]);
        final milliseconds =
            parts.length > 1 ? double.parse(parts[1]) / 1000 : 0;
        return seconds + milliseconds;
      }
    } catch (e) {
      print('Error parsing sector time: $e');
    }
    return null;
  }
}

class Event {
  final String? eventName;
  final String? eventDate;
  final String? organizerName;
  final String trackName;

  Event({
    this.eventName,
    this.eventDate,
    this.organizerName,
    this.trackName = "Sepang International Circuit",
  });
}

class PerformanceSummary {
  final Event event;
  final String driverName;
  final String vehicleInfo;
  final List<LapPerformance> laps;

  // Calculated metrics
  String? bestLapTime;
  int? bestLapNumber;
  int totalLaps;
  double? topSpeed;
  int? topSpeedLapNumber;
  String? averageLapTime;

  // Best sector times
  String? bestS1;
  int? bestS1LapNumber;
  String? bestS2;
  int? bestS2LapNumber;
  String? bestS3;
  int? bestS3LapNumber;
  String? bestS4;
  int? bestS4LapNumber;

  PerformanceSummary({
    required this.event,
    required this.driverName,
    required this.vehicleInfo,
    required this.laps,
  }) : totalLaps = laps.length {
    _calculateMetrics();
  }

  void _calculateMetrics() {
    if (laps.isEmpty) return;

    // Find best lap time
    double? bestLapSeconds;
    for (var i = 0; i < laps.length; i++) {
      final lap = laps[i];
      final lapSeconds = lap.lapTimeToSeconds();

      if (lapSeconds != null &&
          (bestLapSeconds == null || lapSeconds < bestLapSeconds)) {
        bestLapSeconds = lapSeconds;
        bestLapTime = lap.lapTime;
        bestLapNumber = lap.lapNumber;
      }
    }

    // Find top speed
    for (var i = 0; i < laps.length; i++) {
      final lap = laps[i];
      if (lap.speed != null && (topSpeed == null || lap.speed! > topSpeed!)) {
        topSpeed = lap.speed;
        topSpeedLapNumber = lap.lapNumber;
      }
    }

    // Calculate average lap time
    List<double> validLapTimes = [];
    for (var lap in laps) {
      final lapSeconds = lap.lapTimeToSeconds();
      if (lapSeconds != null) {
        // Filter out extreme outliers (e.g., laps over 10 minutes if most are 2-4 minutes)
        if (bestLapSeconds != null && lapSeconds < bestLapSeconds * 3) {
          validLapTimes.add(lapSeconds);
        }
      }
    }

    if (validLapTimes.isNotEmpty) {
      final avgSeconds =
          validLapTimes.reduce((a, b) => a + b) / validLapTimes.length;
      final avgMinutes = (avgSeconds / 60).floor();
      final avgRemainingSeconds = avgSeconds % 60;
      averageLapTime =
          "${avgMinutes.toString().padLeft(2, '0')}:${avgRemainingSeconds.toStringAsFixed(3)}";
    }

    // Find best sector times
    double? bestS1Seconds, bestS2Seconds, bestS3Seconds, bestS4Seconds;

    for (var i = 0; i < laps.length; i++) {
      final lap = laps[i];

      // Sector 1
      final s1Seconds = lap.sectorTimeToSeconds(lap.s1);
      if (s1Seconds != null &&
          (bestS1Seconds == null || s1Seconds < bestS1Seconds)) {
        bestS1Seconds = s1Seconds;
        bestS1 = lap.s1;
        bestS1LapNumber = lap.lapNumber;
      }

      // Sector 2
      final s2Seconds = lap.sectorTimeToSeconds(lap.s2);
      if (s2Seconds != null &&
          (bestS2Seconds == null || s2Seconds < bestS2Seconds)) {
        bestS2Seconds = s2Seconds;
        bestS2 = lap.s2;
        bestS2LapNumber = lap.lapNumber;
      }

      // Sector 3
      final s3Seconds = lap.sectorTimeToSeconds(lap.s3);
      if (s3Seconds != null &&
          (bestS3Seconds == null || s3Seconds < bestS3Seconds)) {
        bestS3Seconds = s3Seconds;
        bestS3 = lap.s3;
        bestS3LapNumber = lap.lapNumber;
      }

      // Sector 4
      final s4Seconds = lap.sectorTimeToSeconds(lap.s4);
      if (s4Seconds != null &&
          (bestS4Seconds == null || s4Seconds < bestS4Seconds)) {
        bestS4Seconds = s4Seconds;
        bestS4 = lap.s4;
        bestS4LapNumber = lap.lapNumber;
      }
    }
  }
}
