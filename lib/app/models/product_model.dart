import 'dart:convert';

Product productFromJson(String str) => Product.fromJson(json.decode(str));

String productToJson(Product data) => json.encode(data.toJson());

class Product {
  int? id;
  String? name;
  String? description;
  int? price;
  int? priceMyr;
  int? priceSale;
  int? priceSaleMyr;
  int? orderNum;
  int? status;
  String? featuredImage;
  List<String>? images;
  DateTime? createdAt;
  String? category;

  Product({
    this.id,
    this.name,
    this.description,
    this.price,
    this.priceMyr,
    this.priceSale,
    this.priceSaleMyr,
    this.orderNum,
    this.status,
    this.featuredImage,
    this.images,
    this.createdAt,
    this.category,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        price: json["price"],
        priceMyr: json["price_myr"],
        priceSale: json["price_sale"],
        priceSaleMyr: json["price_sale_myr"],
        orderNum: json["order_num"],
        status: json["status"],
        featuredImage: json["featured_image"],
        images: json["images"] == null
            ? []
            : List<String>.from(json["images"]!.map((x) => x)),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        category: json["category"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "price": price,
        "price_myr": priceMyr,
        "price_sale": priceSale,
        "price_sale_myr": priceSaleMyr,
        "order_num": orderNum,
        "status": status,
        "featured_image": featuredImage,
        "images":
            images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "created_at": createdAt?.toIso8601String(),
        "category": category,
      };
}
