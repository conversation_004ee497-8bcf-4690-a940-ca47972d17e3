// To parse this JSON data, do
//
//     final reward = rewardFromJson(jsonString);

import 'dart:convert';

Reward rewardFromJson(String str) => Reward.fromJson(json.decode(str));

String rewardToJson(Reward data) => json.encode(data.toJson());

class Reward {
  int? id;
  String? name;
  dynamic description;
  String? image;
  int? pointCost;
  String? couponType;
  int? value;
  int? valueMyr;

  Reward({
    this.id,
    this.name,
    this.description,
    this.image,
    this.pointCost,
    this.couponType,
    this.value,
    this.valueMyr,
  });

  factory Reward.fromJson(Map<String, dynamic> json) => <PERSON>ward(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        image: json["image"],
        pointCost: json["point_cost"],
        couponType: json["coupon_type"],
        value: json["value"],
        valueMyr: json["value_myr"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "image": image,
        "point_cost": pointCost,
        "coupon_type": couponType,
        "value": value,
        "value_myr": valueMyr,
      };
}
