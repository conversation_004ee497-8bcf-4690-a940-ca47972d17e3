import 'package:flutter/material.dart';

import 'event_modal.dart';
import 'result_position.dart';

class LeaderboardResponse {
  final List<Event> events;
  final List<ResultPosition> overallPositions;
  final List<ResultPosition> overallPositionsAll;
  final bool success;

  LeaderboardResponse({
    required this.events,
    required this.overallPositions,
    required this.overallPositionsAll,
    required this.success,
  });

  factory LeaderboardResponse.fromJson(Map<String, dynamic> json) {
    // Handle events list
    List<Event> eventsList = [];
    if (json['events'] != null && json['events'] is List) {
      try {
        eventsList = (json['events'] as List)
            .map((e) => Event.fromJson(e as Map<String, dynamic>))
            .toList();
      } catch (e) {
        debugPrint('Error parsing events: $e');
      }
    }

    // Handle overall positions list
    List<ResultPosition> positionsList = [];
    if (json['overall_positions'] != null &&
        json['overall_positions'] is List) {
      try {
        positionsList = (json['overall_positions'] as List)
            .map((e) => ResultPosition.fromJson(e as Map<String, dynamic>))
            .toList();
      } catch (e) {
        debugPrint('Error parsing overall_positions: $e');
      }
    }

    // Handle overall positions all list
    List<ResultPosition> positionsAllList = [];
    if (json['overall_positions_all'] != null &&
        json['overall_positions_all'] is List) {
      try {
        positionsAllList = (json['overall_positions_all'] as List)
            .map((e) => ResultPosition.fromJson(e as Map<String, dynamic>))
            .toList();
      } catch (e) {
        debugPrint('Error parsing overall_positions_all: $e');
      }
    }

    return LeaderboardResponse(
      success: json['success'] ?? false,
      events: eventsList,
      overallPositions: positionsList,
      overallPositionsAll: positionsAllList,
    );
  }

  Map<String, dynamic> toJson() => {
        'success': success,
        'events': events.map((e) => e.toJson()).toList(),
        'overall_positions': overallPositions.map((e) => e.toJson()).toList(),
        'overall_positions_all':
            overallPositionsAll.map((e) => e.toJson()).toList(),
      };
}
