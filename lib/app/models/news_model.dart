
class News {
  News({
    this.id,
    this.categoryId,
    this.categoryName,
    this.title,
    this.content = "",
    this.cleanContent = "",
    this.image = "",
    this.blogUrl = "",
    this.firebaseDynamicLink = "",
    this.createdAt,
  });

  int? id;
  int? categoryId;
  String? categoryName;
  String? title;
  String? content;
  String? cleanContent;
  String? image;
  String? blogUrl;
  String? firebaseDynamicLink;
  DateTime? createdAt;

  factory News.fromJson(Map<String, dynamic> json) => News(
    id: json["id"],
    categoryId: json["category_id"],
    categoryName: json["category_name"],
    title: json["title"],
    content: json["content"],
    cleanContent: json["clean_content"],
    image: json["image"],
    blogUrl: json["blog_url"] ?? "https://automoment.com.sg",
    firebaseDynamicLink: json["firebase_dynamic_link"],
    createdAt: DateTime.parse(json["created_at"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "category_id": categoryId,
    "category_name": categoryName,
    "title": title,
    "content": content,
    "clean_content": cleanContent,
    "image": image,
    "blog_url": blogUrl,
    "firebase_dynamic_link": firebaseDynamicLink,
    "created_at": createdAt?.toIso8601String(),
  };
}