import 'dart:convert';

CarListing carListingFromJson(String str) => CarListing.fromJson(json.decode(str));

String carListingToJson(CarListing data) => json.encode(data.toJson());

class CarListing {
  int? id;
  String? name;
  String? description;
  int? priceRent;
  int? priceRentMyr;
  int? priceSell;
  int? priceSellMyr;
  bool? isForSale;
  bool? isForRent;
  int? orderNum;
  int? status;
  String? featuredImage;
  List<String>? images;
  DateTime? createdAt;
  String? shareLink;

  CarListing({
    this.id,
    this.name,
    this.description,
    this.priceRent,
    this.priceRentMyr,
    this.priceSell,
    this.priceSellMyr,
    this.isForRent,
    this.isForSale,
    this.orderNum,
    this.status,
    this.featuredImage,
    this.images,
    this.createdAt,
    this.shareLink,
  });

  factory CarListing.fromJson(Map<String, dynamic> json) => CarListing(
    id: json["id"],
    name: json["name"],
    description: json["description"],
    priceRent: json["price_rent"],
    priceRentMyr: json["price_rent_myr"],
    priceSell: json["price_sell"],
    priceSellMyr: json["price_sell_myr"],
    isForRent: json["is_for_rent"],
    isForSale: json["is_for_sale"],
    orderNum: json["order_num"],
    status: json["status"],
    featuredImage: json["featured_image"],
    images: json["images"] == null ? [] : List<String>.from(json["images"]!.map((x) => x)),
    shareLink: json["share_link"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "description": description,
    "price_rent": priceRent,
    "price_rent_myr": priceRentMyr,
    "price_sell": priceSell,
    "price_sell_myr": priceSellMyr,
    "is_for_rent": isForRent,
    "is_for_sale": isForSale,
    "order_num": orderNum,
    "status": status,
    "featured_image": featuredImage,
    "images": images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
    "share_link": shareLink,
    "created_at": createdAt?.toIso8601String(),
  };
}
