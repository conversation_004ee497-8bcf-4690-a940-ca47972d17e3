import 'dart:convert';

Lifestyle lifestyleFromJson(String str) => Lifestyle.fromJson(json.decode(str));

String lifestyleToJson(Lifestyle data) => json.encode(data.toJson());

class Lifestyle {
  int? id;
  String? title;
  String? description;
  String? image;
  String? content;
  String? shareLink;

  Lifestyle({
    this.id,
    this.title,
    this.description,
    this.image,
    this.content,
    this.shareLink,
  });

  factory Lifestyle.fromJson(Map<String, dynamic> json) => Lifestyle(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        image: json["image"],
        content: json["content"],
        shareLink: json["short_url"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "image": image,
        "content": content,
        "short_url": shareLink,
      };
}
