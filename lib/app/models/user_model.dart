
class User {
  User({
    this.id,
    this.name,
    this.email,
    this.address,
    this.country,
    this.mobileNumber,
    this.birth,
    this.photo,
    this.createdAt,
    this.points,
    this.walletBalance,
  });

  int? id;
  String? name;
  String? email;
  String? address;
  String? country;
  String? mobileNumber;
  DateTime? birth;
  String? photo;
  DateTime? createdAt;
  int? points;
  String? walletBalance;

  factory User.fromJson(Map<String, dynamic> json) => User(
    id: json["id"],
    name: json["name"],
    email: json["email"],
    address: json["address"],
    country: json["country"],
    mobileNumber: json["mobile_number"],
    birth: json['birth'] != "" && json['birth'] != null ? DateTime.parse(json['birth']) : null,
    photo: json["photo"],
    createdAt: DateTime.parse(json["created_at"]),
    points: json["points"],
    walletBalance: json["wallet_balance"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "email": email,
    "address": address,
    "country": country,
    "mobile_number": mobileNumber,
    "birth": birth?.toIso8601String(),
    "photo": photo,
    "created_at": createdAt?.toIso8601String(),
    "points": points,
    "wallet_balance": walletBalance,
  };

}

// custom button widget with param title
