class LeaderboardInfo {
  final String? organization;
  String? logo; // Mutable if we need to prefix it
  final String? eventName;
  final String? eventDetail;

  LeaderboardInfo({
    this.organization,
    this.logo,
    this.eventName,
    this.eventDetail,
  });

  factory LeaderboardInfo.fromJson(Map<String, dynamic> json) {
    return LeaderboardInfo(
      organization: json['organization'] as String?,
      logo: json['logo'] as String?,
      eventName: json['event_name'] as String?,
      eventDetail: json['event_detail'] as String?,
    );
  }
}