class Notification {
  int? id;
  String? title;
  String? body;
  String? target;
  String? type;
  bool? read;
  String? data;
  DateTime? createdAt;

  Notification(
      {this.id,
        this.title,
        this.body,
        this.target,
        this.type,
        this.read,
        this.data,
        this.createdAt});

  Notification.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    body = json['body'];
    target = json['target'];
    type = json['type'];
    read = json['read'];
    data = json['data'];
    createdAt = DateTime.parse(json["created_at"]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['body'] = body;
    data['target'] = target;
    data['type'] = type;
    data['read'] = read;
    data['data'] = data;
    data['created_at'] = createdAt?.toIso8601String();
    return data;
  }
}
