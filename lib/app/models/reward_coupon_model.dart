import 'dart:convert';

List<RewardCoupon> rewardCouponFromJson(String str) => List<RewardCoupon>.from(json.decode(str).map((x) => RewardCoupon.fromJson(x)));

String rewardCouponToJson(List<RewardCoupon> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class RewardCoupon {
  int? discount;
  String? description;
  String? company;
  String? companyLogo;

  RewardCoupon({
    this.discount,
    this.description,
    this.company,
    this.companyLogo,
  });

  factory RewardCoupon.fromJson(Map<String, dynamic> json) => RewardCoupon(
    discount: json["discount"],
    description: json["description"],
    company: json["company"],
    companyLogo: json["company_logo"],
  );

  Map<String, dynamic> toJson() => {
    "discount": discount,
    "description": description,
    "company": company,
    "company_logo": companyLogo,
  };
}
