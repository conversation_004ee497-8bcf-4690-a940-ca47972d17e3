import 'dart:convert';
import 'dart:io';

import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../controllers/base_screen_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/user_model.dart';
import '../../../services/api_client.dart';

class ProfileEditController extends BaseScreenController {
  final UserController userController = Get.put(UserController());
  final formKey = GlobalKey<FormState>();

  var msg = "";

  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  var mobileNumberController = TextEditingController().obs;
  var countryController = TextEditingController().obs;
  var dateOfBirthController = TextEditingController().obs;
  DateTime? birthDate;
  var mobileNumber = PhoneNumber(isoCode: 'SG').obs;
  String fullPhoneNumber = "";

  @override
  onInit() {
    super.onInit();

    fullPhoneNumber = userController.user.value.mobileNumber!;

    nameController.text = userController.user.value.name!;
    emailController.text = userController.user.value.email!;
    addressController.text = userController.user.value.address!;
    countryController.value.text = userController.user.value.country!;
    dateOfBirthController.value.text = userController.getBirthDateFormatted();

    birthDate = userController.user.value.birth;

    initMobileNumber();
  }

  initMobileNumber() async {
    if (Platform.isAndroid) {
      if (fullPhoneNumber.startsWith("+")) {
        mobileNumber.value =
            await PhoneNumber.getRegionInfoFromPhoneNumber(fullPhoneNumber);
      } else {
        mobileNumber.value = await PhoneNumber.getRegionInfoFromPhoneNumber(
            "+65$fullPhoneNumber");
      }

      mobileNumberController.value.text =
          mobileNumber.value.parseNumber().replaceAll("+", "");
    } else {
      String countryCode =
          StringUtil.getCountryCodeFromPhoneNumber(fullPhoneNumber) ?? "SG";
      String phone =
          StringUtil.removeCountryCodeFromPhoneNumber(fullPhoneNumber) ?? "";

      mobileNumber.value =
          PhoneNumber(phoneNumber: phone, isoCode: countryCode);
      mobileNumberController.value.text =
          mobileNumber.value.parseNumber().replaceAll("+", "");
    }
  }

  @override
  String get screenName => 'Edit Profile';

  @override
  Future<void> onReady() async {
    super.onReady();
    // Screen view is automatically logged by BaseScreenController
  }

  @override
  void onClose() {
    super.onClose();
    nameController.dispose();
    emailController.dispose();
    addressController.dispose();
    mobileNumberController.value.dispose();
    countryController.value.dispose();
    dateOfBirthController.value.dispose();
  }

  Future<bool> updateProfile() async {
    debugPrint(
        "up full: $fullPhoneNumber - mobileNumber: ${mobileNumber.value}");

    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.updateProfile(
        userController.getToken(),
        userController.user.value.id!,
        nameController.text,
        emailController.text,
        addressController.text,
        countryController.value.text,
        mobileNumber.value.phoneNumber!,
        birthDate,
      );

      bool success = (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];

      msg = jsonDecode(response)['message'];
      EasyLoading.dismiss();

      if (success) {
        var user = User.fromJson(jsonDecode(response)['user']);
        userController.setUser(user);

        // Log profile update event
        await analytics.logCustomEvent(
          name: 'profile_updated',
          parameters: {
            'user_id': user.id.toString(),
            'updated_fields': 'profile_details',
          },
        );

        // Update user properties in analytics
        await analytics.setUserProperties(
          userId: user.id.toString(),
          country: user.country,
        );
      }

      return success;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
