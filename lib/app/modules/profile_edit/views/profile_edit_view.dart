import 'package:country_picker/country_picker.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/profile_edit_controller.dart';

class ProfileEditView extends StatefulWidget {
  const ProfileEditView({super.key});

  @override
  State<ProfileEditView> createState() => _ProfileEditViewState();
}

class _ProfileEditViewState extends State<ProfileEditView> {
  final ProfileEditController controller = Get.put(ProfileEditController());

  @override
  void dispose() {
    Get.delete<ProfileEditController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Update Profile', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
          child: Column(
            children: [
              Form(
                key: controller.formKey,
                child: Column(
                  children: [
                    TextFormField(
                      controller: controller.nameController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      decoration: const InputDecoration(
                        labelText: "Full Name",
                        hintText: "",
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    ),
                    TextFormField(
                      controller: controller.emailController,
                      keyboardType: TextInputType.emailAddress,
                      autocorrect: false,
                      decoration: const InputDecoration(
                        labelText: "Email",
                        hintText: "",
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }

                        if (!EmailValidator.validate(value)) {
                          return "Invalid email pattern";
                        }
                        return null;
                      },
                    ),
                    TextFormField(
                      controller: controller.addressController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      decoration: const InputDecoration(
                        labelText: "Address",
                        hintText: "",
                      ),
                      validator: (value) {
                        return null;

                        // if (value!.isEmpty) {
                        //   return 'Required';
                        // }
                      },
                    ),

                    GestureDetector(
                      onTap: () {
                        showCountryPicker(
                          context: context,
                          showPhoneCode: false,
                          favorite: ['SG', 'MY'],
                          onSelect: (Country country) {
                            controller.countryController.value.text =
                                country.name;
                          },
                        );
                      },
                      child: AbsorbPointer(
                        child: TextFormField(
                          controller: controller.countryController.value,
                          keyboardType: TextInputType.text,
                          //enabled: false,
                          readOnly: true,
                          decoration: const InputDecoration(
                            labelText: "Country",
                            hintText: "",
                          ),
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ),

                    Obx(() {
                      return InternationalPhoneNumberInput(
                        hintText: "Mobile Number",
                        onInputChanged: (PhoneNumber number) {
                          debugPrint("mobile: ${number.phoneNumber}");
                          controller.mobileNumber.value = number;
                        },
                        onInputValidated: (bool value) {
                          debugPrint("valid: $value");
                        },
                        selectorConfig: const SelectorConfig(
                          selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                        ),
                        ignoreBlank: false,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return 'Required';
                          }
                          return null;
                        },
                        autoValidateMode: AutovalidateMode.disabled,
                        // selectorTextStyle: TextStyle(color: Colors.black),
                        initialValue: PhoneNumber(
                            isoCode: controller.mobileNumber.value.isoCode),
                        textFieldController:
                            controller.mobileNumberController.value,
                        formatInput: false,
                        keyboardType: const TextInputType.numberWithOptions(
                            signed: true, decimal: true),
                        // inputBorder: OutlineInputBorder(),
                        onSaved: (PhoneNumber number) {
                          debugPrint('On Saved: $number');
                        },
                      );
                    }),

                    Obx(() {
                      return GestureDetector(
                        onTap: () {
                          DatePicker.showDatePicker(
                            context,
                            showTitleActions: true,
                            minTime: DateTime(1950, 1, 1),
                            //maxTime: DateTime(2000, 1, 1),
                            maxTime: DateTime.now()
                                .subtract(const Duration(days: 365 * 10)),
                            onConfirm: (date) {
                              debugPrint('confirm $date');

                              controller.dateOfBirthController.value.text =
                                  DateFormat('dd MMMM yyyy').format(date);
                              controller.birthDate = date;
                            },
                            currentTime: controller.birthDate,
                          );
                        },
                        child: AbsorbPointer(
                          child: TextFormField(
                            controller: controller.dateOfBirthController.value,
                            keyboardType: TextInputType.text,
                            //enabled: false,
                            readOnly: true,
                            decoration: const InputDecoration(
                              labelText: "Date of Birth",
                              hintText: "",
                            ),
                            validator: (value) {
                              return null;

                              // if (value!.isEmpty) {
                              //   return 'Required';
                              // }
                            },
                          ),
                        ),
                      );
                    }),

                    // term
                    const SizedBox(
                      height: 30,
                    ),
                    ElevatedButton(
                        style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.primaryButtonColor),
                            shape: WidgetStateProperty.all<
                                    RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(18.0),
                                    side: const BorderSide(
                                        color: AppColor.primaryButtonColor)))),
                        onPressed: () async {
                          if (controller.formKey.currentState!.validate()) {
                            debugPrint("ok");

                            if (await controller.updateProfile()) {
                              Get.dialog(
                                  KMessageDialogView(
                                      content: controller.msg,
                                      callback: () {
                                        Get.back();
                                        Get.back();
                                      }),
                                  barrierDismissible: false);
                            } else {
                              Get.dialog(
                                  KMessageDialogView(content: controller.msg),
                                  barrierDismissible: false);
                            }
                          } else {
                            debugPrint("not ok");
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          height: 50,
                          child: const Center(
                            child: Text("Update Profile",
                                style: TextStyle(fontSize: 16)),
                          ),
                        )),
                    const SizedBox(
                      height: 30,
                    ),
                    ElevatedButton(
                        style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.redButtonColor),
                            shape:
                                WidgetStateProperty.all<RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(18.0),
                                        side: const BorderSide(
                                            color: AppColor.redButtonColor)))),
                        onPressed: () {
                          Get.toNamed(Routes.ACCOUNT_DELETE);
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          height: 50,
                          child: const Center(
                            child: Text("Delete My Account",
                                style: TextStyle(fontSize: 16)),
                          ),
                        )),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
