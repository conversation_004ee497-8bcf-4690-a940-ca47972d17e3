import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../../services/api_client.dart';

class EventsFormController extends BaseScreenController {
  @override
  String get screenName => 'Indemnity Form';

  var event = Event().obs;
  var isLoading = true.obs;
  String msg = "";
  var jsonForm = "";
  var isFormFilled = false.obs;
  String signatureBase64 = "";

  Map<String, dynamic> formJson = {};

  final UserController userController = Get.put(UserController());

  var type = "driver".obs;

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments is Event) {
      event.value = Get.arguments;
    } else {
      event.value = Get.arguments['event'];
      type.value = Get.arguments['type'];
    }

    debugPrint("type: ${type.value}");

    fetchFormJson();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<bool> fetchFormJson() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      String response = '{"form": {}, "success": false}';

      if (type.value == "driver" || type.value == "group") {
        response = await ApiClient.getEventForm(
            userController.getToken(), event.value.id!);
      } else if (type.value == "passenger") {
        response = await ApiClient.getFormAsPassenger(
            userController.getToken(), event.value.id!);
      } else if (type.value == "additional driver") {
        response = await ApiClient.getFormAsAdditionalDriver(
            userController.getToken(), event.value.id!);
      }

      formJson = jsonDecode(response)['form'];
      isLoading.value = false;
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<bool> submitForm() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      String response = '{"message": "No action taken", "success": false}';

      if (type.value == "driver" || type.value == "group") {
        response = await ApiClient.submitEventForm(
            userController.getToken(),
            userController.user.value.id!,
            event.value.id!,
            jsonForm,
            signatureBase64);
      } else if (type.value == "passenger") {
        response = await ApiClient.signFormAsPassenger(
            userController.getToken(),
            event.value.id!,
            jsonForm,
            signatureBase64);
      } else if (type.value == "additional driver") {
        response = await ApiClient.signFormAsAdditionalDriver(
            userController.getToken(),
            event.value.id!,
            jsonForm,
            signatureBase64);
      }

      msg = jsonDecode(response)['message'];
      isLoading.value = false;
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }
}
