import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/modules/shared/custom_popup.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
import '../../shared/support_button.dart';
import '../controllers/leaderboard_detail_controller.dart';

class LeaderboardDetailView extends GetView<LeaderboardDetailController> {
  const LeaderboardDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: Text(
          "${controller.event.value.title ?? 'Event'} Leaderboard",
          style: AppTextStyles.titleText,
          textAlign: TextAlign.center,
          maxLines: 2,
        ),
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: () {
              controller.showScoringPopup.value = true;
            },
          ),
          GestureDetector(
            onTap: () async {
              final box = context.findRenderObject() as RenderBox?;
              double w = box?.size.width ?? 820;
              if (!controller.isSharing.value) {
                controller.isSharing.value = true;
                await Share.share(
                  '${controller.event.value.title} at ${controller.event.value.leaderboardShortUrl}',
                  sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100),
                );
                controller.isSharing.value = false;
              }
            },
            child: const Padding(
                padding: EdgeInsets.only(right: 20),
                child: Icon(
                  Icons.share,
                  color: AppColor.primaryColor,
                )),
          ),
        ],
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return Stack(
          children: [
            RefreshIndicator(
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  controller.fetchLeaderboardDetail();
                });
              },
              child: (controller.resultPositionList.isNotEmpty)
                  ? ListView.builder(
                      scrollDirection: Axis.vertical,
                      itemCount: controller.resultPositionList.length + 1,
                      itemBuilder: (BuildContext context, int itemIndex) {
                        return GestureDetector(
                          onTap: () {},
                          child: buildItemList(itemIndex),
                        );
                      })
                  :
                  // show not event not yet started message
                  Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.calendar_month, size: 50),
                          const SizedBox(height: 10),
                          Text(
                            controller.event.value.eventDate != null
                                ? "Event set to begin on\n${StringUtil.dateTimeToString(controller.event.value.eventDate!)}"
                                : "Event date not set",
                            style:
                                AppTextStyles.titleText.copyWith(height: 1.5),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
            ),

            // Scoring options popup
            if (controller.showScoringPopup.value) buildScoringPopup(),
          ],
        );
      }),
    );
  }

  buildItemList(int index) {
    int itemIndex = index - 1;
    return (index == 0)
        ? Padding(
            padding: const EdgeInsets.only(
              left: AppConfig.defaultPadding,
              right: AppConfig.defaultPadding,
              top: AppConfig.defaultPadding / 2,
              //bottom: AppConfig.defaultPadding / 2
            ),
            child: Card(
                elevation: 0,
                color: Colors.transparent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    (controller.leaderboardInfoLogo.isNotEmpty ||
                            controller.leaderboardInfoOrganization.isNotEmpty)
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              (controller.leaderboardInfoLogo.isNotEmpty)
                                  ? Image.network(
                                      "${AppConfig.storageUrl}/${controller.leaderboardInfoLogo}",
                                      width: 50,
                                      height: 50,
                                      fit: BoxFit.cover, errorBuilder:
                                          (BuildContext context,
                                              Object exception,
                                              StackTrace? stackTrace) {
                                      return const SizedBox(
                                          width: 50,
                                          height: 50 +
                                              AppConfig.defaultPadding / 2);
                                    })
                                  : const SizedBox(),
                              (controller.leaderboardInfoLogo.isNotEmpty)
                                  ? const SizedBox(
                                      width: 10,
                                    )
                                  : const SizedBox(),
                              (controller
                                      .leaderboardInfoOrganization.isNotEmpty)
                                  ? Text(
                                      controller.leaderboardInfoOrganization,
                                      style: AppTextStyles.titleText.copyWith(
                                          color: Colors.black, fontSize: 20),
                                    )
                                  : const SizedBox()
                            ],
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfoLogo.isNotEmpty ||
                            controller.leaderboardInfoOrganization.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfoLogo.isNotEmpty ||
                            controller.leaderboardInfoOrganization.isNotEmpty)
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfoEventName.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfoEventName.isNotEmpty)
                        ? Text(
                            controller.leaderboardInfoEventName,
                            style: AppTextStyles.normalText
                                .copyWith(color: Colors.black, fontSize: 16),
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfoEventName.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfoEventName.isNotEmpty)
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfoEventDetail.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfoEventDetail.isNotEmpty)
                        ? Text(
                            controller.leaderboardInfoEventDetail,
                            style: AppTextStyles.normalText
                                .copyWith(color: Colors.black, fontSize: 16),
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfoEventDetail.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfoEventDetail.isNotEmpty)
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    const SizedBox(height: 13),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Pos",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 13,
                        ),
                        Text(
                          "Name",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const Spacer(),
                        Text(
                          controller.getScoreTypeDisplayText(),
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                      ],
                    ),
                  ],
                )),
          )
        : Padding(
            padding: const EdgeInsets.only(
                left: AppConfig.defaultPadding,
                right: AppConfig.defaultPadding,
                top: AppConfig.defaultPadding / 2,
                bottom: AppConfig.defaultPadding / 2),
            child: Card(
                elevation: 3,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        (controller.resultPositionList[itemIndex].position == 0)
                            ? "-"
                            : controller.resultPositionList[itemIndex].position
                                .toString(),
                        style: AppTextStyles.titleText
                            .copyWith(color: Colors.black, fontSize: 20),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: AppConfig.defaultPadding,
                            right: AppConfig.defaultPadding,
                            top: AppConfig.defaultPadding,
                            bottom: AppConfig.defaultPadding / 2),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                controller.resultPositionList[itemIndex]
                                        .userName ??
                                    "",
                                maxLines: 1,
                                style: AppTextStyles.smallTitleBold
                                    .copyWith(fontSize: 16),
                                overflow: TextOverflow.ellipsis),
                            (controller.resultPositionList[itemIndex].vehicle ==
                                    "  ")
                                ? const SizedBox(height: 2)
                                : const SizedBox(height: 5),
                            (controller.resultPositionList[itemIndex].vehicle ==
                                    "  ")
                                ? Container()
                                : Text(
                                    controller.resultPositionList[itemIndex]
                                            .vehicle ??
                                        "",
                                    maxLines: 3,
                                    style: AppTextStyles.normalText,
                                    overflow: TextOverflow.ellipsis),
                          ],
                        ),
                      ),
                    ),
                    //Spacer(),
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        controller.getScoreTypeDisplayValue(
                            controller.resultPositionList[itemIndex]),
                        style: AppTextStyles.normalText
                            .copyWith(color: Colors.black, fontSize: 12),
                      ),
                    ),
                  ],
                )),
          );
  }

  Widget scoringOptionButton(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            // Close the popup
            controller.showScoringPopup.value = false;

            // Set the scoring type based on the button title
            String scoringType = '';
            switch (title) {
              case 'Best Time':
                scoringType = 'best_time';
                break;
              case 'Section 1':
                scoringType = 's1';
                break;
              case 'Section 2':
                scoringType = 's2';
                break;
              case 'Section 3':
                scoringType = 's3';
                break;
              case 'Section 4':
                scoringType = 's4';
                break;
              case 'Speed':
                scoringType = 'speed';
                break;
            }

            // Update the controller with the new scoring type
            controller.changeScoringType(scoringType);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: Center(
              child: Text(title, style: const TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget buildScoringPopup() {
    return CustomPopup(
      onTap: () {
        controller.showScoringPopup.value = false;
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 20),
          Text('Sort By',
              style: AppTextStyles.normalText.copyWith(fontSize: 18)),
          const SizedBox(height: 20),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: GridView.count(
              shrinkWrap: true,
              crossAxisCount: 2,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 2.5,
              children: [
                buildScoringOptionButton('Best Time', 'best_time'),
                buildScoringOptionButton('Top Speed', 'speed'),
                buildScoringOptionButton('Sector 1', 's1'),
                buildScoringOptionButton('Sector 2', 's2'),
                buildScoringOptionButton('Sector 3', 's3'),
                buildScoringOptionButton('Sector 4', 's4'),
              ],
            ),
          ),
          const SizedBox(height: 30),

          // Apply button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: () {
                  controller.applyScoring();
                  controller.showScoringPopup.value = false;
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.primaryButtonColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Apply',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget buildScoringOptionButton(String title, String value) {
    return Obx(() => OutlinedButton(
          onPressed: () {
            controller.tempScoringType.value =
                value; // Store in temp variable instead
          },
          style: OutlinedButton.styleFrom(
            backgroundColor: controller.tempScoringType.value ==
                    value // Use temp variable for highlighting
                ? AppColor.primaryButtonColor.withAlpha(26)
                : Colors.transparent,
            side: BorderSide.none,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            title,
            style: const TextStyle(
              color: AppColor.primaryButtonColor,
              fontSize: 14,
            ),
          ),
        ));
  }
}
