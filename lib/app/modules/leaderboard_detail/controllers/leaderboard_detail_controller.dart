import 'dart:convert';

import 'package:automoment/app/models/result_position.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../models/event_modal.dart';
import '../../../services/api_client.dart';

class LeaderboardDetailController extends BaseScreenController {
  @override
  String get screenName => 'Leaderboard Detail';

  var event = Event().obs;
  var isLoading = true.obs;
  var resultPositionList = <ResultPosition>[].obs;
  var leaderboardInfoOrganization = '';
  var leaderboardInfoLogo = '';
  var leaderboardInfoEventName = '';
  var leaderboardInfoEventDetail = '';
  var showScoringPopup = false.obs;
  var currentScoringType = 'best_time'.obs;
  var tempScoringType = 'best_time'.obs;
  var isSharing = false.obs;

  @override
  void onInit() {
    var data = Get.arguments;
    debugPrint("LeaderboardDetailController data: $data");
    if (data is int) {
      debugPrint("from deeplink int");
      event.value = Event(id: data);
    } else if (data is String) {
      debugPrint("from deeplink string");
      try {
        // Parse the JSON string to get the id
        Map<String, dynamic> jsonData = jsonDecode(data);
        event.value = Event(id: jsonData['id']);
      } catch (e) {
        debugPrint("Error parsing event ID: $e");
        event.value = Event(id: 0, title: "");
      }
    } else if (data is Event) {
      event.value = data;
    } else {
      // Default initialization if no arguments are provided
      //event.value = Event(id: 0, title: "Unknown Event");
    }

    tempScoringType.value = currentScoringType.value;
    fetchLeaderboardDetail();
    super.onInit();
  }

  void changeScoringType(String type) {
    currentScoringType.value = type;
    tempScoringType.value = type;
    fetchLeaderboardDetail();
  }

  void applyScoring() {
    currentScoringType.value = tempScoringType.value;
    fetchLeaderboardDetail();
  }

  String getScoreTypeDisplayText() {
    switch (currentScoringType.value) {
      case 'best_time':
        return 'Best Tm';
      case 's1':
        return 'Sector 1';
      case 's2':
        return 'Sector 2';
      case 's3':
        return 'Sector 3';
      case 's4':
        return 'Sector 4';
      case 'speed':
        return 'Top Speed';
      default:
        return 'Best Tm';
    }
  }

  String getScoreTypeDisplayValue(ResultPosition value) {
    switch (currentScoringType.value) {
      case 'best_time':
        return value.time ?? '-';
      case 's1':
        return value.s1 ?? '-';
      case 's2':
        return value.s2 ?? '-';
      case 's3':
        return value.s3 ?? '-';
      case 's4':
        return value.s4 ?? '-';
      case 'speed':
        return value.speed ?? '-';
      default:
        return value.time ?? '-';
    }
  }

  Future<void> fetchLeaderboardDetail() async {
    EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    try {
      // Make sure event.value.id is not null before making the API call
      if (event.value.id == null) {
        throw Exception("Event ID is null");
      }

      var response = await ApiClient.leaderboardDetail(
          event.value.id!,
          currentScoringType.value == 'best_time'
              ? ''
              : currentScoringType.value);

      var jsonData = jsonDecode(response);

      // Safely extract values from the JSON response with null checks
      var info = jsonData['info'] ?? {};
      leaderboardInfoOrganization = info['organization'] ?? '';
      leaderboardInfoLogo = info['logo'] ?? '';
      leaderboardInfoEventName = info['event_name'] ?? '';
      leaderboardInfoEventDetail = info['event_detail'] ?? '';

      // Update event with data from API response if available
      if (jsonData['event'] != null) {
        event.value = Event.fromJson(jsonData['event']);
      }

      // Process positions data with null safety
      var list = jsonData['positions'] ?? [];
      var l = <ResultPosition>[];

      list.forEach((dynamic d) {
        try {
          var n = ResultPosition.fromJson(d);
          l.add(n);
        } catch (e) {
          debugPrint("Error parsing position: $e");
          // Skip invalid position data
        }
      });

      resultPositionList.value = l;

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      debugPrint("Error in fetchLeaderboardDetail: $e");
      isLoading.value = false;
      EasyLoading.dismiss();
      // Don't throw the exception, just handle it gracefully
      // This prevents the app from crashing when there's an API error
    }
  }
}
