import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/gallery_controller.dart';

class GalleryView extends StatelessWidget {
  GalleryView({super.key});

  final controller = Get.find<GalleryController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: Obx(() {
          return Text(
            controller.selectedEventTitle.value,
            style: const TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16
            ),
          );
        }),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // const Padding(
              //   padding: EdgeInsets.all(10.0),
              //   child: Text(
              //     "Events",
              //     style: TextStyle(
              //         color: AppColor.primaryColor,
              //         fontWeight: FontWeight.bold,
              //         fontSize: 16
              //     ),
              //   ),
              // ),

              // events
              SizedBox(
                height: 100,
                child: ListView.builder(
                  physics: const ClampingScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.photosByEventList.length,
                  itemBuilder: (context, index) {
                    return
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 5.0, top: 5.0, bottom: 5.0),
                        child: InkWell(
                          onTap: () {
                            controller.updatePhotosList(controller
                                .photosByEventList[index].photos);
                            controller.selectedEventTitle.value =
                                controller.photosByEventList[index].title;
                          },
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child: Image.network(
                                "${AppConfig.storageUrl}${controller
                                    .photosByEventList[index].image}",
                                width: 150,
                                height: 100,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Image.asset(
                                    "assets/images/image_placeholder.png",
                                    width: 150,
                                    height: 100,
                                    fit: BoxFit.cover,
                                  );
                                }
                            ),
                          ),
                        ),
                      );
                  },
                ),
              ),

              // Padding(
              //   padding: EdgeInsets.all(10.0),
              //   child: Text(
              //     controller.selectedEventTitle.value,
              //     style: const TextStyle(
              //         color: AppColor.primaryColor,
              //         fontWeight: FontWeight.bold,
              //         fontSize: 16
              //     ),
              //   ),
              // ),

              // photos
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5.0),
                  child: GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 5.0,
                          mainAxisSpacing: 5.0,
                          childAspectRatio: 0.9
                      ),
                      itemCount: controller.photosList.length,
                      itemBuilder: (context, index) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            InkWell(
                              onTap: () {
                                Get.toNamed(
                                  Routes.GALLERY_DETAIL,
                                  arguments: {
                                    "photo": controller.photosList,
                                    "index": index
                                  }
                                );
                              },
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8.0),
                                child: Image.network(
                                    "${AppConfig.storageUrl}${controller
                                        .photosList[index].photo}",
                                    width: MediaQuery
                                        .of(context)
                                        .size
                                        .width / 2 - 10,
                                    height: MediaQuery
                                        .of(context)
                                        .size
                                        .width / 2 - 10,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error,
                                        stackTrace) {
                                      return Image.asset(
                                        "assets/images/image_placeholder.png",
                                        //width: 100,
                                        width: 150,
                                        height: 100,
                                        fit: BoxFit.cover,
                                      );
                                    }
                                ),
                              ),
                            ),
                            const SizedBox(height: 5.0,),
                            Expanded(
                              child: Text(
                                controller.photosList[index].caption,
                                style: const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12
                                ),
                                maxLines: 1,
                              ),
                            ),
                          ],
                        );
                      }
                  ),
                ),
                // child: ListView.builder(
                //   physics: const ClampingScrollPhysics(),
                //   scrollDirection: Axis.vertical,
                //   itemCount: controller.photosList.length,
                //   itemBuilder: (context, index) {
                //     return Container(
                //       //height: 20,
                //       //width: 10,
                //         alignment: Alignment.center,
                //         //color: Colors.yellow,
                //         child: ClipRRect(
                //           borderRadius: BorderRadius.circular(8.0),
                //           child: Image.network(
                //               "${AppConfig.storageUrl}${controller
                //                   .photosList[index].photo}",
                //               width: 150,
                //               height: 100,
                //               fit: BoxFit.cover,
                //               errorBuilder: (context, error, stackTrace) {
                //                 return Image.asset(
                //                   "assets/images/image_placeholder.png",
                //                   width: 150,
                //                   height: 100,
                //                   fit: BoxFit.cover,
                //                 );
                //               }
                //           ),
                //         ),
                //     );
                //   },
                // ),
              ),

            ]
        );
      }),
    );
  }
}
