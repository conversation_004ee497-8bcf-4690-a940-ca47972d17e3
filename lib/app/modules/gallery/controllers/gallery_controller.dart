import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/models/event_photo_model.dart';
import 'package:automoment/app/services/api_client.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

class GalleryController extends BaseScreenController {
  @override
  String get screenName => 'Gallery';

  final userController = Get.find<UserController>();

  var photosList = [].obs;
  var photosByEventList = [].obs;
  var eventList = [].obs;
  var selectedEventTitle = ''.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await fetchMyPhotos();
  }

  Future<void> fetchMyPhotos() async {
    EasyLoading.show(status: 'loading...');

    try {
      EasyLoading.dismiss();

      var response = await ApiClient.getMyPhotos(userController.getToken());
      //allPhotosList.value = jsonDecode(response)['all'].map((e) => Photo.fromJson(e)).toList();
      photosByEventList.value = jsonDecode(response)['events']
          .map((e) => EventPhoto.fromJson(e))
          .toList();
      photosList.value = photosByEventList[0].photos;
      selectedEventTitle.value = photosByEventList[0].title;
    } catch ($e) {
      EasyLoading.dismiss();
      debugPrint($e as String?);
    }
  }

  void updatePhotosList(photos) {
    photosList.value = photos;
  }
}
