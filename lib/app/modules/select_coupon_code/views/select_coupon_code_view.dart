import 'package:automoment/app/models/coupon_reward_model.dart';
import 'package:coupon_uikit/coupon_uikit.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../make_payment/controllers/make_payment_controller.dart';
import '../controllers/select_coupon_code_controller.dart';

class SelectCouponCodeView extends StatelessWidget {
  SelectCouponCodeView({super.key});

  final controller = Get.put(SelectCouponCodeController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title:
              const Text('Select Coupon Code', style: AppTextStyles.titleText),
          elevation: 1,
          automaticallyImplyLeading: true,
        ),
        body: Obx(() {
          return (!controller.isFinishLoading.value)
              ? Container()
              : controller.availableCouponList.isNotEmpty
                  ? SingleChildScrollView(
                      child: Column(children: [
                        const SizedBox(
                          height: 10,
                        ),
                        ...controller.availableCouponList.map((coupon) {
                          return Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 8),
                              child: couponItemView(coupon));
                        }),
                        const SizedBox(
                          height: 10,
                        ),
                      ]),
                    )
                  : SizedBox(
                      width: double.maxFinite,
                      child: Padding(
                        padding: const EdgeInsets.all(40.0),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text("You have no coupons yet",
                                  style: AppTextStyles.normalTextBold.copyWith(
                                    color: AppColor.kTextColor,
                                  )),
                              const SizedBox(height: 10),
                              controller.currentPoints.value == 0
                                  ? Text(
                                      "Start collecting points to redeem coupons.",
                                      textAlign: TextAlign.center,
                                      style: AppTextStyles.normalText.copyWith(
                                        color: AppColor.kTextColor,
                                      ))
                                  : Text(
                                      "Congrats! You earned ${controller.currentPoints.value} points. Redeem them for coupons.",
                                      textAlign: TextAlign.center,
                                      style: AppTextStyles.normalText.copyWith(
                                        color: AppColor.kTextColor,
                                      )),
                            ]),
                      ),
                    );
        }));
  }

  couponItemView(CouponReward coupon) {
    const Color primaryColor = Color(0xfff1e3d3);
    const Color secondaryColor = AppColor.secondaryColor;

    String discountStr = "";

    if (coupon.type == "percentage") {
      discountStr = "${coupon.value}%";
    } else {
      discountStr = "SGD ${coupon.value}";
    }

    String? couponDesc = coupon.description;

    return GestureDetector(
      onTap: () {
        final makePaymentController = Get.find<MakePaymentController>();
        makePaymentController.fillCouponCode(coupon.code!);
        Get.back();
      },
      child: CouponCard(
        height: 150,
        backgroundColor: primaryColor,
        clockwise: true,
        curvePosition: 135,
        curveRadius: 30,
        curveAxis: Axis.vertical,
        borderRadius: 10,
        firstChild: Container(
          decoration: const BoxDecoration(
            color: secondaryColor,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        discountStr,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Text(
                        'OFF',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const Divider(color: Colors.white54, height: 0),
              const Expanded(
                child: Center(
                  child: Text(
                    'AUTOMOMENT',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        secondChild: Container(
          width: double.maxFinite,
          padding: const EdgeInsets.all(18),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Coupon Code',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                coupon.code!,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 24,
                  color: secondaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                couponDesc ?? "",
                maxLines: 2,
                textAlign: TextAlign.start,
                style: const TextStyle(
                  color: Colors.black45,
                ),
              ),
              Text(
                'Valid until ${DateFormat('d MMM yyyy').format(coupon.endDate!)}',
                textAlign: TextAlign.start,
                style: const TextStyle(
                  fontSize: 11,
                  color: Colors.black45,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
