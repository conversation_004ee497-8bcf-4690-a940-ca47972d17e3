import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/coupon_reward_model.dart';
import '../../../services/api_client.dart';

class SelectCouponCodeController extends BaseScreenController {
  @override
  String get screenName => 'Select Coupon Code';

  final userController = Get.put(UserController());
  var availableCouponList = <CouponReward>[].obs;
  var currentPoints = 0.obs;
  var isFinishLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchRewards();
  }

  Future<void> fetchRewards() async {
    EasyLoading.show(status: 'Loading...');
    try {
      var response = await ApiClient.getRewards(userController.getToken());
      currentPoints.value = jsonDecode(response)['current_points'];

      var list = jsonDecode(response)['available'];
      availableCouponList.clear();

      list.forEach((dynamic d) {
        availableCouponList.add(CouponReward.fromJson(d));
      });

      EasyLoading.dismiss();
      isFinishLoading.value = true;
    } catch (e) {
      isFinishLoading.value = true;
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
