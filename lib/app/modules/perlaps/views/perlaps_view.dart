import 'package:automoment/app/routes/app_pages.dart';
import 'package:contained_tab_bar_view/contained_tab_bar_view.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
import '../../shared/support_button.dart';
import '../controllers/perlaps_controller.dart';

class PerlapsView extends StatelessWidget {
  PerlapsView({super.key});

  final PerlapsController controller = Get.put(PerlapsController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: Text(controller.event.title!, style: AppTextStyles.titleText),
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics_outlined),
            tooltip: 'Performance Summary',
            onPressed: () {
              Get.toNamed(Routes.PERFORMANCE_SUMMARY);
            },
          ),
        ],
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return ContainedTabBarView(
          tabs: const [
            Text('My Laps'),
            Text('Leaderboard'),
          ],
          views: [
            RefreshIndicator(
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  controller.fetchPerLaps();
                });
              },
              child: (controller.lapList.isNotEmpty)
                  ? Column(
                      children: [
                        // Performance Summary Button
                        Padding(
                          padding:
                              const EdgeInsets.all(AppConfig.defaultPadding),
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Get.toNamed(Routes.PERFORMANCE_SUMMARY);
                            },
                            icon: const Icon(Icons.analytics_outlined),
                            label: const Text('View Performance Summary'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColor.secondaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 16),
                              minimumSize: const Size(double.infinity, 50),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: ListView.builder(
                            scrollDirection: Axis.vertical,
                            itemCount: controller.lapList.length + 1,
                            itemBuilder: (BuildContext context, int itemIndex) {
                              return GestureDetector(
                                onTap: () {},
                                child: buildItemListLap(itemIndex),
                              );
                            },
                          ),
                        ),
                      ],
                    )
                  :
                  // show not event not yet started message
                  Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.calendar_month, size: 50),
                          const SizedBox(height: 10),
                          Text(
                              "Event set to begin on\n${StringUtil.dateTimeToString(controller.event.eventDate!)}",
                              style:
                                  AppTextStyles.titleText.copyWith(height: 1.5),
                              textAlign: TextAlign.center),
                        ],
                      ),
                    ),
            ),
            RefreshIndicator(
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  controller.fetchPerLaps();
                });
              },
              child: (controller.resultPositionList.isNotEmpty)
                  ? ListView.builder(
                      scrollDirection: Axis.vertical,
                      itemCount: controller.resultPositionList.length + 1,
                      itemBuilder: (BuildContext context, int itemIndex) {
                        return GestureDetector(
                          onTap: () {},
                          child: buildItemListLeaderboard(itemIndex),
                        );
                      })
                  :
                  // show not event not yet started message
                  Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.calendar_month, size: 50),
                          const SizedBox(height: 10),
                          Text(
                            "Event set to begin on\n${StringUtil.dateTimeToString(controller.event.eventDate!)}",
                            style:
                                AppTextStyles.titleText.copyWith(height: 1.5),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
            )
          ],
          tabBarProperties: const TabBarProperties(
            indicatorColor: AppColor.kTextColor,
            labelColor: AppColor.kTextColor,
          ),
          onChange: (index) {},
        );
      }),
    );
  }

  buildItemListLeaderboard(int index) {
    int itemIndex = index - 1;
    return (index == 0)
        ? Padding(
            padding: const EdgeInsets.only(
              left: AppConfig.defaultPadding,
              right: AppConfig.defaultPadding,
              top: AppConfig.defaultPadding / 2,
              //bottom: AppConfig.defaultPadding / 2
            ),
            child: Card(
                elevation: 0,
                color: Colors.transparent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ((controller.leaderboardInfo.value.logo != null &&
                                controller
                                    .leaderboardInfo.value.logo!.isNotEmpty) ||
                            (controller.leaderboardInfo.value.organization !=
                                    null &&
                                controller.leaderboardInfo.value.organization!
                                    .isNotEmpty))
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              (controller.leaderboardInfo.value.logo != null &&
                                      controller.leaderboardInfo.value.logo!
                                          .isNotEmpty)
                                  ? Image.network(
                                      controller.leaderboardInfo.value
                                          .logo!, // Assuming logo is now a full URL from controller
                                      width: 50,
                                      height: 50,
                                      fit: BoxFit.cover, errorBuilder:
                                          (BuildContext context,
                                              Object exception,
                                              StackTrace? stackTrace) {
                                      return const SizedBox(
                                          width: 50,
                                          height: 50 +
                                              AppConfig.defaultPadding / 2);
                                    })
                                  : const SizedBox(),
                              (controller.leaderboardInfo.value.logo != null &&
                                      controller.leaderboardInfo.value.logo!
                                          .isNotEmpty)
                                  ? const SizedBox(
                                      width: 10,
                                    )
                                  : const SizedBox(),
                              (controller.leaderboardInfo.value.organization !=
                                          null &&
                                      controller.leaderboardInfo.value
                                          .organization!.isNotEmpty)
                                  ? Text(
                                      controller
                                          .leaderboardInfo.value.organization!,
                                      style: AppTextStyles.titleText.copyWith(
                                          color: Colors.black, fontSize: 20),
                                    )
                                  : const SizedBox()
                            ],
                          )
                        : const SizedBox(),
                    ((controller.leaderboardInfo.value.logo != null &&
                                controller
                                    .leaderboardInfo.value.logo!.isNotEmpty) ||
                            (controller.leaderboardInfo.value.organization !=
                                    null &&
                                controller.leaderboardInfo.value.organization!
                                    .isNotEmpty))
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    ((controller.leaderboardInfo.value.logo != null &&
                                controller
                                    .leaderboardInfo.value.logo!.isNotEmpty) ||
                            (controller.leaderboardInfo.value.organization !=
                                    null &&
                                controller.leaderboardInfo.value.organization!
                                    .isNotEmpty))
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? Text(
                            controller.leaderboardInfo.value.eventName!,
                            style: AppTextStyles.normalText
                                .copyWith(color: Colors.black, fontSize: 16),
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? Text(
                            controller.leaderboardInfo.value.eventDetail!,
                            style: AppTextStyles.normalText
                                .copyWith(color: Colors.black, fontSize: 16),
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    const SizedBox(height: 13),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Pos",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 13,
                        ),
                        Text(
                          "Name",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const Spacer(),
                        Text(
                          "Best Tm",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                      ],
                    ),
                  ],
                )),
          )
        : Padding(
            padding: const EdgeInsets.only(
                left: AppConfig.defaultPadding,
                right: AppConfig.defaultPadding,
                top: AppConfig.defaultPadding / 2,
                bottom: AppConfig.defaultPadding / 2),
            child: Card(
                elevation: 3,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        (controller.resultPositionList[itemIndex].position == 0)
                            ? "-"
                            : controller.resultPositionList[itemIndex].position
                                .toString(),
                        style: AppTextStyles.titleText
                            .copyWith(color: Colors.black, fontSize: 20),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: AppConfig.defaultPadding,
                            right: AppConfig.defaultPadding,
                            top: AppConfig.defaultPadding,
                            bottom: AppConfig.defaultPadding / 2),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                controller.resultPositionList[itemIndex]
                                        .userName ??
                                    "",
                                maxLines: 1,
                                style: AppTextStyles.smallTitleBold
                                    .copyWith(fontSize: 16),
                                overflow: TextOverflow.ellipsis),
                            (controller.resultPositionList[itemIndex].vehicle ==
                                    "  ")
                                ? const SizedBox(height: 2)
                                : const SizedBox(height: 5),
                            (controller.resultPositionList[itemIndex].vehicle ==
                                    "  ")
                                ? Container()
                                : Text(
                                    controller.resultPositionList[itemIndex]
                                            .vehicle ??
                                        "",
                                    maxLines: 3,
                                    style: AppTextStyles.normalText,
                                    overflow: TextOverflow.ellipsis),
                          ],
                        ),
                      ),
                    ),
                    //Spacer(),
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        (controller
                                .resultPositionList[itemIndex].time!.isNotEmpty)
                            ? controller.resultPositionList[itemIndex].time!
                            : "00:00 000",
                        style: AppTextStyles.normalText
                            .copyWith(color: Colors.black, fontSize: 12),
                      ),
                    ),
                  ],
                )),
          );
  }

  buildItemListLap(int index) {
    int itemIndex = index - 1;
    return (index == 0)
        ? Padding(
            padding: const EdgeInsets.only(
              left: AppConfig.defaultPadding,
              right: AppConfig.defaultPadding,
              top: AppConfig.defaultPadding / 2,
              //bottom: AppConfig.defaultPadding / 2
            ),
            child: Card(
                elevation: 0,
                color: Colors.transparent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ((controller.leaderboardInfo.value.logo != null &&
                                controller
                                    .leaderboardInfo.value.logo!.isNotEmpty) ||
                            (controller.leaderboardInfo.value.organization !=
                                    null &&
                                controller.leaderboardInfo.value.organization!
                                    .isNotEmpty))
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              (controller.leaderboardInfo.value.logo != null &&
                                      controller.leaderboardInfo.value.logo!
                                          .isNotEmpty)
                                  ? Image.network(
                                      controller.leaderboardInfo.value
                                          .logo!, // Assuming logo is now a full URL from controller
                                      width: 50,
                                      height: 50,
                                      fit: BoxFit.cover, errorBuilder:
                                          (BuildContext context,
                                              Object exception,
                                              StackTrace? stackTrace) {
                                      return const SizedBox(
                                          width: 50,
                                          height: 50 +
                                              AppConfig.defaultPadding / 2);
                                    })
                                  : const SizedBox(),
                              (controller.leaderboardInfo.value.logo != null &&
                                      controller.leaderboardInfo.value.logo!
                                          .isNotEmpty)
                                  ? const SizedBox(
                                      width: 10,
                                    )
                                  : const SizedBox(),
                              (controller.leaderboardInfo.value.organization !=
                                          null &&
                                      controller.leaderboardInfo.value
                                          .organization!.isNotEmpty)
                                  ? Text(
                                      controller
                                          .leaderboardInfo.value.organization!,
                                      style: AppTextStyles.titleText.copyWith(
                                          color: Colors.black, fontSize: 20),
                                    )
                                  : const SizedBox()
                            ],
                          )
                        : const SizedBox(),
                    ((controller.leaderboardInfo.value.logo != null &&
                                controller
                                    .leaderboardInfo.value.logo!.isNotEmpty) ||
                            (controller.leaderboardInfo.value.organization !=
                                    null &&
                                controller.leaderboardInfo.value.organization!
                                    .isNotEmpty))
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    ((controller.leaderboardInfo.value.logo != null &&
                                controller
                                    .leaderboardInfo.value.logo!.isNotEmpty) ||
                            (controller.leaderboardInfo.value.organization !=
                                    null &&
                                controller.leaderboardInfo.value.organization!
                                    .isNotEmpty))
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? Text(
                            controller.leaderboardInfo.value.eventName!,
                            style: AppTextStyles.normalText
                                .copyWith(color: Colors.black, fontSize: 16),
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventName != null &&
                            controller
                                .leaderboardInfo.value.eventName!.isNotEmpty)
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? Text(
                            controller.leaderboardInfo.value.eventDetail!,
                            style: AppTextStyles.normalText
                                .copyWith(color: Colors.black, fontSize: 16),
                          )
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? const SizedBox(height: 5)
                        : const SizedBox(),
                    (controller.leaderboardInfo.value.eventDetail != null &&
                            controller
                                .leaderboardInfo.value.eventDetail!.isNotEmpty)
                        ? Container(
                            width: double.infinity,
                            height: 3,
                            color: AppColor.primaryColor,
                          )
                        : const SizedBox(),
                    const SizedBox(height: 13),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Lap",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const Spacer(),
                        Text(
                          "Time of Day",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const Spacer(),
                        Text(
                          "Lap Time",
                          style: AppTextStyles.titleText
                              .copyWith(color: Colors.black, fontSize: 14),
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                      ],
                    ),
                  ],
                )),
          )
        : Padding(
            padding: const EdgeInsets.only(
                left: AppConfig.defaultPadding,
                right: AppConfig.defaultPadding,
                top: AppConfig.defaultPadding / 2,
                bottom: AppConfig.defaultPadding / 2),
            child: Card(
                elevation: 3,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        (controller.lapList[itemIndex].lap == 0)
                            ? "-"
                            : controller.lapList[itemIndex].lap.toString(),
                        style: AppTextStyles.normalText
                            .copyWith(color: Colors.black, fontSize: 12),
                      ),
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.only(left: 0),
                      child: Text(controller.lapList[itemIndex].timeOfDay ?? "",
                          maxLines: 1,
                          style:
                              AppTextStyles.normalText.copyWith(fontSize: 12),
                          overflow: TextOverflow.ellipsis),
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.all(AppConfig.defaultPadding),
                      child: Text(
                        (controller.lapList[itemIndex].lapTime!.isNotEmpty)
                            ? controller.lapList[itemIndex].lapTime!
                            : "00:00 000",
                        style: AppTextStyles.normalText
                            .copyWith(color: Colors.black, fontSize: 12),
                      ),
                    ),
                  ],
                )),
          );
  }
}
