import 'dart:convert';

import 'package:automoment/app/constants/app_config.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../controllers/base_screen_controller.dart';
import '../../../models/event_modal.dart';
import '../../../models/lap_model.dart';
import '../../../models/leaderboard_info_model.dart';
import '../../../models/result_position.dart';
import '../../../services/api_client.dart';

class PerlapsController extends BaseScreenController {
  @override
  String get screenName => 'Per Laps';
  var event = Event();
  var isLoading = true.obs;
  var resultPositionList = <ResultPosition>[].obs;
  var leaderboardInfo = LeaderboardInfo().obs; // Use the model

  var lapList = <Lap>[].obs;

  @override
  void onInit() {
    super.onInit();
    event = Get.arguments;
    fetchPerLaps();
  }

  Future<void> fetchPerLaps() async {
    EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    try {
      final UserController userController = Get.find();
      var responseString =
          await ApiClient.perLaps(userController.getToken(), event.id!);
      var decodedResponse = jsonDecode(responseString);

      if (decodedResponse['info'] != null &&
          decodedResponse['info'] is Map<String, dynamic>) {
        leaderboardInfo.value =
            LeaderboardInfo.fromJson(decodedResponse['info']);
        // Prefix logo URL if it's relative
        if (leaderboardInfo.value.logo != null &&
            !leaderboardInfo.value.logo!.startsWith('http')) {
          leaderboardInfo.value.logo =
              AppConfig.storageUrl + leaderboardInfo.value.logo!;
        }
      }

      if (decodedResponse['positions'] != null &&
          decodedResponse['positions'] is List) {
        final List<dynamic> positionsData = decodedResponse['positions'];
        resultPositionList.value = positionsData
            .map((dynamic item) =>
                ResultPosition.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        resultPositionList.value = [];
      }

      if (decodedResponse['laps'] != null && decodedResponse['laps'] is List) {
        final List<dynamic> lapsData = decodedResponse['laps'];
        lapList.value = lapsData
            .map((dynamic item) => Lap.fromJson(item as Map<String, dynamic>))
            .toList();
      } else {
        lapList.value = [];
      }

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e, stackTrace) {
      isLoading.value = false;
      EasyLoading.dismiss();
      debugPrint("Error fetching per-laps data: $e");
      Sentry.captureException(e, stackTrace: stackTrace);
      Get.snackbar(
        "Error",
        "Failed to load lap data. Please check your connection and try again.",
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
