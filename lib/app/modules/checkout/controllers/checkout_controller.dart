import 'dart:convert';

import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../cart/controllers/cart_controller.dart';
import '../../../controllers/base_screen_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';

class CheckoutController extends BaseScreenController {
  @override
  String get screenName => 'Checkout';
  final cartController = Get.find<CartController>();
  final userController = Get.find<UserController>();
  final cartItems = [].obs;
  final totalAmount = 0.0.obs;
  final isLoading = false.obs;
  final isOrderLoading = false.obs;

  // Collection information
  final deliveryInfo = {
    'type': 'collection',
    'location': 'Sepang International Circuit',
    'instructions':
        'Please bring your order confirmation and ID for verification.',
  };

  @override
  void onInit() {
    super.onInit();
    cartItems.value = cartController.cartItems;
    calculateTotal();
  }

  @override
  void onReady() {
    super.onReady();
    // BaseScreenController will handle logging screen view

    // Log begin checkout event
    _logBeginCheckout();
  }

  Future<void> _logBeginCheckout() async {
    try {
      List<Map<String, dynamic>> items = [];

      for (var item in cartItems) {
        items.add({
          'item_id': item['id'].toString(),
          'item_name': item['name'] ?? 'Unknown Product',
          'price': item['price'] ?? 0.0,
          'quantity': item['quantity'] ?? 1,
        });
      }

      await analytics.logCustomEvent(
        name: 'begin_checkout',
        parameters: {
          'items_count': cartItems.length,
          'value': totalAmount.value,
          'currency': 'USD',
        },
      );
    } catch (e) {
      // Silently handle analytics errors
      debugPrint('Analytics error: $e');
    }
  }

  void calculateTotal() {
    double total = 0;
    for (var item in cartItems) {
      total += (item['price'] ?? 0.0) * (item['quantity'] ?? 1);
    }
    totalAmount.value = total;
  }

  bool get canProceed => true;

  Future<void> _logPurchase(String orderId) async {
    try {
      // Get the first item for the item details (if available)
      String? itemId;
      String? itemName;

      if (cartItems.isNotEmpty) {
        itemId = cartItems[0]['id']?.toString();
        itemName = cartItems[0]['name'];
      }

      await analytics.logPurchase(
        transactionId: orderId,
        value: totalAmount.value,
        currency: 'USD',
        itemId: itemId,
        itemName: itemName,
      );
    } catch (e) {
      // Silently handle analytics errors
      debugPrint('Analytics error: $e');
    }
  }

  Future<void> order() async {
    EasyLoading.show(status: "Please wait...");
    try {
      //isOrderLoading.value = true;

      // Since we can't pass delivery info directly to the API,
      // we'll store it in a temporary field that can be retrieved later
      Get.put(
          Map<String, dynamic>.from({
            'delivery_type': deliveryInfo['type'],
            'delivery_location': deliveryInfo['location'],
            'delivery_instructions': deliveryInfo['instructions'],
            'items': cartItems
                .map((item) => {
                      'product_id': item['id'],
                      'quantity': item['quantity'],
                      'price': item['price'],
                    })
                .toList(),
            'total_amount': totalAmount.value,
          }),
          tag: 'temp_order_data');

      // Call the API with a dummy shipping address ID (0 or -1)
      // The backend should check if this is a collection order
      final response = await ApiClient.makeProductOrder(
        userController.getToken(),
        -1, // Use -1 to indicate this is a collection order
      );

      EasyLoading.dismiss();

      final jsonResponse = Map<String, dynamic>.from(json.decode(response));

      if (jsonResponse['success'] == true) {
        final orderId = jsonResponse['data']['id'];

        // Log purchase event
        await _logPurchase(orderId.toString());

        // Clear cart from shared preferences
        cartController.cartItems.clear();
        await cartController.saveCartItems();

        // Navigate to payment
        Get.toNamed('/product-payment', arguments: {'order_id': orderId});
      } else {
        SnackHelper.showError(
            jsonResponse['message'] ?? 'Failed to place order');
      }
    } catch (e) {
      SnackHelper.showError('Something went wrong while placing your order');
      EasyLoading.dismiss();
    } finally {
      isOrderLoading.value = false;
    }
  }
}
