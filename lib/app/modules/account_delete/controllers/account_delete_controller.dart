import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';

class AccountDeleteController extends BaseScreenController {
  @override
  String get screenName => 'Delete Account Request';

  final UserController userController = Get.put(UserController());
  String msg = "";

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<bool> callUserDeleteRequestApi() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.userDeleteRequest(
          userController.getToken(), userController.user.value.id!);

      bool success = jsonDecode(response)['success'];
      msg = jsonDecode(response)['message'];

      EasyLoading.dismiss();
      if (success) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
