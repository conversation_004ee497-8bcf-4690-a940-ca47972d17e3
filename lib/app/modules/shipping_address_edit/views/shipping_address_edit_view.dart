import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/constants/app_config.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/shipping_address_edit_controller.dart';

class ShippingAddressEditView extends GetView<ShippingAddressEditController> {
  const ShippingAddressEditView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.arrow_back_ios),
        ),
        title: const Text(
          'Edit Address',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildForm(context),
          ],
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppConfig.defaultPadding * 2,
        right: AppConfig.defaultPadding * 2,
        bottom: AppConfig.defaultPadding * 2,
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          children: [
            TextFormField(
              controller: controller.fullNameController,
              decoration: const InputDecoration(
                labelText: "Full Name",
                hintText: "Enter your full name",
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                return null;
              },
            ),
            TextFormField(
              controller: controller.phoneController,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                labelText: "Phone Number",
                hintText: "Enter your phone number",
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                return null;
              },
            ),
            TextFormField(
              controller: controller.addressLine1Controller,
              decoration: const InputDecoration(
                labelText: "Address Line 1",
                hintText: "Enter your street address",
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                return null;
              },
            ),
            TextFormField(
              controller: controller.addressLine2Controller,
              decoration: const InputDecoration(
                labelText: "Address Line 2 (Optional)",
                hintText: "Apartment, suite, unit, etc.",
              ),
            ),
            TextFormField(
              controller: controller.cityController,
              decoration: const InputDecoration(
                labelText: "City (Optional)",
                hintText: "Enter your city",
              ),
            ),
            TextFormField(
              controller: controller.stateController,
              decoration: const InputDecoration(
                labelText: "State (Optional)",
                hintText: "Enter your state",
              ),
            ),
            TextFormField(
              controller: controller.postalCodeController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: "Postal Code (Optional)",
                hintText: "Enter your postal code",
              ),
            ),
            DropdownButtonFormField<String>(
              value: controller.selectedCountry.value,
              decoration: const InputDecoration(
                labelText: "Country",
              ),
              items: const [
                DropdownMenuItem(
                  value: 'Singapore',
                  child: Text('Singapore'),
                ),
                DropdownMenuItem(
                  value: 'Malaysia',
                  child: Text('Malaysia'),
                ),
              ],
              onChanged: (value) {
                if (value != null) {
                  controller.selectedCountry.value = value;
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : () => controller.updateAddress(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.primaryButtonColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: controller.isLoading.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Update Address',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
              )),
            ),
          ],
        ),
      ),
    );
  }
}
