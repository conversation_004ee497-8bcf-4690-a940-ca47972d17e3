import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/vehicles_controller.dart';

class VehiclesView extends StatelessWidget {
  VehiclesView({super.key});

  final VehiclesController controller = Get.put(VehiclesController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('My Vehicles', style: AppTextStyles.titleText),
        elevation: 1,
        actions: [
          GestureDetector(
            onTap: (){
              Get.toNamed(Routes.VEHICLES_ADD);
            },
            child: const Padding(
                padding: EdgeInsets.only(right: 20),
                child: Icon(
                  Icons.add,
                  color: Colors.black,
                )
            ),
          ),
        ],
      ),
      floatingActionButton: const SupportButton(),
      body: RefreshIndicator(
        onRefresh: () async {
          await controller.getVehicles();
        },
        child: Obx(() => ListView.builder(
                itemCount: controller.vehicles.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      Get.toNamed(Routes.VEHICLES_EDIT, arguments: controller.vehicles[index]);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Card(
                        elevation: 3,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(20),
                                  topRight: Radius.circular(20)
                              ),
                              child: Container(
                                constraints: const BoxConstraints(
                                  maxHeight: 200,
                                ),
                                //height: 200,
                                child: (controller.vehicles[index].image == "") ? Image.asset(
                                  "assets/images/placeholder_vehicle.jpg",
                                  width: Get.width,
                                  fit: BoxFit.fitWidth,
                                ) : Image.network(
                                  "${AppConfig.storageUrl}${controller.vehicles[index].image}",
                                  width: Get.width,
                                  fit: BoxFit.fitWidth,
                                  errorBuilder: (BuildContext context, Object exception, StackTrace? stackTrace) {
                                    return Image.asset(
                                      "assets/images/placeholder_vehicle.jpg",
                                      width: Get.width,
                                      fit: BoxFit.fitWidth,
                                    );
                                  }
                                )
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(AppConfig.defaultPadding),
                              child: Text("${controller.vehicles[index].make} ${controller.vehicles[index].model} ${controller.vehicles[index].year ?? ""}",
                                  maxLines: 1,
                                  style: AppTextStyles.smallTitleBold,
                                  overflow: TextOverflow.ellipsis
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )),
      ),
    );
  }
}
