import 'dart:convert';

import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/vehicle_model.dart';
import '../../../services/api_client.dart';

class VehiclesController extends BaseScreenController {
  @override
  String get screenName => 'Vehicles';

  var isLoading = false.obs;
  var vehicles = <Vehicle>[].obs;

  final UserController userController = Get.put(UserController());

  @override
  void onInit() {
    super.onInit();

    isLoading.value = true;
    getVehicles();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<void> getVehicles() async {
    EasyLoading.show(status: 'Loading...');
    try {
      var response = await ApiClient.getVehicles(
          userController.getToken(), userController.user.value.id!);
      var list = jsonDecode(response)['data'];
      var l = <Vehicle>[];

      list.forEach((dynamic d) {
        var n = Vehicle.fromJson(d);
        l.add(n);
      });

      vehicles.value = l;
      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
