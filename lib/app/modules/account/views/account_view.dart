import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../controllers/user_controller.dart';
import '../../../routes/app_pages.dart';
import '../controllers/account_controller.dart';

class AccountView extends StatelessWidget {
  AccountView({super.key});

  final AccountController controller = Get.put(AccountController());
  final UserController userController = Get.put(UserController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          title: const Text(
            "Profile",
            style: TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
          elevation: 1,
          leading: IconButton(
            padding: const EdgeInsets.only(left: 10),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            icon: const Icon(Icons.menu),
            iconSize: 30,
            color: Colors.black,
          ),
          actions: [
            IconButton(
              padding: const EdgeInsets.only(right: 10),
              onPressed: () {
                Get.toNamed(Routes.NOTIFICATION);
              },
              icon: Obx(() {
                return Stack(
                  children: [
                    const Icon(Icons.notifications,
                        color: Colors.black, size: 30),
                    (controller.userController.unreadNotificationCount.value >
                            0)
                        ? Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(1),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(6)),
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 12,
                                minHeight: 12,
                              ),
                              child: Text(
                                (controller.userController
                                            .unreadNotificationCount.value >
                                        99)
                                    ? "99+"
                                    : controller.userController
                                        .unreadNotificationCount.value
                                        .toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          )
                        : const SizedBox()
                  ],
                );
              }),
            ),
          ],
        ),
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: () async {
              await controller.fetchWalletBalance();
              controller.userController.getUser();
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  getProfileSection(context),
                  const SizedBox(height: 20),
                  getWalletBalance(),
                  const SizedBox(height: 40),
                  fakeTopRadius(),
                  getInfo()
                ],
              ),
            ),
          ),
        ));
  }

  Container fakeTopRadius() {
    return Container(
      height: 30,
      decoration: const BoxDecoration(
          color: AppColor.defaultBackground,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30), topRight: Radius.circular(30)),
          boxShadow: AppConfig.kShadow),
    );
  }

  Widget getWalletBalance() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        decoration: BoxDecoration(
          color: AppColor.primaryColor.withAlpha(26),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.account_balance_wallet,
              color: AppColor.primaryColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Credit Balance',
                  style: TextStyle(fontSize: 13, color: Colors.grey),
                ),
                Obx(
                  () => controller.isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AppColor.primaryColor),
                          ),
                        )
                      : Text.rich(
                          TextSpan(
                            children: [
                              const TextSpan(
                                text: 'SGD ',
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.normal,
                                    color: AppColor.primaryColor),
                              ),
                              TextSpan(
                                text: controller.walletBalance,
                                style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: AppColor.primaryColor),
                              ),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget getProfileSection(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.symmetric(horizontal: AppConfig.defaultPadding * 2),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              showImagePicker(context);
            },
            child: Obx(() {
              if (userController.user.value.photo != null &&
                  userController.user.value.photo!.isNotEmpty) {
                return CircleAvatar(
                  radius: 40,
                  backgroundImage: NetworkImage(
                      "${AppConfig.storageUrl}${userController.user.value.photo!}"),
                );
              } else {
                return const CircleAvatar(
                  radius: 40,
                  backgroundImage:
                      AssetImage("assets/images/profile_placeholder.png"),
                );
              }
            }),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Obx(() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    userController.user.value.name ?? '',
                    style: AppTextStyles.titleText,
                  ),
                  const SizedBox(height: 8),
                  Obx(() {
                    if (controller.badges.isEmpty) {
                      return const SizedBox.shrink();
                    }
                    return Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: controller.badges.map((badge) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppColor.primaryColor.withAlpha(26),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColor.primaryColor.withAlpha(26 * 3),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.stars_rounded,
                                size: 12,
                                color: AppColor.primaryColor,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                badge['name'] ?? '',
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: AppColor.primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    );
                  }),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget getBadgesSection() {
    return const SizedBox.shrink();
  }

  getInfo() {
    return Column(
      children: [
        InfoItem(title: 'Email', value: userController.user.value.email ?? '-'),
        InfoItem(
            title: 'Contact No',
            value: userController.user.value.mobileNumber ?? '-'),
        InfoItem(
            title: 'Date of Birth',
            value: userController.getBirthDateFormatted()),
        const SizedBox(
          height: AppConfig.defaultPadding * 2,
        ),
        InkWell(
          onTap: () {
            Get.toNamed(Routes.PROFILE_EDIT);
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Container(
                alignment: Alignment.center,
                height: 50,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.0),
                    boxShadow: const [
                      BoxShadow(
                          offset: Offset(0, -3),
                          blurRadius: 10,
                          color: Colors.black26)
                    ]),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.PROFILE_EDIT);
                  },
                  child: const Text(
                    "View Full Profile",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColor.secondaryColor),
                  ),
                )),
          ),
        ),
        const SizedBox(
          height: AppConfig.defaultPadding * 2,
        ),
        InkWell(
          onTap: () {
            Get.toNamed(Routes.VEHICLES);
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Container(
                alignment: Alignment.center,
                height: 50,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.0),
                    boxShadow: const [
                      BoxShadow(
                          offset: Offset(0, -3),
                          blurRadius: 10,
                          color: Colors.black26)
                    ]),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.VEHICLES);
                  },
                  child: const Text(
                    "My Vehicles",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColor.secondaryColor),
                  ),
                )),
          ),
        ),

        const SizedBox(
          height: AppConfig.defaultPadding * 2,
        ),

        InkWell(
          onTap: () {
            Get.toNamed(Routes.PERSONAL_RESULTS);
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Container(
                alignment: Alignment.center,
                height: 50,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.0),
                    boxShadow: const [
                      BoxShadow(
                          offset: Offset(0, -3),
                          blurRadius: 10,
                          color: Colors.black26)
                    ]),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.PERSONAL_RESULTS);
                  },
                  child: const Text(
                    "My Personal Results",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColor.secondaryColor),
                  ),
                )),
          ),
        ),

        const SizedBox(
          height: AppConfig.defaultPadding * 2,
        ),

        InkWell(
          onTap: () {
            Get.toNamed(Routes.GALLERY);
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Container(
                alignment: Alignment.center,
                height: 50,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.0),
                    boxShadow: const [
                      BoxShadow(
                          offset: Offset(0, -3),
                          blurRadius: 10,
                          color: Colors.black26)
                    ]),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.GALLERY);
                  },
                  child: const Text(
                    "My Photos",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColor.secondaryColor),
                  ),
                )),
          ),
        ),

        const SizedBox(
          height: AppConfig.defaultPadding * 2,
        ),

        InkWell(
          onTap: () {
            Get.toNamed(Routes.ORDER_STATUS);
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Container(
                alignment: Alignment.center,
                height: 50,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.0),
                    boxShadow: const [
                      BoxShadow(
                          offset: Offset(0, -3),
                          blurRadius: 10,
                          color: Colors.black26)
                    ]),
                child: GestureDetector(
                  onTap: () {
                    Get.toNamed(Routes.ORDER_STATUS);
                  },
                  child: const Text(
                    "My Orders",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColor.secondaryColor),
                  ),
                )),
          ),
        ),

        const SizedBox(
          height: AppConfig.defaultPadding * 2,
        ),

        // ProfileButton(
        //     title: "Show Firebasetoken for test",
        //     onClick: () {
        //       Clipboard.setData(new ClipboardData(
        //           text: Session.shared.box.read('firebaseToken')));
        //       Get.snackbar('Your firebaseToken Code', 'Copied');
        //     }),
        // profileButton(
        //     title: "View Service",
        //     onClick: () {
        //       print("View Serviceclicked");
        //     }),
        // profileButton(title : "Create a Club", onClick: (){
        //   print("Create a Club clicked");
        //   Get.to(() => GetAppScreen());
        // }),
      ],
    );
  }

  void showImagePicker(context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return Container(
              height: 140,
              decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30))),
              child: Column(children: [
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Choose Profile Photo",
                  style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColor.primaryColor),
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        controller.getImageFromGallery();
                      },
                      child: const Text(
                        "From Gallery",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryColor),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        controller.getImageFromCamera();
                      },
                      child: const Text(
                        "Take a Photo",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryColor),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ]));
        });
  }
}

class InfoItem extends StatelessWidget {
  final String title;
  final String value;

  const InfoItem({super.key, required this.title, required this.value});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: AppConfig.defaultPadding * 2,
          vertical: AppConfig.defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Text(
              title,
              style: AppTextStyles.normalText,
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              value,
              style: AppTextStyles.normalTextBold,
            ),
          ),
        ],
      ),
    );
  }
}
