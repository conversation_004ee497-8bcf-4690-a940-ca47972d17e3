import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../controllers/base_screen_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/user_model.dart';
import '../../../services/api_client.dart';

class AccountController extends BaseScreenController {
  @override
  String get screenName => 'Profile';

  final UserController userController = Get.put(UserController());
  final _walletBalance = '0.00'.obs;
  final _isLoading = false.obs;
  final _badges = <Map<String, dynamic>>[].obs;

  String get walletBalance {
    try {
      final balance = double.parse(_walletBalance.value);
      return balance.toStringAsFixed(2).replaceAll(RegExp(r'\.00$'), '');
    } catch (e) {
      return _walletBalance.value;
    }
  }

  bool get isLoading => _isLoading.value;
  List<Map<String, dynamic>> get badges => _badges;
  final ImagePicker _picker = ImagePicker();

  String msg = "";

  @override
  void onInit() {
    super.onInit();
    fetchWalletBalance();
  }

  Future<void> fetchWalletBalance() async {
    try {
      _isLoading.value = true;
      final response =
          await ApiClient.getWalletBalance(userController.getToken());
      final jsonResponse = jsonDecode(response);
      if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
        _walletBalance.value = jsonResponse['data']['balance'];
        if (jsonResponse['data']['badges'] != null) {
          _badges.value =
              List<Map<String, dynamic>>.from(jsonResponse['data']['badges']);
        }
      }
    } catch (e) {
      debugPrint('Error fetching wallet balance: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> getImageFromGallery() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    uploadPhoto(image!);
    Get.back();
  }

  Future<void> getImageFromCamera() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    uploadPhoto(image!);
    Get.back();
  }

  Future<bool> uploadPhoto(XFile image) async {
    final imageBytes = await image.readAsBytes();
    String base64Image = base64.encode(imageBytes);

    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.uploadPhoto(userController.getToken(),
          userController.user.value.id!, base64Image);

      var user = User.fromJson(jsonDecode(response)['user']);
      userController.setUser(user);

      msg = jsonDecode(response)['message'];
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
