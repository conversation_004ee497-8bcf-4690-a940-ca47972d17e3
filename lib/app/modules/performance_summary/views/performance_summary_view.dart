import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/lap_performance_model.dart';

class PerformanceSummaryView extends StatelessWidget {
  final PerformanceSummary summary;

  const PerformanceSummaryView({
    Key? key,
    required this.summary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Performance'),
        backgroundColor: AppColor.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.compare_arrows),
            tooltip: 'Compare with previous events',
            onPressed: () {
              Get.toNamed('/performance-comparison');
            },
          ),
        ],
      ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEventHeader(),
              _buildPerformanceHighlights(),
              _buildLapTimeChart(context),
              _buildBestSectorPerformance(),
              _buildLapByLapBreakdown(context),
            ],
          ),
        ),
      ),
    );
  }

  // Section 1: Event & Driver Identification
  Widget _buildEventHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: AppColor.primaryColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            summary.event.eventName ?? 'Track Day',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            summary.event.eventDate ?? 'Date not available',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            summary.event.trackName,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Icon(Icons.person, color: Colors.white70, size: 18),
              const SizedBox(width: 8),
              Text(
                summary.driverName,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.directions_car, color: Colors.white70, size: 18),
              const SizedBox(width: 8),
              Text(
                summary.vehicleInfo,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Section 2: Performance Highlights
  Widget _buildPerformanceHighlights() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Performance Highlights',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildStatCard(
                title: 'Best Lap',
                value: summary.bestLapTime ?? '-',
                subtitle: summary.bestLapNumber != null
                    ? 'on Lap ${summary.bestLapNumber}'
                    : null,
                icon: Icons.timer,
                color: AppColor.secondaryColor,
              ),
              _buildStatCard(
                title: 'Laps Driven',
                value: summary.totalLaps.toString(),
                icon: Icons.repeat,
                color: AppColor.primaryColor,
              ),
              _buildStatCard(
                title: 'Top Speed',
                value: summary.topSpeed != null
                    ? '${summary.topSpeed!.toStringAsFixed(1)} km/h'
                    : '-',
                subtitle: summary.topSpeedLapNumber != null
                    ? 'on Lap ${summary.topSpeedLapNumber}'
                    : null,
                icon: Icons.speed,
                color: AppColor.redButtonColor,
              ),
              _buildStatCard(
                title: 'Avg. Lap Time',
                value: summary.averageLapTime ?? '-',
                icon: Icons.av_timer,
                color: AppColor.kTextGrayColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    String? subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: AppColor.kTextLightColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Section 3: Lap Time Progression Chart
  Widget _buildLapTimeChart(BuildContext context) {
    // Filter out laps with null lap times or extreme outliers
    final validLaps = summary.laps.where((lap) {
      final seconds = lap.lapTimeToSeconds();
      if (seconds == null) return false;

      // Filter out extreme outliers (e.g., laps over 3x the best lap time)
      if (summary.bestLapTime != null) {
        final bestLapSeconds = summary.laps
            .firstWhere((l) => l.lapTime == summary.bestLapTime)
            .lapTimeToSeconds();
        if (bestLapSeconds != null && seconds > bestLapSeconds * 3) {
          return false;
        }
      }
      return true;
    }).toList();

    if (validLaps.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No valid lap data available for chart',
            style: TextStyle(color: AppColor.kTextGrayColor),
          ),
        ),
      );
    }

    // Prepare data for the chart
    final spots = validLaps.map((lap) {
      return FlSpot(
        lap.lapNumber?.toDouble() ?? 0,
        lap.lapTimeToSeconds() ?? 0,
      );
    }).toList();

    // Find min and max values for better scaling
    double minY = double.infinity;
    double maxY = 0;
    for (var spot in spots) {
      if (spot.y < minY) minY = spot.y;
      if (spot.y > maxY) maxY = spot.y;
    }

    // Add some padding to the min/max values
    final padding = (maxY - minY) * 0.1;
    minY = (minY - padding).clamp(0, double.infinity);
    maxY = maxY + padding;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Lap Times Progression',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Track your improvement over the session',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                // Make the chart wider for better scrolling experience
                // Adjust the width based on the number of laps
                width: max(
                    MediaQuery.of(context).size.width, validLaps.length * 50.0),
                padding: const EdgeInsets.only(right: 16),
                child: LineChart(
                  LineChartData(
                    gridData: FlGridData(
                      show: true,
                      drawVerticalLine: true,
                      horizontalInterval: 30, // 30 seconds interval
                      verticalInterval: 1, // 1 lap interval
                    ),
                    titlesData: FlTitlesData(
                      show: true,
                      rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          reservedSize: 30,
                          interval: 1,
                          getTitlesWidget: (value, meta) {
                            return SideTitleWidget(
                              axisSide: meta.axisSide,
                              child: Text(
                                value.toInt().toString(),
                                style: const TextStyle(
                                  color: AppColor.kTextGrayColor,
                                  fontSize: 12,
                                ),
                              ),
                            );
                          },
                        ),
                        axisNameWidget: const Text(
                          'Lap Number',
                          style: TextStyle(
                            color: AppColor.kTextGrayColor,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          interval: 30, // 30 seconds interval
                          reservedSize: 40,
                          getTitlesWidget: (value, meta) {
                            final minutes = (value / 60).floor();
                            final seconds = (value % 60).floor();
                            return SideTitleWidget(
                              axisSide: meta.axisSide,
                              child: Text(
                                '$minutes:${seconds.toString().padLeft(2, '0')}',
                                style: const TextStyle(
                                  color: AppColor.kTextGrayColor,
                                  fontSize: 12,
                                ),
                              ),
                            );
                          },
                        ),
                        axisNameWidget: const Text(
                          'Lap Time (min:sec)',
                          style: TextStyle(
                            color: AppColor.kTextGrayColor,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    borderData: FlBorderData(
                      show: true,
                      border: Border.all(color: AppColor.kLightBgColor),
                    ),
                    minX: 1,
                    maxX: validLaps.length.toDouble(),
                    minY: minY,
                    maxY: maxY,
                    lineBarsData: [
                      LineChartBarData(
                        spots: spots,
                        isCurved: false,
                        color: AppColor.secondaryColor,
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: FlDotData(
                          show: true,
                          getDotPainter: (spot, percent, barData, index) {
                            // Highlight the best lap with a different color
                            final isbestLap = summary.bestLapNumber != null &&
                                spot.x == summary.bestLapNumber!.toDouble();
                            return FlDotCirclePainter(
                              radius: isbestLap ? 6 : 4,
                              color: isbestLap
                                  ? AppColor.lightGreenColor
                                  : AppColor.secondaryColor,
                              strokeWidth: 2,
                              strokeColor: Colors.white,
                            );
                          },
                        ),
                        belowBarData: BarAreaData(
                          show: true,
                          color: AppColor.secondaryColor.withOpacity(0.1),
                        ),
                      ),
                    ],
                    lineTouchData: LineTouchData(
                      touchTooltipData: LineTouchTooltipData(
                        tooltipBgColor: Colors.white.withOpacity(0.8),
                        tooltipRoundedRadius: 8,
                        getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                          return touchedBarSpots.map((barSpot) {
                            final lapIndex = barSpot.x.toInt() - 1;
                            if (lapIndex >= 0 && lapIndex < validLaps.length) {
                              final lap = validLaps[lapIndex];
                              final minutes = (barSpot.y / 60).floor();
                              final seconds = (barSpot.y % 60);
                              return LineTooltipItem(
                                'Lap ${lap.lapNumber}\n${minutes}:${seconds.toStringAsFixed(3)}',
                                const TextStyle(
                                  color: AppColor.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }
                            return null;
                          }).toList();
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Add a hint about scrolling
          const Padding(
            padding: EdgeInsets.only(top: 4),
            child: Text(
              '← Scroll horizontally to view all laps →',
              style: TextStyle(
                fontSize: 12,
                color: AppColor.kTextLightColor,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  // Section 4: Best Sector Performance
  Widget _buildBestSectorPerformance() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Best Sector Performance',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildSectorCard(
                title: 'Best S1',
                value: summary.bestS1 ?? 'N/A',
                lapNumber: summary.bestS1LapNumber,
                color: Colors.purple,
              ),
              _buildSectorCard(
                title: 'Best S2',
                value: summary.bestS2 ?? 'N/A',
                lapNumber: summary.bestS2LapNumber,
                color: Colors.orange,
              ),
              _buildSectorCard(
                title: 'Best S3',
                value: summary.bestS3 ?? 'N/A',
                lapNumber: summary.bestS3LapNumber,
                color: Colors.green,
              ),
              _buildSectorCard(
                title: 'Best S4',
                value: summary.bestS4 ?? 'N/A',
                lapNumber: summary.bestS4LapNumber,
                color: Colors.blue,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectorCard({
    required String title,
    required String value,
    int? lapNumber,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (lapNumber != null) ...[
            const SizedBox(height: 4),
            Text(
              'on Lap $lapNumber',
              style: const TextStyle(
                fontSize: 12,
                color: AppColor.kTextLightColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Section 5: Detailed Lap-by-Lap Breakdown
  Widget _buildLapByLapBreakdown(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Lap-by-Lap Breakdown',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: summary.laps.length,
            itemBuilder: (context, index) {
              final lap = summary.laps[index];
              final isbestLap = lap.lapTime == summary.bestLapTime;
              final hasTopSpeed = lap.lapNumber == summary.topSpeedLapNumber;
              final hasBestS1 = lap.lapNumber == summary.bestS1LapNumber;
              final hasBestS2 = lap.lapNumber == summary.bestS2LapNumber;
              final hasBestS3 = lap.lapNumber == summary.bestS3LapNumber;
              final hasBestS4 = lap.lapNumber == summary.bestS4LapNumber;

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: isbestLap
                        ? AppColor.lightGreenColor
                        : Colors.transparent,
                    width: isbestLap ? 2 : 0,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Lap ${lap.lapNumber}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColor.primaryColor,
                                ),
                              ),
                              if (isbestLap) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: AppColor.lightGreenColor,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Row(
                                    children: [
                                      Icon(
                                        Icons.emoji_events,
                                        color: Colors.white,
                                        size: 14,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        'BEST',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                          ),
                          Text(
                            lap.lapTime ?? 'N/A',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isbestLap
                                  ? AppColor.lightGreenColor
                                  : AppColor.secondaryColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildSectorItem(
                              'S1',
                              lap.s1,
                              hasBestS1,
                              Colors.purple,
                            ),
                          ),
                          Expanded(
                            child: _buildSectorItem(
                              'S2',
                              lap.s2,
                              hasBestS2,
                              Colors.orange,
                            ),
                          ),
                          Expanded(
                            child: _buildSectorItem(
                              'S3',
                              lap.s3,
                              hasBestS3,
                              Colors.green,
                            ),
                          ),
                          Expanded(
                            child: _buildSectorItem(
                              'S4',
                              lap.s4,
                              hasBestS4,
                              Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.speed,
                                size: 16,
                                color: hasTopSpeed
                                    ? AppColor.redButtonColor
                                    : AppColor.kTextGrayColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                lap.speed != null
                                    ? '${lap.speed!.toStringAsFixed(1)} km/h'
                                    : 'N/A',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight:
                                      hasTopSpeed ? FontWeight.bold : null,
                                  color: hasTopSpeed
                                      ? AppColor.redButtonColor
                                      : AppColor.kTextGrayColor,
                                ),
                              ),
                              if (hasTopSpeed) ...[
                                const SizedBox(width: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 1),
                                  decoration: BoxDecoration(
                                    color: AppColor.redButtonColor
                                        .withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: const Text(
                                    'TOP',
                                    style: TextStyle(
                                      color: AppColor.redButtonColor,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          Text(
                            'Started: ${lap.timeOfDay ?? 'N/A'}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppColor.kTextLightColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectorItem(
      String label, String? value, bool isBest, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
        color: isBest ? color.withOpacity(0.1) : Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isBest ? color.withOpacity(0.5) : Colors.transparent,
        ),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isBest ? color : AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value ?? 'N/A',
            style: TextStyle(
              fontSize: 14,
              fontWeight: isBest ? FontWeight.bold : FontWeight.normal,
              color: isBest ? color : AppColor.kTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
