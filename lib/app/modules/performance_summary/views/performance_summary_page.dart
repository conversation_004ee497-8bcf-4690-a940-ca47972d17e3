import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/performance_summary_controller.dart';
import 'performance_summary_view.dart';

class PerformanceSummaryPage extends GetView<PerformanceSummaryController> {
  const PerformanceSummaryPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.performanceSummary.value == null) {
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      return PerformanceSummaryView(
        summary: controller.performanceSummary.value!,
      );
    });
  }
}
