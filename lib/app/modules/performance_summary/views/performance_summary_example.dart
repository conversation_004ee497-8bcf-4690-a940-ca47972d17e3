import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/lap_performance_model.dart';
import 'performance_summary_view.dart';

class PerformanceSummaryExample extends StatelessWidget {
  const PerformanceSummaryExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample data for demonstration
    final performanceSummary = _createSamplePerformanceSummary();

    return PerformanceSummaryView(summary: performanceSummary);
  }

  // Create sample data for demonstration purposes
  PerformanceSummary _createSamplePerformanceSummary() {
    final event = Event(
      eventName: 'Sepang Track Day',
      eventDate: 'June 15, 2023',
      organizerName: 'Automoment Racing Club',
      trackName: 'Sepang International Circuit',
    );

    final driverName = '<PERSON>';
    final vehicleInfo = 'Honda Civic Type R (2022)';

    // Sample lap data
    final laps = [
      LapPerformance(
        lapNumber: 1,
        lapTime: '03:45.210',
        s1: '53.576',
        s2: '49.845',
        s3: '1:12.471',
        s4: '49.318',
        speed: 135.40,
        timeOfDay: '10:15:23.784',
      ),
      LapPerformance(
        lapNumber: 2,
        lapTime: '03:38.120',
        s1: '51.123',
        s2: '47.845',
        s3: '1:10.471',
        s4: '48.681',
        speed: 142.70,
        timeOfDay: '10:19:08.994',
      ),
      LapPerformance(
        lapNumber: 3,
        lapTime: '03:32.450',
        s1: '49.896',
        s2: '46.545',
        s3: '1:08.471',
        s4: '47.538',
        speed: 148.20,
        timeOfDay: '10:22:47.114',
      ),
      LapPerformance(
        lapNumber: 4,
        lapTime: '03:29.780',
        s1: '48.576',
        s2: '45.845',
        s3: '1:07.471',
        s4: '47.888',
        speed: 152.60,
        timeOfDay: '10:26:19.564',
      ),
      LapPerformance(
        lapNumber: 5,
        lapTime: '03:25.120',
        s1: '47.123',
        s2: '45.545',
        s3: '1:05.471',
        s4: '46.981',
        speed: 155.40,
        timeOfDay: '10:29:49.344',
      ),
      LapPerformance(
        lapNumber: 6,
        lapTime: '03:22.890',
        s1: '46.896',
        s2: '44.845',
        s3: '1:04.471',
        s4: '46.678',
        speed: 158.70,
        timeOfDay: '10:33:14.464',
      ),
      LapPerformance(
        lapNumber: 7,
        lapTime: '03:21.450',
        s1: '46.123',
        s2: '44.545',
        s3: '1:04.471',
        s4: '46.311',
        speed: 160.20,
        timeOfDay: '10:36:37.354',
      ),
      LapPerformance(
        lapNumber: 8,
        lapTime: '03:19.000',
        s1: '45.896',
        s2: '44.545',
        s3: '1:02.471',
        s4: '46.088',
        speed: 162.90,
        timeOfDay: '10:39:58.804',
      ),
      LapPerformance(
        lapNumber: 9,
        lapTime: '03:19.120',
        s1: '45.123',
        s2: '44.845',
        s3: '1:03.471',
        s4: '45.681',
        speed: 165.40,
        timeOfDay: '10:43:17.924',
      ),
      LapPerformance(
        lapNumber: 10,
        lapTime: '03:20.450',
        s1: '45.896',
        s2: '45.545',
        s3: '1:03.471',
        s4: '45.538',
        speed: 163.20,
        timeOfDay: '10:46:38.374',
      ),
      LapPerformance(
        lapNumber: 11,
        lapTime: '23:18.010', // Outlier (pit stop or issue)
        s1: '45.576',
        s2: '45.845',
        s3: '21:00.471', // Issue in sector 3
        s4: '46.118',
        speed: 95.90,
        timeOfDay: '10:49:58.824',
      ),
      LapPerformance(
        lapNumber: 12,
        lapTime: '03:19.450',
        s1: '45.123',
        s2: '44.545',
        s3: '1:03.471',
        s4: '46.311',
        speed: 164.70,
        timeOfDay: '11:13:16.834',
      ),
    ];

    return PerformanceSummary(
      event: event,
      driverName: driverName,
      vehicleInfo: vehicleInfo,
      laps: laps,
    );
  }
}
