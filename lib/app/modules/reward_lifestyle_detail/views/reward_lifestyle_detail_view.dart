import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/constants/app_config.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../controllers/reward_lifestyle_detail_controller.dart';

class RewardLifestyleDetailView
    extends GetView<RewardLifestyleDetailController> {
  const RewardLifestyleDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(''),
        centerTitle: true,
        actions: [
          GestureDetector(
            onTap: () async {
              final box = context.findRenderObject() as RenderBox?;
              double w = box?.size.width ?? 820;
              if (!controller.isSharing.value) {
                controller.isSharing.value = true;
                await Share.share(
                  '${controller.lifestyle.value.title} at ${controller.lifestyle.value.shareLink}',
                  sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100),
                );
                controller.isSharing.value = false;
              }
            },
            child: const Padding(
                padding: EdgeInsets.only(right: 20),
                child: Icon(
                  Icons.share,
                  color: AppColor.primaryColor,
                )),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Obx(() {
          return Column(
            children: [
              // Image
              if (controller.lifestyle.value.image != null)
                Image.network(
                  "${AppConfig.storageUrl}${controller.lifestyle.value.image}",
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: double.infinity,
                      height: 200,
                      color: Colors.grey[200],
                      child: const Icon(Icons.error_outline),
                    );
                  },
                ),

              // Title
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  controller.lifestyle.value.title ?? '',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Description
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                  ),
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    controller.lifestyle.value.description ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      height: 1.5,
                    ),
                  ),
                ),
              ),

              // WebView
              SizedBox(
                width: double.infinity,
                height: controller.webViewHeight.value,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: (controller.lifestyle.value.id == null)
                      ? Container()
                      : WebViewWidget(controller: controller.webViewController),
                ),
              ),
              const SizedBox(height: 22),
            ],
          );
        }),
      ),
    );
  }
}
