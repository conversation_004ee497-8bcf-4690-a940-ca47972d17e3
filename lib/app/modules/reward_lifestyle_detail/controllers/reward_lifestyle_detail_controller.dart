import 'dart:convert';
import 'dart:io';

import 'package:automoment/app/constants/app_config.dart';
import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/helpers/string_util.dart';
import 'package:automoment/app/models/lifestyle_model.dart';
import 'package:automoment/app/services/api_client.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class RewardLifestyleDetailController extends BaseScreenController {
  @override
  String get screenName => 'Reward Lifestyle Detail';

  Rx<Lifestyle> lifestyle = Lifestyle().obs;

  var webViewHeight = 300.0.obs;
  var isSharing = false.obs;

  late WebViewController webViewController;

  UserController userController = Get.put(UserController());

  @override
  Future<void> onInit() async {
    super.onInit();

    initWebViewController();

    var data = Get.arguments;

    if (data is String) {
      debugPrint("from notification or deeplink");
      await fetchLifestyleRewardDetail(data);
    } else {
      lifestyle.value = data;
    }
    debugPrint("preparing to load lifestyle id: ${lifestyle.value.id}");
    webViewController.loadRequest(Uri.parse(
        "${AppConfig.webUrl}lifestyle/${lifestyle.value.id}?${StringUtil.randomString(10)}"));
  }

  initWebViewController() {
    webViewController = WebViewController()
      ..setBackgroundColor(Theme.of(Get.context!).scaffoldBackgroundColor)
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {
            debugPrint("onPageStarted: $url");
            EasyLoading.show(status: 'Please wait...');
          },
          onPageFinished: (String url) async {
            debugPrint("onPageFinished: $url");

            final result = await webViewController.runJavaScriptReturningResult(
                "document.documentElement.scrollHeight;");
            double height = double.tryParse(result.toString()) ?? 300.0;
            webViewHeight.value = height;
            EasyLoading.dismiss();
          },
          onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) async {
            debugPrint("navigationDelegate: ${request.url}");

            if (Platform.isIOS) {
              if (request.url
                      .startsWith("https://app.automoment.com.sg/lifestyle/") ||
                  request.url.startsWith(
                      "https://testapp.automoment.com.sg/lifestyle/")) {
                return NavigationDecision.navigate;
              }

              if (request.url.startsWith('https://youtube.com') ||
                  request.url.startsWith('https://www.youtube.com') ||
                  request.url.startsWith('https://youtu.be') ||
                  request.url.startsWith('https://www.youtu.be')) {
                return NavigationDecision.navigate;
              }
            }

            if (request.url.endsWith(".pdf")) {
              // open external app
              if (!await launchUrl(
                Uri.parse("https://docs.google.com/viewer?url=${request.url}"),
                mode: LaunchMode.externalApplication,
              )) {
                throw 'Could not launch ${request.url}';
              }
              return NavigationDecision.prevent;
            }

            // open external browser
            if (request.url.startsWith("http")) {
              if (!await launchUrl(
                Uri.parse(request.url),
                mode: LaunchMode.externalApplication,
              )) {
                throw 'Could not launch ${request.url}';
              }
              return NavigationDecision.prevent;
            }

            return NavigationDecision.prevent;
          },
        ),
      );
  }

  Future<void> fetchLifestyleRewardDetail(String lifestyleData) async {
    EasyLoading.show(status: 'Loading...');
    try {
      // Parse the JSON string to get the id
      Map<String, dynamic> data = jsonDecode(lifestyleData);
      int lifestyleId = data['id'];

      var response = await ApiClient.getLifestyleRewardDetail(
          userController.getToken(), lifestyleId);
      lifestyle.value = Lifestyle.fromJson(jsonDecode(response)['lifestyle']);
      debugPrint("fetched lifestyle id: ${lifestyle.value.id}");
    } catch (e) {
      debugPrint("error fetching lifestyle: ${e.toString()}");
    } finally {
      EasyLoading.dismiss();
    }
  }
}
