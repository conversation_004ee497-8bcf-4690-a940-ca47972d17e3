import 'dart:convert';
import 'dart:io';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/models/event_photo_model.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:flutter/material.dart';

import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../../helpers/string_util.dart';
import '../../../services/api_client.dart';

class GalleryDetailController extends BaseScreenController {
  @override
  String get screenName => 'Gallery Detail';

  var photos = Get.arguments['photo'];
  var index = Get.arguments['index'];
  var caption = ''.obs;

  String photoUrl = "";
  String photoThumbnailUrl = "";
  String photoWithBestTimeUrl = "";
  String photoWithBestTimeThumbnailUrl = "";
  String photoWithPersonalBestUrl = "";
  String photoWithPersonalBestThumbnailUrl = "";

  @override
  void onInit() {
    super.onInit();
    caption.value = photos[index].caption;
  }

  Future<bool> fetchSharablePhotoUrl() async {
    EasyLoading.show(status: 'loading...');

    Photo photo = photos[index];

    try {
      EasyLoading.dismiss();

      var response = await ApiClient.getSharablePhoto(
          photo.id!, photo.eventId!, Get.put(UserController().getToken()));
      bool success = jsonDecode(response)['success'];

      if (success) {
        photoUrl = jsonDecode(response)['photo'];
        photoThumbnailUrl = jsonDecode(response)['photo_thumbnail'];
        photoWithBestTimeUrl = jsonDecode(response)['photo_with_best_time'];
        photoWithBestTimeThumbnailUrl =
            jsonDecode(response)['photo_with_best_time_thumbnail'];
        photoWithPersonalBestUrl =
            jsonDecode(response)['photo_with_personal_best'];
        photoWithPersonalBestThumbnailUrl =
            jsonDecode(response)['photo_with_personal__best_thumbnail'];
        return true;
      }

      return false;
    } catch ($e) {
      EasyLoading.dismiss();
      debugPrint($e as String?);
      return false;
    }
  }

  Future<void> downloadSharablePhotoUrl(
      String photoWithBestTimeUrl, BuildContext context) async {
    //
    final box = context.findRenderObject() as RenderBox?;
    double w = box?.size.width ?? 820;

    EasyLoading.show(status: 'Loading...');

    // controller.downloadSharablePhotoUrl

    String downloadUrl = photoWithBestTimeUrl;
    String filename = StringUtil.getFilenameFromUrl(downloadUrl);

    final task = DownloadTask(
      url: downloadUrl,
      urlQueryParameters: {'q': 'pizza'},
      filename: filename,
      //headers: {'myHeader': 'value'},
      //directory: '',
      updates: Updates.statusAndProgress, // request status and progress updates
      //requiresWiFi: true,
      retries: 5,
      allowPause: false,
      //metaData: 'data for me'
    );

    final TaskStatusUpdate result = await FileDownloader().download(task,
        onProgress: (progress) => debugPrint('Progress: ${progress * 100}%'),
        onStatus: (status) => debugPrint('Status: $status'));

    EasyLoading.dismiss();

    switch (result.status) {
      case TaskStatus.complete:
        final Directory appDocumentsDir =
            await getApplicationDocumentsDirectory();

        if (Platform.isAndroid) {
          await Share.shareXFiles([XFile("${appDocumentsDir.path}/$filename")],
              text: photos[index].caption,
              subject: photos[index].caption,
              sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
        } else if (Platform.isIOS) {
          Share.shareXFiles([XFile("${appDocumentsDir.path}/$filename")],
              sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
        }

        break;

      case TaskStatus.canceled:
        debugPrint('Download was canceled');
        break;

      case TaskStatus.paused:
        debugPrint('Download was paused');
        break;

      default:
        debugPrint('Download not successful');
    }
  }
}
