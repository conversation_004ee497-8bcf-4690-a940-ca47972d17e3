import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../form/checklist_model.dart';
import '../../../../form/form_widget.dart';
import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/events_form_signed_controller.dart';

class EventsFormSignedView extends StatelessWidget {
  EventsFormSignedView({super.key});

  final EventsFormSignedController controller =
      Get.put(EventsFormSignedController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Indemnity Form', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return SingleChildScrollView(
            child: (!controller.isLoading.value)
                ? Column(
                    children: [
                      FormBuilder(
                        initialData: controller.formJson,
                        index: 0,
                        showIndex: false,
                        submitButtonWidth: 1,
                        submitTextDecoration:
                            const TextStyle(fontSize: 16, color: Colors.white),
                        submitButtonDecoration: BoxDecoration(
                          color: AppColor.primaryButtonColor,
                          borderRadius: BorderRadius.circular(18),
                        ),
                        showIcon: false,
                        onSubmit: (ChecklistModel val) async {},
                        showSubmitButton: false,
                        readOnly: true,
                      ),
                      Image.memory(base64Decode(controller.signatureBase64)),
                      //SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: ElevatedButton(
                            onPressed: () {
                              Get.toNamed(Routes.EVENTS_FORM_UPDATE,
                                  arguments: {
                                    "event": controller.event.value,
                                    "type": controller.type.value.toLowerCase()
                                  });
                            },
                            style: ButtonStyle(
                                foregroundColor: WidgetStateProperty.all<Color>(
                                    Colors.white),
                                backgroundColor: WidgetStateProperty.all<Color>(
                                    AppColor.primaryButtonColor),
                                shape: WidgetStateProperty.all<
                                        RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(18.0),
                                        side: const BorderSide(
                                            color:
                                                AppColor.primaryButtonColor)))),
                            child: Container(
                              padding:
                                  const EdgeInsets.only(left: 20, right: 20),
                              height: 50,
                              child: const Center(
                                child: Text("Update Indenmity Form",
                                    style: TextStyle(fontSize: 16)),
                              ),
                            )),
                      ),
                    ],
                  )
                : Container());
      }),
    );
  }
}
