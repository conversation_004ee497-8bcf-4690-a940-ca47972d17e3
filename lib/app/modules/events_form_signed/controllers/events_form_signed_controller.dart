import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/base_screen_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../../services/api_client.dart';

class EventsFormSignedController extends BaseScreenController {
  @override
  String get screenName => 'View Signed Indemnity Form';
  var event = Event().obs;
  var isLoading = true.obs;
  String msg = "";
  var jsonForm = "";
  String signatureBase64 = "";

  Map<String, dynamic> formJson = {};

  final UserController userController = Get.put(UserController());

  var type = "driver".obs;

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments is Event) {
      event.value = Get.arguments;
    } else {
      event.value = Get.arguments['event'];
      type.value = Get.arguments['type'];
    }

    debugPrint("type: ${type.value}");

    fetchFormJson();
  }

  Future<void> fetchFormJson() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      String response = '{"form": {}, "signature": ""}';

      if (type.value == "driver") {
        response = await ApiClient.getSignedEventForm(
            userController.getToken(), event.value.id!);
      } else if (type.value == "passenger") {
        response = await ApiClient.getSignedEventFormPassenger(
            userController.getToken(), event.value.id!);
      }

      formJson = jsonDecode(response)['form'];
      signatureBase64 = jsonDecode(response)['signature'];
      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<void> reloadForm() async {
    isLoading.value = true;
    await fetchFormJson();
  }
}
