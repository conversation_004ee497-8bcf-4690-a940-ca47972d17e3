import 'dart:convert';
import 'dart:io';

import 'package:automoment/app/constants/app_config.dart';
import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/helpers/debug_helper.dart';
import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../helpers/notification_util.dart';
import '../../../models/news_model.dart';
import '../../../services/api_client.dart';

class NewsDetailController extends BaseScreenController {
  @override
  String get screenName => 'News Detail';

  var news = News().obs;
  late WebViewController webViewController;
  late String webHtml;
  var webViewHeight = 300.0.obs;
  int notificationId = 0;

  final UserController userController = Get.put(UserController());

  @override
  Future<void> onInit() async {
    super.onInit();

    initWebViewController();

    var data = Get.arguments;

    if (data is String) {
      debugPrint("from notification or deeplink");
      String json = data;
      int newsId = -1;

      try {
        newsId = jsonDecode(json)['id'];
        notificationId = jsonDecode(json)['notificationId'];
      } catch (e) {
        debugPrint(e as String?);
      }

      if (newsId == -1) {
        debugPrint("from deeplink");
        newsId = int.parse(data);
        await fetchNews(newsId);
      } else {
        debugPrint("from notification");
        await fetchNews(newsId);

        if (userController.isLoggedIn.value) {
          NotificationUtil.callMarkAsRead(userController.getToken(),
              userController.getUser().id!, notificationId);
        }
      }
    } else {
      debugPrint("from news");
      news.value = data[0];
    }

    webViewController.loadRequest(Uri.parse(
        "${AppConfig.webUrl}news/${news.value.id}?${StringUtil.randomString(10)}"));
  }

  initWebViewController() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {
            DebugHelper.d("onPageStarted: $url");
            EasyLoading.show(status: 'Please wait...');
          },
          onPageFinished: (String url) async {
            DebugHelper.d("onPageFinished: $url");

            final result = await webViewController.runJavaScriptReturningResult(
                "document.documentElement.scrollHeight;");
            double height = double.tryParse(result.toString()) ?? 300.0;
            webViewHeight.value = height;
            EasyLoading.dismiss();
          },
          onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) async {
            DebugHelper.d("navigationDelegate: ${request.url}");

            if (Platform.isIOS) {
              if (request.url
                      .startsWith("https://app.automoment.com.sg/news/") ||
                  request.url
                      .startsWith("https://testapp.automoment.com.sg/news/")) {
                return NavigationDecision.navigate;
              }

              if (request.url.startsWith('https://youtube.com') ||
                  request.url.startsWith('https://www.youtube.com') ||
                  request.url.startsWith('https://youtu.be') ||
                  request.url.startsWith('https://www.youtu.be')) {
                return NavigationDecision.navigate;
              }
            }

            if (request.url.endsWith(".pdf")) {
              // open external app
              if (!await launchUrl(
                Uri.parse("https://docs.google.com/viewer?url=${request.url}"),
                mode: LaunchMode.externalApplication,
              )) {
                throw 'Could not launch ${request.url}';
              }
              return NavigationDecision.prevent;
            }

            // open external browser
            if (request.url.startsWith("http")) {
              if (!await launchUrl(
                Uri.parse(request.url),
                mode: LaunchMode.externalApplication,
              )) {
                throw 'Could not launch ${request.url}';
              }
              return NavigationDecision.prevent;
            }

            return NavigationDecision.prevent;
          },
        ),
      );
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  setWebViewHeight(double h) {
    webViewHeight.value = h;
  }

  Future<void> fetchNews(int id) async {
    try {
      EasyLoading.show(status: 'loading...');
      var response = await ApiClient.getNewsFromId(id);
      EasyLoading.dismiss();
      news.value = News.fromJson(jsonDecode(response)['data']);
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
