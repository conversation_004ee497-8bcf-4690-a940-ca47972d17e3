import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../shared/support_button.dart';
import '../controllers/news_detail_controller.dart';

class NewsDetailView extends StatelessWidget {
  NewsDetailView({super.key});

  final NewsDetailController controller = Get.put(NewsDetailController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          title: const Text(
            "",
            style: TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
          elevation: 1,
          actions: [
            GestureDetector(
              onTap: () {
                final box = context.findRenderObject() as RenderBox?;
                double w = box?.size.width ?? 820;
                Share.share(
                    '${controller.news.value.title} at ${controller.news.value.firebaseDynamicLink}',
                    sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
              },
              child: const Padding(
                  padding: EdgeInsets.only(right: 20),
                  child: Icon(
                    Icons.share,
                    color: Colors.white,
                  )),
            ),
          ],
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(() =>
            ListView(padding: const EdgeInsets.only(top: 0), children: [
              Stack(children: [
                Stack(children: [
                  Container(
                    // color: Colors.red,
                    width: (Get.width),
                    height: 230,
                    margin: const EdgeInsets.all(0.0),
                    child: (controller.news.value.id == null)
                        ? Container()
                        : FadeInImage(
                            placeholder: const AssetImage(
                                'assets/images/image_placeholder.png'),
                            image: NetworkImage(
                                "${AppConfig.storageUrl}${controller.news.value.image}"),
                            fit: BoxFit.cover,
                            imageErrorBuilder: (context, error, stackTrace) {
                              return const Image(
                                fit: BoxFit.cover,
                                image: AssetImage(
                                    'assets/images/image_placeholder.png'),
                              );
                            },
                          ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(
                        top: 180,
                        bottom: AppConfig.defaultPadding * 2,
                        left: AppConfig.defaultPadding * 2,
                        right: AppConfig.defaultPadding * 2),
                    child: Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: const [
                            BoxShadow(
                                offset: Offset(0,
                                    5), //(0,-12) Bóng lên,(0,12) bóng xuống,, tuong tự cho trái phải
                                blurRadius: 10,
                                color: Colors.black26)
                          ]),
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                              left: AppConfig.defaultPadding * 2,
                              right: AppConfig.defaultPadding * 2,
                              top: AppConfig.defaultPadding * 3,
                            ),
                            child: Text(controller.news.value.title ?? "",
                                style: AppTextStyles.titleText
                                    .copyWith(fontSize: 20)),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                              left: (AppConfig.defaultPadding * 2) - 6,
                              right: (AppConfig.defaultPadding * 2) - 6,
                              top: AppConfig.defaultPadding * 2,
                              bottom: AppConfig.defaultPadding * 3,
                            ),
                            child: SizedBox(
                              height: controller.webViewHeight.value,
                              child: (controller.news.value.id == null)
                                  ? Container()
                                  : WebViewWidget(
                                      controller: controller.webViewController),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ]),
              ]),
              //getBottomButton()
            ])));
  }
}
