import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/notification_model.dart';
import '../../../services/api_client.dart';

class NotificationController extends BaseScreenController {
  @override
  String get screenName => 'Notifications';

  var isLoading = false.obs;
  var notificationList = <Notification>[].obs;

  final UserController userController = Get.put(UserController());

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<void> fetchNotifications() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Loading...');
    try {
      String response;

      if (userController.isLoggedIn.value) {
        response = await ApiClient.getNotifications(
            userController.getToken(), userController.user.value.id!);
      } else {
        response = await ApiClient.getNotificationsPublicUser();
      }

      var list = jsonDecode(response)['notifications'];
      var l = <Notification>[];

      list.forEach((dynamic d) {
        var n = Notification.fromJson(d);
        l.add(n);
      });

      notificationList.value = l;

      isLoading.value = false;
      EasyLoading.dismiss();

      userController.unreadNotificationCount.value = 0;
    } catch (e) {
      isLoading.value = false;
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
