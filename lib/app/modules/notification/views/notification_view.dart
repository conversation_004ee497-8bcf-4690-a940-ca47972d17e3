import 'dart:convert';

import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/notification_controller.dart';

class NotificationView extends GetView<NotificationController> {
  const NotificationView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Notification', style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(() {
          return VisibilityDetector(
              key: const Key("notification-screen-key"),
              onVisibilityChanged: (visibilityInfo) {
                if (visibilityInfo.visibleFraction == 1) {
                  controller.fetchNotifications();
                } else {}
              },
              child: listView(context));
        }));
  }

  Widget listView(BuildContext context) {
    return ListView.builder(
      itemCount: controller.notificationList.length,
      itemBuilder: (context, index) {
        return // show ListTile with blue background and bold text
            InkWell(
          onTap: () {
            if (controller.notificationList[index].type == 'news') {
              int newsId = 0;

              try {
                newsId = int.parse(controller.notificationList[index].data!);
              } catch (e) {
                debugPrint(e as String?);
              }

              if (newsId == 0) {
                newsId =
                    jsonDecode(controller.notificationList[index].data!)['id'];
              }

              int notificationId = controller.notificationList[index].id!;
              String param =
                  jsonEncode({'id': newsId, 'notificationId': notificationId});
              Get.toNamed(Routes.HOME_DETAIL, arguments: param);
            } else if (controller.notificationList[index].type == 'event') {
              int eventId = 0;

              try {
                eventId = int.parse(controller.notificationList[index].data!);
              } catch (e) {
                debugPrint(e as String?);
              }

              if (eventId == 0) {
                eventId =
                    jsonDecode(controller.notificationList[index].data!)['id'];
              }

              int notificationId = controller.notificationList[index].id!;
              String param =
                  jsonEncode({'id': eventId, 'notificationId': notificationId});
              Get.toNamed(Routes.EVENTS_DETAIL, arguments: param);
            } else {
              Get.toNamed(Routes.NOTIFICATION_DETAIL,
                  arguments: controller.notificationList[index]);
            }
          },
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: (controller.notificationList[index].read!)
                      ? Colors.white
                      : Colors.grey[100],
                ),
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    Image.asset(
                      (controller.notificationList[index].type == 'news')
                          ? 'assets/images/notif_news.png'
                          : (controller.notificationList[index].type == 'event')
                              ? 'assets/images/notif_event.png'
                              : 'assets/images/notif_info.png',
                      width: 20,
                      height: 20,
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            controller.notificationList[index].title!,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            StringUtil.shortenText(
                                controller.notificationList[index].body!, 100),
                            style: const TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            StringUtil.timeAgo(
                                controller.notificationList[index].createdAt!),
                            style: AppTextStyles.normalText
                                .copyWith(color: Colors.grey[500]),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                height: 1,
                color: Colors.grey[300],
              )
            ],
          ),
        );
      },
    );
  }
}
