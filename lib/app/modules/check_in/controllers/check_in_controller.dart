import 'dart:convert';

import 'package:automoment/app/models/event_modal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';
import '../../events_detail/controllers/events_detail_controller.dart';

class CheckInController extends BaseScreenController {
  @override
  String get screenName => 'Check In';

  var event = Event().obs;
  String checkInMsg = '';
  var qrcodeDetected = false.obs;

  final UserController userController = Get.put(UserController());
  final EventsDetailController eventDetailController =
      Get.put(EventsDetailController());

  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  TextEditingController decalController = TextEditingController();
  TextEditingController transponderController = TextEditingController();
  var isCheckInSuccess = false.obs;

  MobileScannerController scanController = MobileScannerController();
  String qrcode = '';

  @override
  Future<void> onInit() async {
    super.onInit();
    event.value = Get.arguments;
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<bool> checkIn() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.checkIn(
          userController.getToken(),
          userController.user.value.id!,
          event.value.id!,
          decalController.text,
          transponderController.text);
      checkInMsg = jsonDecode(response)['message'];
      EasyLoading.dismiss();

      bool success = jsonDecode(response)['success'] ?? false;

      if (success) {
        updateBookingStatus();

        return true;
      } else {
        return false;
      }
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  updateBookingStatus() {
    eventDetailController.fetchCheckBookingStatus();
  }
}
