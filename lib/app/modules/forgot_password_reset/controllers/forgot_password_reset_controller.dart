import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../services/api_client.dart';

class ForgotPasswordResetController extends BaseScreenController {
  @override
  String get screenName => 'Reset Password: Entering new password';

  TextEditingController passwordController = TextEditingController();
  TextEditingController passwordConfirmController = TextEditingController();
  String msg = "";
  final formKey = GlobalKey<FormState>();
  String code = Get.arguments;

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    passwordController.dispose();
    passwordConfirmController.dispose();
  }

  Future<bool> resetPassword(String code, String password) async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.resetPasswordReset(code, password);
      msg = jsonDecode(response)['message'];
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
