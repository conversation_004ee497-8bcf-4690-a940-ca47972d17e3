import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/view_util.dart';
import '../controllers/booking_controller.dart';

class PlateNumberBottomSheet extends StatelessWidget {
  final dynamic controller;

  const PlateNumberBottomSheet({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              "Please provide your vehicle's license plate number for this event.",
              style: AppTextStyles.normalText.copyWith(height: 1.5),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            width: 150,
            padding: const EdgeInsets.only(bottom: 20),
            child: TextFormField(
              controller: controller.plateNumberController,
              style: AppTextStyles.normalText.copyWith(fontSize: 20),
              textAlign: TextAlign.center,
              decoration: const InputDecoration(hintText: "Plate Number"),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
            child: ElevatedButton(
              onPressed: () => onRegisterPressed(context),
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AppColor.primaryButtonColor,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18.0)),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              ),
              child: const Text("Register Now", style: TextStyle(fontSize: 16)),
            ),
          ),
        ],
      ),
    );
  }

  void onRegisterPressed(BuildContext context) async {
    final controller = Get.find<BookingController>();

    if (controller.plateNumberController.text.isEmpty) {
      ViewUtil.showErrorDialog(context, "Please enter your vehicle's license plate number");
    } else {
      await controller.submitBooking(controller, context);
    }
  }
}