import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/lap_performance_model.dart';
import '../../../models/performance_comparison_model.dart';
import '../controllers/performance_comparison_controller.dart';

class PerformanceComparisonView
    extends GetView<PerformanceComparisonController> {
  const PerformanceComparisonView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Progress3'),
        backgroundColor: AppColor.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColor.primaryColor,
              ),
            );
          }

          if (controller.performanceComparison.value == null) {
            return const Center(
              child: Text(
                'No performance data available',
                style: TextStyle(color: AppColor.kTextGrayColor),
              ),
            );
          }

          final comparison = controller.performanceComparison.value!;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTrackHeader(comparison),
                _buildProgressSummary(comparison),
                _buildLapTimeProgressionChart(context, comparison),
                _buildSectorImprovements(comparison),
                _buildAllEventsComparison(comparison),
              ],
            ),
          );
        }),
      ),
    );
  }

  // Section 1: Track & Driver Identification
  Widget _buildTrackHeader(PerformanceComparison comparison) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: AppColor.primaryColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            comparison.trackName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${comparison.eventSummaries.length} Events',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Icon(Icons.person, color: Colors.white70, size: 18),
              const SizedBox(width: 8),
              Text(
                comparison.driverName,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.directions_car, color: Colors.white70, size: 18),
              const SizedBox(width: 8),
              Text(
                comparison.vehicleInfo,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Section 2: Progress Summary
  Widget _buildProgressSummary(PerformanceComparison comparison) {
    final improvements = comparison.improvements;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Your Progress',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildImprovementCard(
                title: 'Best Lap',
                value:
                    '${improvements['bestLapImprovement'].toStringAsFixed(1)}%',
                subtitle: 'Faster than first event',
                icon: Icons.timer,
                color: AppColor.secondaryColor,
                isPositive: improvements['bestLapImprovement'] > 0,
              ),
              _buildImprovementCard(
                title: 'Avg. Lap Time',
                value:
                    '${improvements['avgLapImprovement'].toStringAsFixed(1)}%',
                subtitle: 'More consistent',
                icon: Icons.av_timer,
                color: AppColor.primaryColor,
                isPositive: improvements['avgLapImprovement'] > 0,
              ),
              _buildImprovementCard(
                title: 'Top Speed',
                value:
                    '${improvements['topSpeedImprovement'].toStringAsFixed(1)}%',
                subtitle: 'Increased max speed',
                icon: Icons.speed,
                color: AppColor.redButtonColor,
                isPositive: improvements['topSpeedImprovement'] > 0,
              ),
              _buildImprovementCard(
                title: 'Best Sector',
                value:
                    '${(improvements['bestSectorsImprovement'][comparison.getMostImprovedSector()]).toStringAsFixed(1)}%',
                subtitle:
                    'Sector ${comparison.getMostImprovedSector().substring(1)}',
                icon: Icons.timeline,
                color: AppColor.kTextGrayColor,
                isPositive: improvements['bestSectorsImprovement']
                        [comparison.getMostImprovedSector()] >
                    0,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImprovementCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isPositive,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(width: 4),
              Icon(
                isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                color: isPositive ? Colors.green : Colors.red,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          if (subtitle.isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: AppColor.kTextLightColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Section 3: Lap Time Progression Chart
  Widget _buildLapTimeProgressionChart(
      BuildContext context, PerformanceComparison comparison) {
    final eventSummaries = comparison.eventSummaries;

    if (eventSummaries.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No event data available for chart',
            style: TextStyle(color: AppColor.kTextGrayColor),
          ),
        ),
      );
    }

    // Prepare data for the chart
    final spots = eventSummaries.map((summary) {
      // Convert event date to x-axis value (days since first event)
      final eventDate = summary.event.eventDate;
      final eventIndex = eventSummaries.indexOf(summary).toDouble();

      // Convert best lap time to seconds for y-axis
      final bestLapSeconds = summary.bestLapTime != null
          ? _convertLapTimeToSeconds(summary.bestLapTime!)
          : 0.0;

      return FlSpot(eventIndex, bestLapSeconds);
    }).toList();

    // Find min and max values for better scaling
    double minY = double.infinity;
    double maxY = 0;
    for (var spot in spots) {
      if (spot.y < minY && spot.y > 0) minY = spot.y;
      if (spot.y > maxY) maxY = spot.y;
    }

    // Add some padding to the min/max values
    final padding = (maxY - minY) * 0.1;
    minY = (minY - padding).clamp(0, double.infinity);
    maxY = maxY + padding;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Best Lap Time Progression',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Track your improvement over time',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  horizontalInterval: 10, // 10 seconds interval
                  verticalInterval: 1, // 1 event interval
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          // Show abbreviated event name or date
                          final eventName =
                              eventSummaries[index].event.eventName;
                          final shortName =
                              eventName != null && eventName.length > 10
                                  ? '${eventName.substring(0, 10)}...'
                                  : eventName ?? 'Event ${index + 1}';

                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              shortName,
                              style: const TextStyle(
                                color: AppColor.kTextGrayColor,
                                fontSize: 10,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    axisNameWidget: const Text(
                      'Events',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 10, // 10 seconds interval
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        final minutes = (value / 60).floor();
                        final seconds = (value % 60).floor();
                        return SideTitleWidget(
                          axisSide: meta.axisSide,
                          child: Text(
                            '$minutes:${seconds.toString().padLeft(2, '0')}',
                            style: const TextStyle(
                              color: AppColor.kTextGrayColor,
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
                    axisNameWidget: const Text(
                      'Best Lap Time (min:sec)',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: AppColor.kLightBgColor),
                ),
                minX: 0,
                maxX: eventSummaries.length.toDouble() - 1,
                minY: minY,
                maxY: maxY,
                lineBarsData: [
                  LineChartBarData(
                    spots: spots,
                    isCurved: true,
                    color: AppColor.primaryColor,
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 5,
                          color: AppColor.primaryColor,
                          strokeWidth: 1,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color: AppColor.primaryColor.withOpacity(0.2),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  touchTooltipData: LineTouchTooltipData(
                    tooltipBgColor: Colors.white,
                    tooltipRoundedRadius: 8,
                    getTooltipItems: (List<LineBarSpot> touchedSpots) {
                      return touchedSpots.map((spot) {
                        final index = spot.x.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          final summary = eventSummaries[index];
                          final eventName =
                              summary.event.eventName ?? 'Event ${index + 1}';
                          final eventDate = summary.event.eventDate ?? 'N/A';
                          final lapTime = summary.bestLapTime ?? 'N/A';

                          return LineTooltipItem(
                            '$eventName\n$eventDate\nBest Lap: $lapTime',
                            const TextStyle(
                              color: AppColor.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Section 4: Sector Improvements
  Widget _buildSectorImprovements(PerformanceComparison comparison) {
    final sectorImprovements = comparison.improvements['bestSectorsImprovement']
        as Map<String, double>;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sector Improvements',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Your progress in each track sector',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildSectorProgressBars(sectorImprovements),
        ],
      ),
    );
  }

  Widget _buildSectorProgressBars(Map<String, double> sectorImprovements) {
    return Column(
      children: [
        _buildSectorProgressBar('Sector 1', sectorImprovements['s1'] ?? 0.0,
            AppColor.secondaryColor),
        const SizedBox(height: 12),
        _buildSectorProgressBar(
            'Sector 2', sectorImprovements['s2'] ?? 0.0, AppColor.primaryColor),
        const SizedBox(height: 12),
        _buildSectorProgressBar('Sector 3', sectorImprovements['s3'] ?? 0.0,
            AppColor.redButtonColor),
        const SizedBox(height: 12),
        _buildSectorProgressBar(
            'Sector 4', sectorImprovements['s4'] ?? 0.0, Colors.purple),
      ],
    );
  }

  Widget _buildSectorProgressBar(
      String sectorName, double improvement, Color color) {
    // Cap the progress bar at 100% for visual purposes
    final progressValue = (improvement / 100).clamp(0.0, 1.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              sectorName,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColor.kTextColor,
              ),
            ),
            Row(
              children: [
                Text(
                  '${improvement.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: improvement > 0 ? Colors.green : Colors.red,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  improvement > 0 ? Icons.arrow_upward : Icons.arrow_downward,
                  color: improvement > 0 ? Colors.green : Colors.red,
                  size: 14,
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 6),
        LinearProgressIndicator(
          value: progressValue,
          backgroundColor: Colors.grey.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }

  // Section 5: All Events Comparison
  Widget _buildAllEventsComparison(PerformanceComparison comparison) {
    final eventSummaries = comparison.eventSummaries;

    // Sort events by date
    final sortedEvents = List<PerformanceSummary>.from(eventSummaries);
    sortedEvents.sort((a, b) {
      if (a.event.eventDate == null || b.event.eventDate == null) {
        return 0;
      }
      return a.event.eventDate!.compareTo(b.event.eventDate!);
    });

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'All Events Comparison',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Compare your performance across all events',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildFullComparisonTable(sortedEvents),
        ],
      ),
    );
  }

  Widget _buildFullComparisonTable(List<PerformanceSummary> events) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.withOpacity(0.1)),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFullComparisonHeader(events),
            _buildFullComparisonRow(
                'Best Lap', events, (event) => event.bestLapTime ?? '-', false),
            _buildFullComparisonRow('Avg. Lap', events,
                (event) => event.averageLapTime ?? '-', false),
            _buildFullComparisonRow(
                'Top Speed',
                events,
                (event) => '${event.topSpeed?.toStringAsFixed(1) ?? '-'} km/h',
                true),
            _buildFullComparisonRow(
                'Sector 1', events, (event) => event.bestS1 ?? '-', false),
            _buildFullComparisonRow(
                'Sector 2', events, (event) => event.bestS2 ?? '-', false),
            _buildFullComparisonRow(
                'Sector 3', events, (event) => event.bestS3 ?? '-', false),
            _buildFullComparisonRow(
                'Sector 4', events, (event) => event.bestS4 ?? '-', false),
          ],
        ),
      ),
    );
  }

  Widget _buildFullComparisonHeader(List<PerformanceSummary> events) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: const BoxDecoration(
        color: AppColor.primaryColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: IntrinsicWidth(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // First column for metric labels
            const SizedBox(
              width: 100,
              child: Text(
                'Metric',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            // Event columns
            ...events.map((event) {
              final eventName = event.event.eventName ?? 'Event';
              final eventDate = event.event.eventDate ?? '';

              return Container(
                width: 120,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Column(
                  children: [
                    Text(
                      eventName,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      eventDate,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildFullComparisonRow(String label, List<PerformanceSummary> events,
      String Function(PerformanceSummary) valueGetter, bool higherIsBetter) {
    // Get values for all events
    final values = events.map(valueGetter).toList();

    // Find the best value
    String bestValue = values.first;
    double bestValueInSeconds = higherIsBetter
        ? double.tryParse(bestValue.replaceAll(' km/h', '')) ?? 0
        : _convertLapTimeToSeconds(bestValue);

    for (int i = 1; i < values.length; i++) {
      final currentValue = values[i];
      final currentValueInSeconds = higherIsBetter
          ? double.tryParse(currentValue.replaceAll(' km/h', '')) ?? 0
          : _convertLapTimeToSeconds(currentValue);

      if (higherIsBetter) {
        if (currentValueInSeconds > bestValueInSeconds) {
          bestValue = currentValue;
          bestValueInSeconds = currentValueInSeconds;
        }
      } else {
        if (currentValueInSeconds < bestValueInSeconds &&
            currentValueInSeconds > 0) {
          bestValue = currentValue;
          bestValueInSeconds = currentValueInSeconds;
        }
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.1)),
        ),
      ),
      child: IntrinsicWidth(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Label column
            SizedBox(
              width: 100,
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColor.kTextColor,
                ),
              ),
            ),
            // Value columns for each event
            ...events.asMap().entries.map((entry) {
              final index = entry.key;
              final event = entry.value;
              final value = valueGetter(event);

              // Determine if this is the best value
              final isBest = value == bestValue && value != '-';

              // Determine if this is better than the previous event (if not the first event)
              bool isImproved = false;
              if (index > 0) {
                final previousValue = valueGetter(events[index - 1]);

                if (higherIsBetter) {
                  // For top speed, higher is better
                  final prev =
                      double.tryParse(previousValue.replaceAll(' km/h', '')) ??
                          0;
                  final curr =
                      double.tryParse(value.replaceAll(' km/h', '')) ?? 0;
                  isImproved = curr > prev;
                } else {
                  // For times, lower is better
                  final prev = _convertLapTimeToSeconds(previousValue);
                  final curr = _convertLapTimeToSeconds(value);
                  isImproved = curr < prev && curr > 0;
                }
              }

              return Container(
                width: 120,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      value,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight:
                            isBest ? FontWeight.bold : FontWeight.normal,
                        color: isBest
                            ? AppColor.primaryColor
                            : (isImproved
                                ? Colors.green
                                : AppColor.kTextGrayColor),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (isImproved && index > 0) ...[
                      const SizedBox(width: 4),
                      Icon(
                        higherIsBetter
                            ? Icons.arrow_upward
                            : Icons.arrow_downward,
                        color: Colors.green,
                        size: 14,
                      ),
                    ],
                    if (isBest) ...[
                      const SizedBox(width: 4),
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 14,
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  double _convertLapTimeToSeconds(String lapTime) {
    try {
      // Handle formats like "3:45.210" or "45.896"
      if (lapTime.contains(':')) {
        final parts = lapTime.split(':');
        final minutes = int.parse(parts[0]);
        final secondsPart = parts[1].split('.');
        final seconds = double.parse(secondsPart[0]);
        final milliseconds =
            secondsPart.length > 1 ? double.parse(secondsPart[1]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      } else if (lapTime.contains('.')) {
        final parts = lapTime.split('.');
        final seconds = double.parse(parts[0]);
        final milliseconds =
            parts.length > 1 ? double.parse(parts[1]) / 1000 : 0;
        return seconds + milliseconds;
      }
    } catch (e) {
      print('Error parsing time: $e');
    }
    return 0;
  }
}
