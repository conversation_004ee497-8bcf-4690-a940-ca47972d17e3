import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../models/lap_performance_model.dart';
import '../../../models/performance_comparison_model.dart';
import '../controllers/performance_comparison_controller.dart';

// Helper class to store row data
class RowData {
  final String label;
  final List<CellData> cells;
  final bool higherIsBetter;

  RowData(this.label, this.cells, this.higherIsBetter);
}

// Helper class to store cell data
class CellData {
  final String value;
  final bool isBest;
  final bool isImproved;
  final bool isDeclined;

  CellData(this.value, this.isBest, this.isImproved, this.isDeclined);
}

class PerformanceComparisonView
    extends GetView<PerformanceComparisonController> {
  const PerformanceComparisonView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Progress'),
        backgroundColor: AppColor.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColor.primaryColor,
              ),
            );
          }

          if (controller.performanceComparison.value == null) {
            return const Center(
              child: Text(
                'No performance data available',
                style: TextStyle(color: AppColor.kTextGrayColor),
              ),
            );
          }

          final comparison = controller.performanceComparison.value!;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTrackHeader(comparison),
                _buildLapTimeProgressionChart(context, comparison),
                _buildSectorProgressionChart(context, comparison),
                _buildSpeedProgressionChart(context, comparison),
                _buildAllEventsComparison(comparison),
              ],
            ),
          );
        }),
      ),
    );
  }

  // Section 1: Track & Driver Identification
  Widget _buildTrackHeader(PerformanceComparison comparison) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: AppColor.primaryColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            comparison.trackName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${comparison.eventSummaries.length} Events',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Icon(Icons.person, color: Colors.white70, size: 18),
              const SizedBox(width: 8),
              Text(
                comparison.driverName,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.directions_car, color: Colors.white70, size: 18),
              const SizedBox(width: 8),
              Text(
                comparison.vehicleInfo,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Section 2: Lap Time Progression Chart
  Widget _buildLapTimeProgressionChart(
      BuildContext context, PerformanceComparison comparison) {
    final eventSummaries = comparison.eventSummaries;

    if (eventSummaries.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No event data available for chart',
            style: TextStyle(color: AppColor.kTextGrayColor),
          ),
        ),
      );
    }

    // Prepare data for the chart
    final spots = eventSummaries.map((summary) {
      final eventIndex = eventSummaries.indexOf(summary).toDouble();

      // Convert best lap time to seconds for y-axis
      final bestLapSeconds = summary.bestLapTime != null
          ? _convertLapTimeToSeconds(summary.bestLapTime!)
          : 0.0;

      return FlSpot(eventIndex, bestLapSeconds);
    }).toList();

    // Find min and max values for better scaling
    double minY = double.infinity;
    double maxY = 0;
    for (var spot in spots) {
      if (spot.y < minY && spot.y > 0) minY = spot.y;
      if (spot.y > maxY) maxY = spot.y;
    }

    // Add some padding to the min/max values
    final padding = (maxY - minY) * 0.1;
    minY = (minY - padding).clamp(0, double.infinity);
    maxY = maxY + padding;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Best Lap Time Progression',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Track your improvement over time',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  horizontalInterval: 10, // 10 seconds interval
                  verticalInterval: 1, // 1 event interval
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          // Show abbreviated event name or date
                          final eventName =
                              eventSummaries[index].event.eventName;
                          final shortName =
                              eventName != null && eventName.length > 10
                                  ? '${eventName.substring(0, 10)}...'
                                  : eventName ?? 'Event ${index + 1}';

                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              shortName,
                              style: const TextStyle(
                                color: AppColor.kTextGrayColor,
                                fontSize: 10,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    axisNameWidget: const Text(
                      'Events',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 10, // 10 seconds interval
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        final minutes = (value / 60).floor();
                        final seconds = (value % 60).floor();
                        return SideTitleWidget(
                          axisSide: meta.axisSide,
                          child: Text(
                            '$minutes:${seconds.toString().padLeft(2, '0')}',
                            style: const TextStyle(
                              color: AppColor.kTextGrayColor,
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
                    axisNameWidget: const Text(
                      'Best Lap Time (min:sec)',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: AppColor.kLightBgColor),
                ),
                minX: 0,
                maxX: eventSummaries.length.toDouble() - 1,
                minY: minY,
                maxY: maxY,
                lineBarsData: [
                  LineChartBarData(
                    spots: spots,
                    isCurved: true,
                    color: AppColor.primaryColor,
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 5,
                          color: AppColor.primaryColor,
                          strokeWidth: 1,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color: AppColor.primaryColor.withAlpha(50),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  touchTooltipData: LineTouchTooltipData(
                    tooltipBgColor: Colors.white,
                    tooltipRoundedRadius: 8,
                    getTooltipItems: (List<LineBarSpot> touchedSpots) {
                      return touchedSpots.map((spot) {
                        final index = spot.x.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          final summary = eventSummaries[index];
                          final eventName =
                              summary.event.eventName ?? 'Event ${index + 1}';
                          final eventDate = summary.event.eventDate ?? 'N/A';
                          final lapTime = summary.bestLapTime ?? 'N/A';

                          return LineTooltipItem(
                            '$eventName\n$eventDate\nBest Lap: $lapTime',
                            const TextStyle(
                              color: AppColor.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to convert sector time to seconds
  double _convertSectorTimeToSeconds(String sectorTime) {
    try {
      // Handle formats like "1:02.471" or "40.896"
      if (sectorTime.contains(':')) {
        final parts = sectorTime.split(':');
        final minutes = int.parse(parts[0]);
        final secondsPart = parts[1].split('.');
        final seconds = double.parse(secondsPart[0]);
        final milliseconds =
            secondsPart.length > 1 ? double.parse(secondsPart[1]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      } else {
        final parts = sectorTime.split('.');
        final seconds = double.parse(parts[0]);
        final milliseconds =
            parts.length > 1 ? double.parse(parts[1]) / 1000 : 0;
        return seconds + milliseconds;
      }
    } catch (e) {
      print('Error parsing sector time: $e');
    }
    return 0;
  }

  // Section 3: Sector Progression Chart
  Widget _buildSectorProgressionChart(
      BuildContext context, PerformanceComparison comparison) {
    final eventSummaries = comparison.eventSummaries;

    if (eventSummaries.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No event data available for chart',
            style: TextStyle(color: AppColor.kTextGrayColor),
          ),
        ),
      );
    }

    // Prepare data for the chart - one line for each sector
    final s1Spots = <FlSpot>[];
    final s2Spots = <FlSpot>[];
    final s3Spots = <FlSpot>[];
    final s4Spots = <FlSpot>[];

    for (int i = 0; i < eventSummaries.length; i++) {
      final summary = eventSummaries[i];
      final eventIndex = i.toDouble();

      // Convert sector times to seconds for y-axis
      if (summary.bestS1 != null) {
        final s1Seconds = _convertSectorTimeToSeconds(summary.bestS1!);
        if (s1Seconds > 0) {
          s1Spots.add(FlSpot(eventIndex, s1Seconds));
        }
      }

      if (summary.bestS2 != null) {
        final s2Seconds = _convertSectorTimeToSeconds(summary.bestS2!);
        if (s2Seconds > 0) {
          s2Spots.add(FlSpot(eventIndex, s2Seconds));
        }
      }

      if (summary.bestS3 != null) {
        final s3Seconds = _convertSectorTimeToSeconds(summary.bestS3!);
        if (s3Seconds > 0) {
          s3Spots.add(FlSpot(eventIndex, s3Seconds));
        }
      }

      if (summary.bestS4 != null) {
        final s4Seconds = _convertSectorTimeToSeconds(summary.bestS4!);
        if (s4Seconds > 0) {
          s4Spots.add(FlSpot(eventIndex, s4Seconds));
        }
      }
    }

    // Find min and max values for better scaling
    double minY = double.infinity;
    double maxY = 0;

    for (var spots in [s1Spots, s2Spots, s3Spots, s4Spots]) {
      for (var spot in spots) {
        if (spot.y < minY && spot.y > 0) minY = spot.y;
        if (spot.y > maxY) maxY = spot.y;
      }
    }

    // Add some padding to the min/max values
    final padding = (maxY - minY) * 0.1;
    minY = (minY - padding).clamp(0, double.infinity);
    maxY = maxY + padding;

    // Define colors for each sector
    const s1Color = Color(0xFF4CAF50); // Green
    const s2Color = Color(0xFF2196F3); // Blue
    const s3Color = Color(0xFFFF9800); // Orange
    const s4Color = Color(0xFFE91E63); // Pink

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sector Time Progression',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Track your sector improvements over time',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          // Legend for the sectors
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLegendItem('Sector 1', s1Color),
              const SizedBox(width: 16),
              _buildLegendItem('Sector 2', s2Color),
              const SizedBox(width: 16),
              _buildLegendItem('Sector 3', s3Color),
              const SizedBox(width: 16),
              _buildLegendItem('Sector 4', s4Color),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  horizontalInterval: 5, // 5 seconds interval
                  verticalInterval: 1, // 1 event interval
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          // Show abbreviated event name or date
                          final eventName =
                              eventSummaries[index].event.eventName;
                          final shortName =
                              eventName != null && eventName.length > 10
                                  ? '${eventName.substring(0, 10)}...'
                                  : eventName ?? 'Event ${index + 1}';

                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              shortName,
                              style: const TextStyle(
                                color: AppColor.kTextGrayColor,
                                fontSize: 10,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    axisNameWidget: const Text(
                      'Events',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 5, // 5 seconds interval
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        // For sector times, we'll just show seconds
                        return SideTitleWidget(
                          axisSide: meta.axisSide,
                          child: Text(
                            value.toStringAsFixed(1),
                            style: const TextStyle(
                              color: AppColor.kTextGrayColor,
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
                    axisNameWidget: const Text(
                      'Sector Time (seconds)',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: AppColor.kLightBgColor),
                ),
                minX: 0,
                maxX: eventSummaries.length.toDouble() - 1,
                minY: minY,
                maxY: maxY,
                lineBarsData: [
                  // Sector 1 Line
                  if (s1Spots.isNotEmpty)
                    LineChartBarData(
                      spots: s1Spots,
                      isCurved: true,
                      color: s1Color,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: s1Color,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                  // Sector 2 Line
                  if (s2Spots.isNotEmpty)
                    LineChartBarData(
                      spots: s2Spots,
                      isCurved: true,
                      color: s2Color,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: s2Color,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                  // Sector 3 Line
                  if (s3Spots.isNotEmpty)
                    LineChartBarData(
                      spots: s3Spots,
                      isCurved: true,
                      color: s3Color,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: s3Color,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                  // Sector 4 Line
                  if (s4Spots.isNotEmpty)
                    LineChartBarData(
                      spots: s4Spots,
                      isCurved: true,
                      color: s4Color,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: s4Color,
                            strokeWidth: 1,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                    ),
                ],
                lineTouchData: LineTouchData(
                  touchTooltipData: LineTouchTooltipData(
                    tooltipBgColor: Colors.white,
                    tooltipRoundedRadius: 8,
                    getTooltipItems: (List<LineBarSpot> touchedSpots) {
                      return touchedSpots.map((spot) {
                        final index = spot.x.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          final summary = eventSummaries[index];
                          final eventName =
                              summary.event.eventName ?? 'Event ${index + 1}';

                          String sectorName = '';
                          String sectorTime = '';
                          Color sectorColor = AppColor.primaryColor;

                          // Determine which sector was touched based on the line color
                          if (spot.barIndex == 0 && s1Spots.isNotEmpty) {
                            sectorName = 'Sector 1';
                            sectorTime = summary.bestS1 ?? 'N/A';
                            sectorColor = s1Color;
                          } else if (spot.barIndex == 1 ||
                              (spot.barIndex == 0 &&
                                  s1Spots.isEmpty &&
                                  s2Spots.isNotEmpty)) {
                            sectorName = 'Sector 2';
                            sectorTime = summary.bestS2 ?? 'N/A';
                            sectorColor = s2Color;
                          } else if (spot.barIndex == 2 ||
                              (spot.barIndex == 1 &&
                                  s1Spots.isEmpty &&
                                  s2Spots.isEmpty &&
                                  s3Spots.isNotEmpty)) {
                            sectorName = 'Sector 3';
                            sectorTime = summary.bestS3 ?? 'N/A';
                            sectorColor = s3Color;
                          } else {
                            sectorName = 'Sector 4';
                            sectorTime = summary.bestS4 ?? 'N/A';
                            sectorColor = s4Color;
                          }

                          return LineTooltipItem(
                            '$eventName\n$sectorName: $sectorTime',
                            TextStyle(
                              color: sectorColor,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build a legend item
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColor.kTextGrayColor,
          ),
        ),
      ],
    );
  }

  // Section 4: Speed Progression Chart
  Widget _buildSpeedProgressionChart(
      BuildContext context, PerformanceComparison comparison) {
    final eventSummaries = comparison.eventSummaries;

    if (eventSummaries.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No event data available for chart',
            style: TextStyle(color: AppColor.kTextGrayColor),
          ),
        ),
      );
    }

    // Prepare data for the chart
    final speedSpots = <FlSpot>[];

    for (int i = 0; i < eventSummaries.length; i++) {
      final summary = eventSummaries[i];
      final eventIndex = i.toDouble();

      // Add top speed data point
      if (summary.topSpeed != null && summary.topSpeed! > 0) {
        speedSpots.add(FlSpot(eventIndex, summary.topSpeed!));
      }
    }

    if (speedSpots.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            'No speed data available for chart',
            style: TextStyle(color: AppColor.kTextGrayColor),
          ),
        ),
      );
    }

    // Find min and max values for better scaling
    double minY = double.infinity;
    double maxY = 0;

    for (var spot in speedSpots) {
      if (spot.y < minY && spot.y > 0) minY = spot.y;
      if (spot.y > maxY) maxY = spot.y;
    }

    // Add some padding to the min/max values
    final padding = (maxY - minY) * 0.1;
    minY = (minY - padding).clamp(0, double.infinity);
    maxY = maxY + padding;

    // Define color for speed chart
    const speedColor = Color(0xFFE53935); // Red

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Top Speed Progression',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Track your speed improvements over time',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  horizontalInterval: 10, // 10 km/h interval
                  verticalInterval: 1, // 1 event interval
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          // Show abbreviated event name or date
                          final eventName =
                              eventSummaries[index].event.eventName;
                          final shortName =
                              eventName != null && eventName.length > 10
                                  ? '${eventName.substring(0, 10)}...'
                                  : eventName ?? 'Event ${index + 1}';

                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              shortName,
                              style: const TextStyle(
                                color: AppColor.kTextGrayColor,
                                fontSize: 10,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    axisNameWidget: const Text(
                      'Events',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 10, // 10 km/h interval
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        return SideTitleWidget(
                          axisSide: meta.axisSide,
                          child: Text(
                            value.toInt().toString(),
                            style: const TextStyle(
                              color: AppColor.kTextGrayColor,
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
                    axisNameWidget: const Text(
                      'Speed (km/h)',
                      style: TextStyle(
                        color: AppColor.kTextGrayColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: AppColor.kLightBgColor),
                ),
                minX: 0,
                maxX: eventSummaries.length.toDouble() - 1,
                minY: minY,
                maxY: maxY,
                lineBarsData: [
                  LineChartBarData(
                    spots: speedSpots,
                    isCurved: true,
                    color: speedColor,
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 5,
                          color: speedColor,
                          strokeWidth: 1,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color: speedColor.withAlpha(50),
                    ),
                  ),
                ],
                lineTouchData: LineTouchData(
                  touchTooltipData: LineTouchTooltipData(
                    tooltipBgColor: Colors.white,
                    tooltipRoundedRadius: 8,
                    getTooltipItems: (List<LineBarSpot> touchedSpots) {
                      return touchedSpots.map((spot) {
                        final index = spot.x.toInt();
                        if (index >= 0 && index < eventSummaries.length) {
                          final summary = eventSummaries[index];
                          final eventName =
                              summary.event.eventName ?? 'Event ${index + 1}';
                          final eventDate = summary.event.eventDate ?? 'N/A';
                          final topSpeed = summary.topSpeed != null
                              ? '${summary.topSpeed!.toStringAsFixed(1)} km/h'
                              : 'N/A';
                          final lapNumber = summary.topSpeedLapNumber != null
                              ? 'Lap ${summary.topSpeedLapNumber}'
                              : '';

                          return LineTooltipItem(
                            '$eventName\n$eventDate\nTop Speed: $topSpeed\n$lapNumber',
                            const TextStyle(
                              color: speedColor,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }
                        return null;
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Section 5: All Events Comparison
  Widget _buildAllEventsComparison(PerformanceComparison comparison) {
    final eventSummaries = comparison.eventSummaries;

    // Sort events by date
    final sortedEvents = List<PerformanceSummary>.from(eventSummaries);
    sortedEvents.sort((a, b) {
      if (a.event.eventDate == null || b.event.eventDate == null) {
        return 0;
      }
      return a.event.eventDate!.compareTo(b.event.eventDate!);
    });

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'All Events Comparison',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColor.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Compare your performance across all events',
            style: TextStyle(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildComparisonTable(sortedEvents),
        ],
      ),
    );
  }

  // Prepare data for a table row
  RowData _buildRowData(String label, List<PerformanceSummary> events,
      String Function(PerformanceSummary) valueGetter, bool higherIsBetter) {
    // Get values for all events
    final values = events.map(valueGetter).toList();

    // Find the best value
    String bestValue = '-';
    double bestValueInSeconds = higherIsBetter ? 0 : double.infinity;

    for (int i = 0; i < values.length; i++) {
      final currentValue = values[i];
      if (currentValue == '-') continue;

      final currentValueInSeconds = higherIsBetter
          ? double.tryParse(currentValue.replaceAll(' km/h', '')) ?? 0
          : _convertLapTimeToSeconds(currentValue);

      if (currentValueInSeconds <= 0) continue;

      if (higherIsBetter) {
        if (currentValueInSeconds > bestValueInSeconds) {
          bestValue = currentValue;
          bestValueInSeconds = currentValueInSeconds;
        }
      } else {
        if (currentValueInSeconds < bestValueInSeconds) {
          bestValue = currentValue;
          bestValueInSeconds = currentValueInSeconds;
        }
      }
    }

    // Create cell data for each event
    final cells = events.asMap().entries.map((entry) {
      final index = entry.key;
      final event = entry.value;
      final value = valueGetter(event);

      // Determine if this is the best value
      final isBest = value == bestValue && value != '-';

      // Determine if this is better than the previous event (if not the first event)
      bool isImproved = false;
      bool isDeclined = false;

      if (index > 0 && value != '-') {
        final previousValue = valueGetter(events[index - 1]);
        if (previousValue != '-') {
          if (higherIsBetter) {
            // For top speed, higher is better
            final prev =
                double.tryParse(previousValue.replaceAll(' km/h', '')) ?? 0;
            final curr = double.tryParse(value.replaceAll(' km/h', '')) ?? 0;
            if (prev > 0 && curr > 0) {
              isImproved = curr > prev;
              isDeclined = curr < prev;
            }
          } else {
            // For times, lower is better
            final prev = _convertLapTimeToSeconds(previousValue);
            final curr = _convertLapTimeToSeconds(value);
            if (prev > 0 && curr > 0) {
              isImproved = curr < prev;
              isDeclined = curr > prev;
            }
          }
        }
      }

      return CellData(value, isBest, isImproved, isDeclined);
    }).toList();

    return RowData(label, cells, higherIsBetter);
  }

  Widget _buildComparisonTable(List<PerformanceSummary> events) {
    // Sort events chronologically by date for proper comparison
    final sortedEvents = List<PerformanceSummary>.from(events);
    sortedEvents.sort((a, b) {
      if (a.event.eventDate == null || b.event.eventDate == null) {
        return 0;
      }

      // Parse dates for proper chronological sorting
      try {
        final dateA = _parseEventDate(a.event.eventDate!);
        final dateB = _parseEventDate(b.event.eventDate!);
        return dateA.compareTo(dateB);
      } catch (e) {
        // Fallback to string comparison if date parsing fails
        return a.event.eventDate!.compareTo(b.event.eventDate!);
      }
    });

    // Prepare data for the table using chronologically sorted events
    final rows = [
      _buildRowData(
          'Best Lap', sortedEvents, (event) => event.bestLapTime ?? '-', false),
      _buildRowData('Avg. Lap', sortedEvents,
          (event) => event.averageLapTime ?? '-', false),
      _buildRowData('Top Speed', sortedEvents,
          (event) => '${event.topSpeed?.toStringAsFixed(1) ?? '-'} km/h', true),
      _buildRowData(
          'Sector 1', sortedEvents, (event) => event.bestS1 ?? '-', false),
      _buildRowData(
          'Sector 2', sortedEvents, (event) => event.bestS2 ?? '-', false),
      _buildRowData(
          'Sector 3', sortedEvents, (event) => event.bestS3 ?? '-', false),
      _buildRowData(
          'Sector 4', sortedEvents, (event) => event.bestS4 ?? '-', false),
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.withAlpha(25)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Row(
          children: [
            // Fixed left column (metric labels)
            Transform.translate(
              offset: const Offset(0, -4), // Shift up by 4 pixels
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header cell
                  Container(
                    width: 100,
                    height: 64, // Fixed height to match the event header
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 12),
                    color: AppColor.primaryColor,
                    alignment: Alignment.centerLeft,
                    child: const Text(
                      'Metric',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  // Metric label cells
                  ...rows.asMap().entries.map((entry) {
                    final index = entry.key;
                    final rowData = entry.value;

                    return Container(
                      width: 100,
                      height: 52, // Fixed height to match data rows
                      padding: const EdgeInsets.symmetric(
                          vertical: 16, horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.grey.withAlpha(25)),
                        ),
                        color: index % 2 == 0
                            ? Colors.white
                            : Colors.grey.withAlpha(10),
                      ),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        rowData.label,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColor.kTextColor,
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
            // Scrollable right part (event data)
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header row with event names (using sorted events)
                    Row(
                      children: sortedEvents.map((event) {
                        final eventName = event.event.eventName ?? 'Event';
                        final eventDate = event.event.eventDate ?? '';

                        return Container(
                          width: 120,
                          height: 64, // Fixed height to match the metric header
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 8),
                          color: AppColor.primaryColor,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                eventName,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                eventDate,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white70,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                    // Data rows
                    ...rows.asMap().entries.map((entry) {
                      final rowIndex = entry.key;
                      final rowData = entry.value;

                      return Container(
                        decoration: BoxDecoration(
                          border: Border(
                            bottom:
                                BorderSide(color: Colors.grey.withAlpha(25)),
                          ),
                          color: rowIndex % 2 == 0
                              ? Colors.white
                              : Colors.grey.withAlpha(10),
                        ),
                        child: Row(
                          children: rowData.cells.map((cell) {
                            return Container(
                              width: 120,
                              height:
                                  52, // Fixed height to match the metric cells
                              padding: const EdgeInsets.symmetric(
                                  vertical: 16, horizontal: 8),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    cell.value,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: cell.isBest
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                      color: _getCellTextColor(
                                          cell, rowData.cells.indexOf(cell)),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  if (cell.isImproved && cell.value != '-') ...[
                                    const SizedBox(width: 4),
                                    Icon(
                                      // For improved values:
                                      // - Higher is better (speed): show up arrow
                                      // - Lower is better (time): show down arrow
                                      // For lap times (higherIsBetter=false): show DOWN arrow when improved
                                      // For speed (higherIsBetter=true): show UP arrow when improved
                                      rowData.higherIsBetter
                                          ? Icons
                                              .arrow_upward // Speed increased (good)
                                          : Icons
                                              .arrow_downward, // Time decreased (good)
                                      color: Colors.green,
                                      size: 14,
                                    ),
                                  ],
                                  if (cell.isDeclined && cell.value != '-') ...[
                                    const SizedBox(width: 4),
                                    Icon(
                                      // For declined values:
                                      // - Higher is better (speed): show down arrow
                                      // - Lower is better (time): show up arrow
                                      // For lap times (higherIsBetter=false): show UP arrow when declined
                                      // For speed (higherIsBetter=true): show DOWN arrow when declined
                                      rowData.higherIsBetter
                                          ? Icons
                                              .arrow_downward // Speed decreased (bad)
                                          : Icons
                                              .arrow_upward, // Time increased (bad)
                                      color: Colors.red,
                                      size: 14,
                                    ),
                                  ],
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to determine cell text color
  Color _getCellTextColor(CellData cell, int cellIndex) {
    // Missing values are always gray
    if (cell.value == '-') {
      return AppColor.kTextGrayColor;
    }

    // Best values are always primary color
    if (cell.isBest) {
      return AppColor.primaryColor;
    }

    // First column is always normal text color
    if (cellIndex == 0) {
      return AppColor.kTextColor;
    }

    // Improved values are green
    if (cell.isImproved) {
      return Colors.green;
    }

    // Declined values are red
    if (cell.isDeclined) {
      return Colors.red;
    }

    // Default color
    return AppColor.kTextColor;
  }

  double _convertLapTimeToSeconds(String lapTime) {
    try {
      // Handle formats like "3:45.210" or "45.896"
      if (lapTime.contains(':')) {
        final parts = lapTime.split(':');
        final minutes = int.parse(parts[0]);
        final secondsPart = parts[1].split('.');
        final seconds = double.parse(secondsPart[0]);
        final milliseconds =
            secondsPart.length > 1 ? double.parse(secondsPart[1]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      } else if (lapTime.contains('.')) {
        final parts = lapTime.split('.');
        final seconds = double.parse(parts[0]);
        final milliseconds =
            parts.length > 1 ? double.parse(parts[1]) / 1000 : 0;
        return seconds + milliseconds;
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return 0;
  }

  // Helper method to parse event date strings into DateTime objects for proper sorting
  DateTime _parseEventDate(String dateString) {
    try {
      // Handle common date formats like "January 15, 2023", "June 15, 2023", etc.
      final months = {
        'january': 1,
        'february': 2,
        'march': 3,
        'april': 4,
        'may': 5,
        'june': 6,
        'july': 7,
        'august': 8,
        'september': 9,
        'october': 10,
        'november': 11,
        'december': 12
      };

      // Remove extra whitespace and convert to lowercase
      final cleanDate = dateString.trim().toLowerCase();

      // Try to parse "Month Day, Year" format
      final parts = cleanDate.split(' ');
      if (parts.length >= 3) {
        final monthName = parts[0];
        final dayStr = parts[1].replaceAll(',', '');
        final yearStr = parts[2];

        final month = months[monthName];
        final day = int.tryParse(dayStr);
        final year = int.tryParse(yearStr);

        if (month != null && day != null && year != null) {
          return DateTime(year, month, day);
        }
      }

      // Fallback: try standard DateTime.parse
      return DateTime.parse(dateString);
    } catch (e) {
      // If all parsing fails, return a default date (epoch)
      return DateTime(1970, 1, 1);
    }
  }
}
