import 'package:get/get.dart';

import '../../../models/lap_performance_model.dart';
import '../../../models/performance_comparison_model.dart';

class PerformanceComparisonController extends GetxController {
  // This would be populated from the API in a real implementation
  final performanceComparison = Rx<PerformanceComparison?>(null);
  final isLoading = true.obs;

  @override
  void onInit() {
    super.onInit();
    // In a real implementation, we would fetch data from the API
    // For now, we'll use the sample data
    loadPerformanceData();
  }

  Future<void> loadPerformanceData() async {
    isLoading.value = true;

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      // Create sample data for demonstration
      performanceComparison.value = _createSamplePerformanceComparison();
    } catch (e) {
      print('Error loading performance data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Create sample data for demonstration purposes
  PerformanceComparison _createSamplePerformanceComparison() {
    // Create multiple event summaries for the same track
    final List<PerformanceSummary> eventSummaries = [
      _createEventSummary(
        eventName: 'Sepang Track Day - Beginner',
        eventDate: 'January 15, 2023',
        bestLapTime: '03:25.210',
        avgLapTime: '03:45.120',
        topSpeed: 135.40,
        bestS1: '53.576',
        bestS2: '49.845',
        bestS3: '1:12.471',
        bestS4: '49.318',
      ),
      _createEventSummary(
        eventName: 'Sepang Track Day - Intermediate',
        eventDate: 'March 22, 2023',
        bestLapTime: '03:10.450',
        avgLapTime: '03:25.780',
        topSpeed: 148.20,
        bestS1: '48.123',
        bestS2: '46.545',
        bestS3: '1:08.471',
        bestS4: '47.311',
      ),
      _createEventSummary(
        eventName: 'Sepang Racing Weekend',
        eventDate: 'June 15, 2023',
        bestLapTime: '02:58.890',
        avgLapTime: '03:10.450',
        topSpeed: 158.70,
        bestS1: '45.896',
        bestS2: '44.545',
        bestS3: '1:03.471',
        bestS4: '44.978',
      ),
      _createEventSummary(
        eventName: 'Sepang Championship Round 1',
        eventDate: 'September 8, 2023',
        bestLapTime: '02:52.120',
        avgLapTime: '03:05.780',
        topSpeed: 165.40,
        bestS1: '43.123',
        bestS2: '42.845',
        bestS3: '1:01.471',
        bestS4: '44.681',
      ),
    ];

    return PerformanceComparison(
      eventSummaries: eventSummaries,
      trackName: 'Sepang International Circuit',
      driverName: 'John Doe',
      vehicleInfo: 'Honda Civic Type R (2022)',
    );
  }

  PerformanceSummary _createEventSummary({
    required String eventName,
    required String eventDate,
    required String bestLapTime,
    required String avgLapTime,
    required double topSpeed,
    required String bestS1,
    required String bestS2,
    required String bestS3,
    required String bestS4,
  }) {
    final event = Event(
      eventName: eventName,
      eventDate: eventDate,
      organizerName: 'Automoment Racing Club',
      trackName: 'Sepang International Circuit',
    );

    final driverName = 'John Doe';
    final vehicleInfo = 'Honda Civic Type R (2022)';

    // Create sample laps
    final laps = _generateSampleLaps(
        bestLapTime, bestS1, bestS2, bestS3, bestS4, topSpeed);

    final summary = PerformanceSummary(
      event: event,
      driverName: driverName,
      vehicleInfo: vehicleInfo,
      laps: laps,
    );

    // Override calculated values for demonstration
    summary.bestLapTime = bestLapTime;
    summary.averageLapTime = avgLapTime;
    summary.topSpeed = topSpeed;
    summary.bestS1 = bestS1;
    summary.bestS2 = bestS2;
    summary.bestS3 = bestS3;
    summary.bestS4 = bestS4;

    return summary;
  }

  List<LapPerformance> _generateSampleLaps(
    String bestLapTime,
    String bestS1,
    String bestS2,
    String bestS3,
    String bestS4,
    double topSpeed,
  ) {
    // Generate 10 sample laps with the best lap being one of them
    final laps = <LapPerformance>[];

    // Convert best lap time to seconds for calculations
    final bestLapSeconds = _convertLapTimeToSeconds(bestLapTime);

    for (int i = 1; i <= 10; i++) {
      // Vary lap times around the best lap time
      final lapVariation = i == 5 ? 0.0 : (i % 3 == 0 ? 5.0 : 2.0 + (i * 0.5));
      final lapSeconds = bestLapSeconds + lapVariation;

      // Convert back to lap time format
      final lapTime = _secondsToLapTime(lapSeconds);

      // Vary sector times
      final s1 = i == 5 ? bestS1 : _varySectorTime(bestS1, 0.5 + (i * 0.1));
      final s2 = i == 5 ? bestS2 : _varySectorTime(bestS2, 0.3 + (i * 0.1));
      final s3 = i == 5 ? bestS3 : _varySectorTime(bestS3, 0.7 + (i * 0.1));
      final s4 = i == 5 ? bestS4 : _varySectorTime(bestS4, 0.4 + (i * 0.1));

      // Vary speed
      final speed = i == 5 ? topSpeed : topSpeed - (i * 1.5);

      laps.add(LapPerformance(
        lapNumber: i,
        lapTime: lapTime,
        s1: s1,
        s2: s2,
        s3: s3,
        s4: s4,
        speed: speed,
        timeOfDay: '10:${15 + i}:${23 + (i * 3)}.${784 + (i * 100)}',
      ));
    }

    return laps;
  }

  double _convertLapTimeToSeconds(String lapTime) {
    try {
      // Format: "MM:SS.mmm" or "MM:SS:mmm"
      final parts = lapTime.replaceAll(':', '.').split('.');
      if (parts.length >= 2) {
        final minutes = int.parse(parts[0]);
        final seconds = double.parse(parts[1]);
        final milliseconds =
            parts.length > 2 ? double.parse(parts[2]) / 1000 : 0;
        return (minutes * 60) + seconds + milliseconds;
      }
    } catch (e) {
      print('Error parsing lap time: $e');
    }
    return 0;
  }

  String _secondsToLapTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final remainingSeconds = seconds % 60;
    final secondsFormatted = remainingSeconds.toStringAsFixed(3);
    return '${minutes.toString().padLeft(2, '0')}:$secondsFormatted';
  }

  String _varySectorTime(String sectorTime, double variation) {
    try {
      double sectorSeconds = 0;

      // Handle formats like "1:02.471" or "40.896"
      if (sectorTime.contains(':')) {
        final parts = sectorTime.split(':');
        final minutes = int.parse(parts[0]);
        final secondsPart = parts[1].split('.');
        final seconds = double.parse(secondsPart[0]);
        final milliseconds =
            secondsPart.length > 1 ? double.parse(secondsPart[1]) / 1000 : 0;
        sectorSeconds = (minutes * 60) + seconds + milliseconds;
      } else {
        final parts = sectorTime.split('.');
        final seconds = double.parse(parts[0]);
        final milliseconds =
            parts.length > 1 ? double.parse(parts[1]) / 1000 : 0;
        sectorSeconds = seconds + milliseconds;
      }

      // Add variation
      sectorSeconds += variation;

      // Convert back to sector time format
      if (sectorSeconds >= 60) {
        final minutes = (sectorSeconds / 60).floor();
        final remainingSeconds = sectorSeconds % 60;
        final secondsFormatted = remainingSeconds.toStringAsFixed(3);
        return '$minutes:$secondsFormatted';
      } else {
        return sectorSeconds.toStringAsFixed(3);
      }
    } catch (e) {
      print('Error varying sector time: $e');
      return sectorTime;
    }
  }
}
