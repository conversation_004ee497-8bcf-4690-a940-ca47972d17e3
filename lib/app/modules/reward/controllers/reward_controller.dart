import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/models/coupon_reward_model.dart';
import 'package:automoment/app/models/lifestyle_model.dart';
import 'package:automoment/app/models/point_history_model.dart';
import 'package:contained_tab_bar_view/contained_tab_bar_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/reward.dart';
import '../../../services/api_client.dart';

class RewardController extends BaseScreenController {
  @override
  String get screenName => 'Reward';

  final UserController userController = Get.put(UserController());

  var redeemList = <Reward>[].obs;
  var availableCouponList = <CouponReward>[].obs;
  var usedCouponList = <CouponReward>[].obs;
  var historyList = <PointHistory>[].obs;
  var lifestyleList = <Lifestyle>[].obs;

  var currentPoints = 0.obs;

  var selectedRewardToRedeem = Reward().obs;
  var msg = "";

  GlobalKey<ContainedTabBarViewState> containedTabBarKey = GlobalKey();

  @override
  void onInit() {
    super.onInit();
    fetchRewards();
  }

  Future<void> fetchRewards() async {
    EasyLoading.show(status: 'Loading...');
    try {
      var response = await ApiClient.getRewards(userController.getToken());
      currentPoints.value = jsonDecode(response)['current_points'];
      var list = jsonDecode(response)['redeem'];

      redeemList.clear();

      list.forEach((dynamic d) {
        redeemList.add(Reward.fromJson(d));
      });

      // redeemList.value = list.map((e) => Reward.fromJson(e)).toList();

      //

      var list2 = jsonDecode(response)['available'];
      availableCouponList.clear();

      list2.forEach((dynamic d) {
        availableCouponList.add(CouponReward.fromJson(d));
      });

      //

      var list3 = jsonDecode(response)['used'];
      usedCouponList.clear();

      list3.forEach((dynamic d) {
        usedCouponList.add(CouponReward.fromJson(d));
      });

      //

      var list4 = jsonDecode(response)['history'];
      historyList.clear();

      list4.forEach((dynamic d) {
        historyList.add(PointHistory.fromJson(d));
      });

      //

      var list5 = jsonDecode(response)['lifestyles'];
      lifestyleList.clear();

      list5.forEach((dynamic d) {
        lifestyleList.add(Lifestyle.fromJson(d));
      });

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> pointExchange() async {
    EasyLoading.show(status: 'Loading...');
    try {
      EasyLoading.dismiss();

      var response = await ApiClient.pointExchange(
          userController.getToken(), selectedRewardToRedeem.value.id!);

      bool success = jsonDecode(response)['success'];
      msg = jsonDecode(response)['message'];

      return success;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
