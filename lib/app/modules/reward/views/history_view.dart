import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/point_history_model.dart';
import '../controllers/reward_controller.dart';

class HistoryView extends StatelessWidget {
  HistoryView({super.key});

  final RewardController controller = Get.put(RewardController());

  @override
  Widget build(BuildContext context) {
    return controller.historyList.isNotEmpty ?
    SingleChildScrollView(
      child: Column(
          children: [
            const SizedBox(height: 10,),
            Card(
                elevation: 0,
                color: Colors.transparent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 13),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppConfig.defaultPadding),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const SizedBox(width: 8,),
                          Text(
                            "Date",
                            style: AppTextStyles.titleText.copyWith(color: Colors.black, fontSize: 14),
                          ),
                          const SizedBox(width: 55,),
                          Text(
                            "Activity",
                            style: AppTextStyles.titleText.copyWith(color: Colors.black, fontSize: 14),
                          ),
                          const Spacer(),
                          Text(
                            "Points",
                            style: AppTextStyles.titleText.copyWith(color: Colors.black, fontSize: 14),
                          ),
                          const SizedBox(width: 5,),
                        ],
                      ),
                    ),
                  ],
                )
            ),
            ...controller.historyList.map((history) {
              return Padding(
                padding: const EdgeInsets.only(left: 8, right: 8, bottom: 4),
                child: historyCardView(history),
              );
            }),
            const SizedBox(height: 10,),
          ]
      ),
    ) :
    Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text("You have not earned any points yet", style: AppTextStyles.normalTextBold.copyWith(
            color: AppColor.kTextColor,
          )),
          const SizedBox(height: 10),
          Text("Points earned and redeemed go here", style: AppTextStyles.normalText.copyWith(
            color: AppColor.kTextColor,
          )),
        ]
    );
  }

  Widget historyCardView(PointHistory history) {
    return Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: Text (
                DateFormat("d MMM yyyy").format(history.createdAt!),
                style: AppTextStyles.normalText.copyWith(color: Colors.black, fontSize: 12),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(
                    left: AppConfig.defaultPadding,
                    right: AppConfig.defaultPadding,
                    top: AppConfig.defaultPadding,
                    bottom: AppConfig.defaultPadding
                ),
                child: Text (
                  history.activity ?? "",
                  style: AppTextStyles.normalText.copyWith(color: Colors.black, fontSize: 12),
                ),
              ),
            ),
            //Spacer(),
            Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: history.type == "redeemed" ?
                Text (
                  "- ${history.points}",
                  style: AppTextStyles.normalText.copyWith(color: Colors.red, fontSize: 12),
                ) :
                Text (
                  "+ ${history.points}",
                  style: AppTextStyles.normalText.copyWith(color: Colors.green, fontSize: 12, fontWeight: FontWeight.bold),
                ),
            ),
          ],
        ),
    );
  }


}
