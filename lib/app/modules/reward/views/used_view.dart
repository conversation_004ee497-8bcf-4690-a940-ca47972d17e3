import 'package:coupon_uikit/coupon_uikit.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/coupon_reward_model.dart';
import '../controllers/reward_controller.dart';

class UsedView extends StatelessWidget {
  UsedView({super.key});

  final RewardController controller = Get.put(RewardController());

  @override
  Widget build(BuildContext context) {

    return controller.usedCouponList.isNotEmpty ?
      SingleChildScrollView(
        child: Column(
            children: [
              const SizedBox(height: 10,),
              ...controller.usedCouponList.map((coupon) {

                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  child: couponItemView(coupon)
                );
              }),
              const SizedBox(height: 10,),
            ]
        ),
      ) :
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text("You have not used any coupons yet", style: AppTextStyles.normalTextBold.copyWith(
            color: AppColor.kTextColor,
          )),
          const SizedBox(height: 10),
          Text("Used and expired coupons go here", style: AppTextStyles.normalText.copyWith(
            color: AppColor.kTextColor,
          )),
        ]
      );
  }

  couponItemView(CouponReward coupon) {
    const Color primaryColor = Color(0xfff1e3d3);
    const Color secondaryColor = AppColor.secondaryColor;

    String discountStr = "";

    if (coupon.type == "percentage") {
      discountStr = "${coupon.value}%";
    } else {
      discountStr = "SGD ${coupon.value}";
    }

    String? couponDesc = coupon.description;

    return Stack(
      children: [
        CouponCard(
          height: 150,
          backgroundColor: primaryColor,
          clockwise: true,
          curvePosition: 135,
          curveRadius: 30,
          curveAxis: Axis.vertical,
          borderRadius: 10,
          firstChild: Container(
            decoration: const BoxDecoration(
              color: secondaryColor,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          discountStr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          'OFF',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(color: Colors.white54, height: 0),
                const Expanded(
                  child: Center(
                    child: Text(
                      'AUTOMOMENT',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          secondChild: Container(
            width: double.maxFinite,
            padding: const EdgeInsets.all(18),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Coupon Code',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.black54,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  coupon.code!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 24,
                    color: secondaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  couponDesc ?? "",
                  maxLines: 2,
                  textAlign: TextAlign.start,
                  style: const TextStyle(
                    color: Colors.black45,
                  ),
                ),
                Text(
                  'Valid until ${DateFormat('d MMM yyyy').format(coupon.endDate!)}',
                  textAlign: TextAlign.start,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.black45,
                  ),
                ),
              ],
            ),
          ),
        ),
        Container(
          decoration: const BoxDecoration(
            color: Color.fromRGBO(255, 255, 255, 0.5),
          ),
          width: double.infinity,
          height: 150,
        )
      ],
    );
  }
}
