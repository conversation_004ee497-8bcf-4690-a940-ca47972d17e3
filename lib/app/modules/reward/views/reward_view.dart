import 'package:automoment/app/modules/reward/views/information_view.dart';
import 'package:automoment/app/modules/reward/views/lifestyle_view.dart';
import 'package:automoment/app/modules/reward/views/redeem_view.dart';
import 'package:contained_tab_bar_view/contained_tab_bar_view.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/reward_controller.dart';
import 'dashboard_view.dart';

class RewardView extends StatelessWidget {
  RewardView({super.key});
  final RewardController controller = Get.put(RewardController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          title: const Text(
            "Rewards",
            style: TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
          elevation: 1,
          leading: IconButton(
            padding: const EdgeInsets.only(left: 10),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
            icon: const Icon(Icons.menu),
            iconSize: 30,
            color: Colors.black,
          ),
          actions: [
            IconButton(
                padding: const EdgeInsets.only(right: 0),
                onPressed: () {
                  controller.fetchRewards();
                },
                icon: const Icon(Icons.refresh)),
            IconButton(
              padding: const EdgeInsets.only(right: 10, left: 0),
              onPressed: () {
                Get.toNamed(Routes.NOTIFICATION);
              },
              icon: Obx(() {
                return Stack(
                  children: [
                    const Icon(Icons.notifications,
                        color: Colors.black, size: 30),
                    (controller.userController.unreadNotificationCount.value >
                            0)
                        ? Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(1),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(6)),
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 12,
                                minHeight: 12,
                              ),
                              child: Text(
                                (controller.userController
                                            .unreadNotificationCount.value >
                                        99)
                                    ? "99+"
                                    : controller.userController
                                        .unreadNotificationCount.value
                                        .toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          )
                        : const SizedBox()
                  ],
                );
              }),
            ),
          ],
        ),
        floatingActionButton: const SupportButton(),
        body: ContainedTabBarView(
          key: controller.containedTabBarKey,
          tabs: const [
            Text('Rewards'),
            Text('Points'),
            Text('Redeem'),
            Text('Information'),
          ],
          views: [
            RefreshIndicator(
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  //controller.fetchLeaderboards();
                });
              },
              child: LifestyleView(),
            ),
            RefreshIndicator(
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  //controller.fetchLeaderboards();
                });
              },
              child: DashboardView(),
            ),
            RefreshIndicator(
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  controller.fetchRewards();
                });
              },
              child: RedeemView(),
            ),
            RefreshIndicator(
              onRefresh: () {
                return Future.delayed(const Duration(seconds: 1), () {
                  //controller.fetchLeaderboards();
                });
              },
              child: const InformationView(),
            ),
          ],
          tabBarProperties: const TabBarProperties(
            indicatorColor: AppColor.kTextColor,
            labelColor: AppColor.kTextColor,
          ),
          onChange: (index) {},
        ));
  }
}
