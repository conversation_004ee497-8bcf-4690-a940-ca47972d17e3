import 'package:automoment/app/models/coupon_reward_model.dart';
import 'package:coupon_uikit/coupon_uikit.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../controllers/reward_controller.dart';

class AvailableView extends StatelessWidget {
  AvailableView({super.key});

  final RewardController controller = Get.put(RewardController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return controller.availableCouponList.isNotEmpty
          ? SingleChildScrollView(
              child: Column(children: [
                const SizedBox(
                  height: 10,
                ),
                ...controller.availableCouponList.map((coupon) {
                  return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: couponItemView(coupon));
                }),
                const SizedBox(
                  height: 10,
                ),
              ]),
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                  Text("You have no coupons yet",
                      style: AppTextStyles.normalTextBold.copyWith(
                        color: AppColor.kTextColor,
                      )),
                  const SizedBox(height: 10),
                  controller.currentPoints.value == 0
                      ? Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 40.0),
                          child:
                              Text("Start collecting points to redeem coupons.",
                                  textAlign: TextAlign.center,
                                  style: AppTextStyles.normalText.copyWith(
                                    color: AppColor.kTextColor,
                                  )),
                        )
                      : Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 40.0),
                          child: Text(
                              "Congrats! You earned ${controller.currentPoints.value} points. Redeem them for coupons.",
                              textAlign: TextAlign.center,
                              style: AppTextStyles.normalText.copyWith(
                                color: AppColor.kTextColor,
                              )),
                        ),
                  const SizedBox(height: 30),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 40),
                    child: ElevatedButton(
                        style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.primaryButtonColor),
                            shape: WidgetStateProperty.all<
                                    RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(18.0),
                                    side: const BorderSide(
                                        color: AppColor.primaryButtonColor)))),
                        onPressed: () {
                          // open tab information
                          final rewardController = Get.find<RewardController>();
                          rewardController.containedTabBarKey.currentState
                              ?.animateTo(2);
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          height: 50,
                          child: const Center(
                            child: Text("How do I earn points?",
                                style: TextStyle(fontSize: 16)),
                          ),
                        )),
                  ),
                ]);
    });
  }

  couponItemView(CouponReward coupon) {
    const Color primaryColor = Color(0xfff1e3d3);
    const Color secondaryColor = AppColor.secondaryColor;

    String discountStr = "";

    if (coupon.type == "percentage") {
      discountStr = "${coupon.value}%";
    } else {
      discountStr = "SGD ${coupon.value}";
    }

    String? couponDesc = coupon.description;

    return CouponCard(
      height: 150,
      backgroundColor: primaryColor,
      clockwise: true,
      curvePosition: 135,
      curveRadius: 30,
      curveAxis: Axis.vertical,
      borderRadius: 10,
      firstChild: Container(
        decoration: const BoxDecoration(
          color: secondaryColor,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      discountStr,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      'OFF',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(color: Colors.white54, height: 0),
            const Expanded(
              child: Center(
                child: Text(
                  'AUTOMOMENT',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      secondChild: Container(
        width: double.maxFinite,
        padding: const EdgeInsets.all(18),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Coupon Code',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold,
                color: Colors.black54,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              coupon.code!,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 24,
                color: secondaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Text(
              couponDesc ?? "",
              maxLines: 2,
              textAlign: TextAlign.start,
              style: const TextStyle(
                color: Colors.black45,
              ),
            ),
            Text(
              'Valid until ${DateFormat('d MMM yyyy').format(coupon.endDate!)}',
              textAlign: TextAlign.start,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.black45,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
