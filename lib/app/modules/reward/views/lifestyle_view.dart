import 'package:automoment/app/constants/app_config.dart';
import 'package:automoment/app/models/lifestyle_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../controllers/reward_controller.dart';

class LifestyleView extends StatelessWidget {
  LifestyleView({super.key});

  final RewardController controller = Get.put(RewardController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: controller.lifestyleList.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () => Get.toNamed('/reward-lifestyle-detail',
                arguments: controller.lifestyleList[index]),
            child: lifestyleCard(lifestyle: controller.lifestyleList[index]),
          );
        },
      ),
    );
  }
}

Widget lifestyleCard({required Lifestyle lifestyle}) {
  return Card(
    elevation: 4,
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    child: SizedBox(
      height: 150,
      child: Row(
        children: [
          // Left side image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              bottomLeft: Radius.circular(12),
            ),
            child: Image.network(
              "${AppConfig.storageUrl}${lifestyle.image}",
              width: 150,
              height: 150,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 150,
                  height: 150,
                  color: Colors.grey[200],
                  child: const Icon(Icons.error_outline),
                );
              },
            ),
          ),

          // Right side content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    lifestyle.title ?? '',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColor.primaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  // Description
                  Expanded(
                    child: Text(
                      lifestyle.description ?? '',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
