import 'package:automoment/app/modules/reward/views/history_view.dart';
import 'package:automoment/app/modules/reward/views/used_view.dart';
import 'package:contained_tab_bar_view/contained_tab_bar_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../controllers/reward_controller.dart';
import 'available_view.dart';

class DashboardView extends StatelessWidget {
  DashboardView({super.key});

  final RewardController controller = Get.put(RewardController());

  @override
  Widget build(BuildContext context) {
    return Stack(
        children: [
          Obx(() {
            return Container(
              width: double.infinity,
              padding: const EdgeInsets.only(top: 20),
              child: controller.currentPoints.value == 0 ||
                  controller.currentPoints.value == 1 ?
                Text(
                  "${controller.currentPoints.value} point",
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      color: AppColor.primaryColor,
                      fontSize: 25,
                      fontWeight: FontWeight.bold
                  )
              ) :
                Text(
                  "${controller.currentPoints.value} points",
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      color: AppColor.primaryColor,
                      fontSize: 25,
                      fontWeight: FontWeight.bold
                  )
              ),
            );
          }),
          Padding(
            padding: const EdgeInsets.only(top: 60.0),
            child: ContainedTabBarView(
                tabs: const [
                  Text("Available"),
                  Text("Used"),
                  Text("History"),
                ],
                views: [
                  AvailableView(),
                  UsedView(),
                  HistoryView(),
                ]
            ),
          )
        ]
    );
  }
}
