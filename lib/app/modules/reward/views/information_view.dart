import 'package:flutter/material.dart';

import '../../../constants/app_color.dart';

class InformationView extends StatelessWidget {
  const InformationView({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text("Earn points each time you complete various activities through the app.",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16
                )
            ),
            const SizedBox(height: 20,),
            const Text("Redeem your points for coupons and get discounts on your next purchase.",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16
                )
            ),
            const SizedBox(height: 40,),
            const Text("How do I earn points?",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold
                )
            ),
            Table(
              // border: TableBorder.all(),
              columnWidths: const {
                0: FlexColumnWidth(1),
                1: FlexColumnWidth(1.5),
              },
              children: const [
                TableRow(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        'POINTS',
                        style: TextStyle(
                            color: AppColor.primaryColor,
                            fontSize: 16,
                            fontWeight: FontWeight.bold
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                          'ACTION',
                          style: TextStyle(
                              color: AppColor.primaryColor,
                              fontSize: 16,
                              fontWeight: FontWeight.bold
                          ),
                      ),
                    ),
                  ],
                ),
                TableRow(
                  children: [
                    Text(
                        '100',
                        style: TextStyle(
                            color: AppColor.primaryColor,
                            fontSize: 16,
                        ),
                    ),
                    Text(
                        'Booking an event',
                        style: TextStyle(
                            color: AppColor.primaryColor,
                            fontSize: 16,
                        ),
                    ),
                  ],
                ),
                // TableRow(
                //   children: [
                //     Padding(
                //       padding: EdgeInsets.symmetric(vertical: 8.0),
                //       child: Text(
                //           '50',
                //           style: TextStyle(
                //             color: AppColor.primaryColor,
                //             fontSize: 16,
                //           ),
                //       ),
                //     ),
                //     Padding(
                //       padding: EdgeInsets.symmetric(vertical: 8.0),
                //       child: Text(
                //           'Buy addons',
                //           style: TextStyle(
                //             color: AppColor.primaryColor,
                //             fontSize: 16,
                //           ),
                //       ),
                //     ),
                //   ],
                // ),
                // TableRow(
                //   children: [
                //     Text(
                //         '30',
                //         style: TextStyle(
                //           color: AppColor.primaryColor,
                //           fontSize: 16,
                //         ),
                //     ),
                //     Text(
                //         'Upload photos',
                //         style: TextStyle(
                //           color: AppColor.primaryColor,
                //           fontSize: 16,
                //         ),
                //     ),
                //   ],
                // ),
              ],
            ),
            const SizedBox(height: 20,),
            const Text("How do I redeem points?",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold
                )
            ),
            const SizedBox(height: 10,),
            const Text("Redeem your points for coupons and get discounts on your next purchase. Tap redeem tab to get started.",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16
                )
            ),
            const SizedBox(height: 20,),
            const Text("How do I use my coupons?",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold
                )
            ),
            const SizedBox(height: 10,),
            const Text("Apply your coupon by copying the code associated with it and applying it at checkout.",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16
                )
            ),
            const SizedBox(height: 20,),
            const Text("Do my points expire?",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold
                )
            ),
            const SizedBox(height: 10,),
            const Text("No, your points never expire.",
                style: TextStyle(
                    color: AppColor.primaryColor,
                    fontSize: 16
                )
            ),
          ]
        ),
      ),
    );
  }
}
