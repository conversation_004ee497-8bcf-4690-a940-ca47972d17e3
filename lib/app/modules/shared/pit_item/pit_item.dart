import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';

class PitItemController extends GetxController {
  final int pitNumber;
  final int currentPitNumber;
  final int currentPitPosition;

  final List<bool> availability;

  final bool isSelectable;

  final Function(int, int) onSelected;

  RxInt selectedCarPosition = 100.obs;

  PitItemController({
    required this.pitNumber,
    this.currentPitNumber = pitNotSelected,
    this.currentPitPosition = pitNotSelected,
    required this.onSelected,
    required this.availability,
    this.isSelectable = true,
  });

  void selectCarPosition(int carPositionNumber) {
    // Check if the CarPosition is available
    if (availability[carPositionNumber]) {
      onSelected(pitNumber, carPositionNumber);
    }
  }
}

class PitItem extends StatelessWidget {
  final PitItemController controller;

  const PitItem(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
          child: Center(
            child: Text('Pit ${controller.pitNumber}',
                style: AppTextStyles.titleText.copyWith(fontSize: 16)),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
          decoration: const BoxDecoration(
            color: AppColor.blueDarker,
            border: Border(
                top: BorderSide(
                  width: 1,
                  color: AppColor.kTextColor,
                ),
                bottom: BorderSide(
                  width: 1,
                  color: AppColor.kTextColor,
                ),
                left: BorderSide(
                  width: 1,
                  color: AppColor.kTextColor,
                )),
          ),
          child: Column(
            children: [
              Column(
                children: [
                  Row(children: [
                    carWidget(0, controller.availability[0]),
                    const SizedBox(width: 10),
                    carWidget(1, controller.availability[1]),
                  ]),
                  const SizedBox(height: 5),
                  Row(children: [
                    carWidget(2, controller.availability[2]),
                    const SizedBox(width: 10),
                    carWidget(3, controller.availability[3]),
                  ]),
                  const SizedBox(height: 5),
                  Row(children: [
                    carWidget(4, controller.availability[4]),
                  ]),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget carWidget(int carPosition, bool isAvailable) {
    final pitOpController = Get.put(PitOpController());

    //
    //print ('carPosition: $carPosition');
    //print ('controller.selectedCarPosition.value: ${controller.selectedCarPosition.value}');
    //print ('controller.pitNumber: ${controller.pitNumber}');
    //print ('pitOpController.selectedPitNumber.value: ${pitOpController.selectedPitNumber.value}');
    //print ('controller.availability[carPosition]: ${controller.availability[carPosition]}');
    //

    if (carPosition == controller.currentPitPosition) isAvailable = true;

    return GestureDetector(onTap: () {
      //if (controller.isSelectable) {
      controller.selectCarPosition(carPosition);
      controller.selectedCarPosition.value = carPosition;
      //}
    }, child: Obx(() {
      return Container(
          width: 40,
          height: 50,
          margin: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: (carPosition == pitOpController.selectedPitPosition.value &&
                    controller.pitNumber ==
                        pitOpController.selectedPitNumber
                            .value) // && controller.availability[carPosition]
                ? AppColor.redButtonColor
                : isAvailable
                    ? Colors.green
                    : Colors.grey,
          ));
    }));
  }
}

const int pitNotSelected = 100;

class PitOpController extends GetxController {
  var selectedPitNumber = pitNotSelected.obs;
  var selectedPitPosition = pitNotSelected.obs;
}
