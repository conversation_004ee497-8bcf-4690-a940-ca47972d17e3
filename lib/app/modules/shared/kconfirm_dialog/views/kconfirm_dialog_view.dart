import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:automoment/app/constants/app_color.dart';

class KConfirmDialogView extends StatelessWidget {
  final String content;
  final Function? callbackYes;
  final Function? callbackNo;

  const KConfirmDialogView({
    super.key,
    required this.content,
    this.callbackYes,
    this.callbackNo,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
        title: const Text("Automoment"),
        content: Text(content),
        actions: [
          TextButton(
            child: const Text(
              "Yes",
              style: TextStyle(color: AppColor.secondaryColor),
            ),
            onPressed: () {
              if (callbackYes == null) {
                Get.back();
              }
              else {
                callbackYes!();
              }
            },
          ),
          TextButton(
            child: const Text(
              "No",
              style: TextStyle(color: AppColor.secondaryColor),
            ),
            onPressed: () {
              if (callbackNo == null) {
                Get.back();
              }
              else {
                callbackNo!();
              }
            },
          )
        ]);
  }
}
