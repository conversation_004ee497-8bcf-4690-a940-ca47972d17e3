
import 'package:flutter/material.dart';

import '../../constants/app_color.dart';

class DottedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColor.kTextGrayColor
      ..strokeWidth = 2;

    var max = size.width;
    var dashWidth = 2.0;
    var dashSpace = 5.0;
    var startY = size.height / 2;

    for (var dx = 0.0; dx < max; dx += dashWidth + dashSpace) {
      canvas.drawLine(Offset(dx, startY), Offset(dx + dashWidth, startY), paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class DashedLineVerticalPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double dashHeight = 3;
    double dashSpace = 3;
    double startY = 0;

    final paint = Paint()
      ..color = AppColor.kTextGrayColor
      ..strokeWidth = size.width; // Set the width of the line

    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}