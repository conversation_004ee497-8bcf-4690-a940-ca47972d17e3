import 'package:flutter/material.dart';

import '../../constants/app_color.dart';

Widget loadingButton(String? text) {
  return ElevatedButton(
      style: ButtonStyle(
          foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
          backgroundColor: WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(18.0),
                  side: const BorderSide(color: AppColor.primaryButtonColor)
              )
          )
      ),
      onPressed: () {
      },
      child: Container(
        padding: const EdgeInsets.only(left: 20, right: 20),
        height: 50,
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  color: Colors.white, strokeWidth: 2,
                ),
              ),
              const SizedBox(width: 15),
              Text(
                  text ?? "Please wait...",
                  style: const TextStyle(fontSize: 16)
              ),
            ],
          ),
        ),
      )
  );
}