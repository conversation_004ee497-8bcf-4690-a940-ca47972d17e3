import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';

import '../controllers/order_status_detail_controller.dart';

class OrderStatusDetailView extends GetView<OrderStatusDetailController> {
  const OrderStatusDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Details'),
        centerTitle: true,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final orderData = controller.orderData.value;
        if (orderData == null) {
          return const Center(child: Text('Order not found'));
        }

        final orderItems =
            List<Map<String, dynamic>>.from(orderData['items'] ?? []);

        return RefreshIndicator(
          onRefresh: controller.loadOrderStatus,
          child: <PERSON>View(
            padding: const EdgeInsets.all(16),
            children: [
              // Order ID and Date
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Order #${orderData['order_id']}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: controller
                                  .getStatusColor(orderData['order_status'])
                                  .withAlpha(26),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              controller
                                  .getOrderStatus(orderData['order_status']),
                              style: TextStyle(
                                color: controller
                                    .getStatusColor(orderData['order_status']),
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Ordered on ${DateFormat('MMM dd, yyyy').format(DateTime.parse(orderData['created_at']))}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Order Items
              Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        'Order Items',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ),
                    const Divider(height: 1),
                    ...orderItems.map((item) => ListTile(
                          leading: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              item['product']['featured_image'] ?? '',
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 60,
                                  height: 60,
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.image_not_supported),
                                );
                              },
                            ),
                          ),
                          title: Text(
                            item['product_name'],
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          subtitle: Text(
                            'Qty: ${item['quantity']} x SGD ${item['price_sgd']}',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          trailing: Text(
                            'SGD ${item['subtotal_sgd']}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        )),
                    const Divider(height: 1),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildPricingRow(
                              'Total Items', orderData['items_count'] ?? 0,
                              isQuantity: true),
                          const SizedBox(height: 8),
                          _buildPricingRow('Total Quantity',
                              orderData['items_total_quantity'] ?? 0,
                              isQuantity: true),
                          const Divider(),
                          _buildPricingRow(
                              'Total Amount',
                              double.parse(
                                  orderData['total_amount']['sgd'] ?? '0'),
                              isTotal: true,
                              currency: 'SGD'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Order History
              Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(Icons.history),
                          const SizedBox(width: 8),
                          Text(
                            'Order History',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                        ],
                      ),
                    ),
                    const Divider(height: 1),
                    if ((orderData['status_history'] as List).isEmpty)
                      const Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('No history available'),
                      )
                    else
                      ...List<Map<String, dynamic>>.from(
                              orderData['status_history'] ?? [])
                          .map((history) => Column(
                                children: [
                                  ListTile(
                                    contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    leading: CircleAvatar(
                                      radius: 16,
                                      backgroundColor: controller
                                          .getStatusColor(history['new_status'])
                                          .withAlpha(26),
                                      child: Icon(
                                        _getStatusIcon(history['status_type']),
                                        size: 18,
                                        color: controller.getStatusColor(
                                            history['new_status']),
                                      ),
                                    ),
                                    title: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Text(
                                              history['status_type'] ==
                                                      'payment_status'
                                                  ? 'Payment Status'
                                                  : 'Order Status',
                                              style: const TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.w500,
                                                color: Colors.grey,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              DateFormat('MMM dd, yyyy HH:mm')
                                                  .format(
                                                DateTime.parse(
                                                    history['created_at']),
                                              ),
                                              style: const TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 4),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: controller
                                                .getStatusColor(
                                                    history['new_status'])
                                                .withAlpha(26),
                                            borderRadius:
                                                BorderRadius.circular(6),
                                          ),
                                          child: Text(
                                            history['notes'] ??
                                                controller.getOrderStatus(
                                                    history['new_status']),
                                            style: TextStyle(
                                              fontSize: 13,
                                              color: controller.getStatusColor(
                                                  history['new_status']),
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (history !=
                                      orderData['status_history'].last)
                                    const Divider(height: 1, indent: 56),
                                ],
                              )),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Shipping Address
              if (orderData['shipping_address'] != null)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.local_shipping_outlined),
                            const SizedBox(width: 8),
                            Text(
                              'Shipping Address',
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          orderData['shipping_address']['full_name'] ?? '',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        Text(orderData['shipping_address']['address'] ?? ''),
                        Text(orderData['shipping_address']['phone'] ?? ''),
                      ],
                    ),
                  ),
                ),

              // Pay button for unpaid orders
              if (orderData['payment_status']?.toString().toLowerCase() !=
                  'paid') ...[
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => Get.toNamed(
                    '/product-payment',
                    arguments: {'order_id': orderData['order_id']},
                  ),
                  style: ButtonStyle(
                    foregroundColor:
                        WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.resolveWith<Color>(
                        (states) => AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.0),
                        side: const BorderSide(
                            color: AppColor.primaryButtonColor),
                      ),
                    ),
                  ),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    height: 50,
                    child: const Center(
                      child: Text(
                        'Pay',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ],
          ),
        );
      }),
    );
  }

  IconData _getStatusIcon(String statusType) {
    switch (statusType) {
      case 'payment_status':
        return Icons.payment;
      case 'order_status':
        return Icons.local_shipping;
      default:
        return Icons.info;
    }
  }

  Widget _buildPricingRow(String label, num amount,
      {bool isDiscount = false,
      bool isTotal = false,
      bool isQuantity = false,
      String currency = ''}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: isDiscount ? Colors.red : null,
          ),
        ),
        Text(
          isQuantity
              ? amount.toString()
              : isDiscount
                  ? '-$currency ${amount.toStringAsFixed(2)}'
                  : '$currency ${amount.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: isDiscount ? Colors.red : null,
          ),
        ),
      ],
    );
  }
}
