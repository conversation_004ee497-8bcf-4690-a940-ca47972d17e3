import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';

class OrderStatusDetailController extends BaseScreenController {
  @override
  String get screenName => 'Order Status Detail';

  final UserController userController = Get.find<UserController>();
  final isLoading = false.obs;
  final orderData = Rxn<Map<String, dynamic>>();
  final orderId = 0.obs;

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments['order_id'] != null) {
      orderId.value = Get.arguments['order_id'];
      loadOrderStatus();
    }
  }

  Future<void> loadOrderStatus() async {
    try {
      isLoading.value = true;
      final token = userController.getToken();
      if (token == null || token.toString().isEmpty) {
        throw Exception('User token not found');
      }

      final response = await ApiClient.getOrderStatus(token, orderId.value);
      final data = json.decode(response);

      if (data['success'] == true) {
        orderData.value = data['data'];
      } else {
        throw Exception(data['message'] ?? 'Failed to load order status');
      }
    } catch (e) {
      SnackHelper.showError('Failed to load order status: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'shipped':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String getOrderStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }
}
