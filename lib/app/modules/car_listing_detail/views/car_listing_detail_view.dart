import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
import '../../shared/support_button.dart';
import '../controllers/car_listing_detail_controller.dart';

class CarListingDetailView extends GetView<CarListingDetailController> {
  const CarListingDetailView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Track Car Listing Detail',
              style: AppTextStyles.titleText),
          elevation: 1,
          actions: [
            GestureDetector(
              onTap: () {
                debugPrint("ontap");
                final box = context.findRenderObject() as RenderBox?;
                double w = box?.size.width ?? 820;
                Share.share(
                    '${controller.carListing.value.name} at ${controller.carListing.value.shareLink}',
                    sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
              },
              child: const Padding(
                  padding: EdgeInsets.only(right: 20),
                  child: Icon(
                    Icons.share,
                  )),
            ),
          ],
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(() {
          bool isForRent = false;
          bool isForSale = false;
          String currencyRent = "SGD";
          String currencySell = "SGD";
          double? priceRent;
          double? priceSell;

          if (controller.isLoaded.value) {
            isForRent = controller.carListing.value.isForRent!;
            isForSale = controller.carListing.value.isForSale!;
            priceRent = controller.carListing.value.priceRent?.toDouble();
            priceSell = controller.carListing.value.priceSell?.toDouble();

            if (controller.carListing.value.priceRent == null &&
                controller.carListing.value.priceRentMyr != null) {
              currencyRent = "MYR";
              priceRent = controller.carListing.value.priceRentMyr?.toDouble();
            } else if (controller.carListing.value.priceSell == null &&
                controller.carListing.value.priceSellMyr != null) {
              currencySell = "MYR";
              priceSell = controller.carListing.value.priceSellMyr?.toDouble();
            }

            if (controller.carListing.value.priceRent == null &&
                controller.carListing.value.priceRentMyr == null) {
              isForRent = false;
            }

            if (controller.carListing.value.priceSell == null &&
                controller.carListing.value.priceSellMyr == null) {
              isForSale = false;
            }
          }

          return (controller.isLoaded.value)
              ? SingleChildScrollView(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        (controller.carListing.value.images == null ||
                                controller.carListing.value.images!.isEmpty)
                            ? const SizedBox()
                            : CarouselSlider(
                                options: CarouselOptions(
                                  height: 300.0,
                                ),
                                items: controller.carListing.value.images!
                                    .map((i) {
                                  return Builder(
                                    builder: (BuildContext context) {
                                      return Container(
                                          width:
                                              MediaQuery.of(context).size.width,
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 5.0),
                                          child: Image.network(i,
                                              fit: BoxFit.cover));
                                    },
                                  );
                                }).toList(),
                              ),

                        const SizedBox(height: 40),

                        // title

                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Text(
                            controller.carListing.value.name ?? "",
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.normalTextBold.copyWith(
                              fontSize: 23,
                            ),
                          ),
                        ),

                        // price

                        const SizedBox(height: 10),

                        if (isForRent)
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                // make text with background yellow, text color red, rounded
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 5,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColor.redButtonColor,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Text('For Rent',
                                      style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        color: Colors.white,
                                      )),
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  '$currencyRent ${StringUtil.formatMoneyWithoutCent(priceRent!)}/day',
                                  style: AppTextStyles.normalText.copyWith(
                                    fontSize: 16,
                                    color: AppColor.redButtonColor,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        if (isForRent) const SizedBox(height: 5),

                        if (isForSale)
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 5,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColor.secondaryColor,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Text('For Sale',
                                      style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        color: Colors.white,
                                      )),
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  '$currencySell ${StringUtil.formatMoneyWithoutCent(priceSell!)}',
                                  style: AppTextStyles.normalText.copyWith(
                                    fontSize: 16,
                                    color: AppColor.redButtonColor,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        // description

                        const SizedBox(height: 30),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20.0),
                          child: Text(
                            '${controller.carListing.value.description}',
                            style: AppTextStyles.normalText.copyWith(
                              fontSize: 16,
                            ),
                          ),
                        ),

                        const SizedBox(height: 30),
                      ]),
                )
              : const SizedBox();
        }));
  }
}
