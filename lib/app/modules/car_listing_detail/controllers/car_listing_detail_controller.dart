import 'dart:convert';

import 'package:automoment/app/models/car_listing_model.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';

class CarListingDetailController extends BaseScreenController {
  @override
  String get screenName => 'Car Listing Detail';

  var isLoaded = false.obs;
  var carListing = CarListing().obs;
  int carListingId = Get.arguments;

  final userController = Get.put(UserController());

  @override
  void onReady() {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
    fetchCarListingDetail();
  }

  Future<void> fetchCarListingDetail() async {
    try {
      EasyLoading.show(status: 'loading...');
      var response = await ApiClient.getCarListingDetail(
          userController.getToken(), carListingId);
      EasyLoading.dismiss();
      carListing.value = CarListing.fromJson(jsonDecode(response));
      isLoaded.value = true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
