import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../shared/support_button.dart';
import '../controllers/reward_detail_controller.dart';

class RewardDetailView extends StatelessWidget {
  RewardDetailView({super.key});

  final controller = Get.put(RewardDetailController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        title: const Text(
          "Reward Details",
          style: TextStyle(
              color: AppColor.primaryColor,
              fontWeight: FontWeight.bold,
              fontSize: 16
          ),
        ),
        elevation: 1,
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
          icon: const Icon(Icons.menu),
          iconSize: 30,
          color: Colors.black,
        ),
      ),
      floatingActionButton: const SupportButton(),
      body: const Center(
        child: Text(
          'RewardDetailView is working',
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}
