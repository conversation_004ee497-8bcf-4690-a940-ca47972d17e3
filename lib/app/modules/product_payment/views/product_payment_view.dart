import 'package:automoment/app/constants/app_config.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/product_payment_controller.dart';
import '../../../constants/app_text_styles.dart';
import '../../../constants/app_color.dart';

class ProductPaymentView extends GetView<ProductPaymentController> {
  const ProductPaymentView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Payment', style: AppTextStyles.titleText),
        elevation: 1,
        automaticallyImplyLeading: true,
        actions: [
          IconButton(
            onPressed: () => controller.switchCurrency(),
            icon: Image.asset(
              'assets/images/sgdmyr.png',
              width: 30,
            ),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.orderDetails.value == null) {
          return Container();
        }

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Order Summary Section
                const Text(
                  'Order Summary',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(color: Colors.grey[300]!),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        // Order items list
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: (controller.orderDetails.value!['items'] as List?)?.length ?? 0,
                          itemBuilder: (context, index) {
                            final item = (controller.orderDetails.value!['items'] as List)[index];
                            String? productImage = AppConfig.storageUrl + item['product_image'];
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.network(
                                      productImage,
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return Container(
                                          width: 60,
                                          height: 60,
                                          color: Colors.grey[200],
                                          child: const Icon(Icons.image_not_supported),
                                        );
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          item['product_name'] ?? '',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          '${item['quantity']} x ${controller.currencyPrefix.value} ${controller.currency.value == 'SGD' ? (item['price_sgd'] ?? '0.00') : (item['price_myr'] ?? '0.00')}',
                                          style: const TextStyle(
                                            fontSize: 14,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Total Amount',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${controller.currencyPrefix.value} ${controller.totalAmount.value.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColor.redButtonColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                
                Obx(() {
                  final isProcessing = controller.isProcessingPayment.value;
                  return ElevatedButton(
                    onPressed: isProcessing ? null : controller.makeStripePayment,
                    style: ButtonStyle(
                      foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                      backgroundColor: WidgetStateProperty.resolveWith<Color>(
                        (states) => AppColor.primaryButtonColor
                      ),
                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(18.0),
                          side: const BorderSide(
                              color: AppColor.primaryButtonColor),
                        ),
                      ),
                    ),
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      height: 50,
                      child: const Center(
                        child: Text(
                              'Pay',
                              style: TextStyle(fontSize: 16),
                            ),
                      ),
                    ),
                  );
                }),
                // const SizedBox(height: 20),
                // Obx(() {
                //   final isProcessing = controller.isProcessingPayment.value;
                //   return controller.currency.value == "SGD"
                //       ? ElevatedButton(
                //           onPressed: isProcessing ? null : controller.makePayNowPayment,
                //           style: ButtonStyle(
                //             foregroundColor: MaterialStateProperty.all<Color>(Colors.white),
                //             backgroundColor: MaterialStateProperty.resolveWith<Color>(
                //               (states) => AppColor.primaryButtonColor,
                //             ),
                //             shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                //               RoundedRectangleBorder(
                //                 borderRadius: BorderRadius.circular(18.0),
                //                 side: const BorderSide(
                //                     color: AppColor.primaryButtonColor,
                //                   )
                //               ),
                //             ),
                //           ),
                //           child: Container(
                //             width: double.infinity,
                //             padding: const EdgeInsets.only(left: 20, right: 20),
                //             height: 50,
                //             child: const Center(
                //               child: Text(
                //                     'Pay with PayNow',
                //                     style: TextStyle(fontSize: 16),
                //                   ),
                //             ),
                //           ),
                //         )
                //       : const SizedBox();
                // }),
              ],
            ),
          ),
        );
      }),
    );
  }
}
