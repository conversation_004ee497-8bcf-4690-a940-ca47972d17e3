import 'dart:convert';

import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/models/car_listing_model.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import '../../../models/product_model.dart';
import '../../../services/api_client.dart';

class StoreController extends BaseScreenController {
  final UserController userController = Get.put(UserController());

  var isLoading = false.obs;
  var productList = <Product>[].obs;
  var carListingList = <CarListing>[].obs;

  @override
  String get screenName => 'Store';

  @override
  void onInit() {
    super.onInit();
    fetchProductsAndCarListings();
  }

  Future<void> fetchProductsAndCarListings() async {
    EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    try {
      var response =
          await ApiClient.getProductsAndCarListings(userController.getToken());
      var list = jsonDecode(response)['products'];
      var list2 = jsonDecode(response)['car_listings'];
      var l = <Product>[];

      list.forEach((dynamic d) {
        var n = Product.fromJson(d);
        l.add(n);
      });

      productList.clear();
      productList.value = l;

      //

      var l2 = <CarListing>[];

      list2.forEach((dynamic d) {
        var n = CarListing.fromJson(d);
        l2.add(n);
      });

      carListingList.clear();
      carListingList.value = l2;

      // Log store content loaded event
      await analytics.logCustomEvent(
        name: 'store_content_loaded',
        parameters: {
          'products_count': l.length,
          'car_listings_count': l2.length,
        },
      );

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> fetchProducts() async {
    EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    try {
      var response = await ApiClient.getProducts(userController.getToken());
      var list = jsonDecode(response)['products'];
      var l = <Product>[];

      list.forEach((dynamic d) {
        var n = Product.fromJson(d);
        l.add(n);
      });

      productList.clear();
      productList.value = l;

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> fetchCarListings() async {
    EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    try {
      var response = await ApiClient.getCarListings(userController.getToken());
      var list = jsonDecode(response)['car_listings'];
      var l = <CarListing>[];

      list.forEach((dynamic d) {
        var n = CarListing.fromJson(d);
        l.add(n);
      });

      carListingList.clear();
      carListingList.value = l;

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
