import 'package:automoment/app/modules/store/views/car_listing_view.dart';
import 'package:automoment/app/modules/store/views/products_view.dart';
import 'package:contained_tab_bar_view/contained_tab_bar_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../controllers/store_controller.dart';
import '../../cart/controllers/cart_controller.dart';

class StoreView extends GetView<StoreController> {
  StoreView({super.key});

  final cartController = Get.put(CartController());

  @override
  Widget build(BuildContext context) {
    // Fetch cart data when view loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cartController.fetchCartFromApi();
    });

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
          icon: const Icon(Icons.menu),
          iconSize: 30,
          color: Colors.black,
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                padding: const EdgeInsets.only(right: 10, left: 0),
                onPressed: () => Get.toNamed(Routes.CART),
                icon: const Icon(
                  Icons.shopping_cart,
                  color: Colors.black,
                  size: 30,
                ),
              ),
              Positioned(
                right: 8,
                top: 8,
                child: Obx(() {
                  final count = cartController.cartCount.value;
                  return count > 0
                      ? Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: AppColor.redButtonColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 16,
                            minHeight: 16,
                          ),
                          child: Text(
                            count.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : const SizedBox();
                }),
              ),
            ],
          ),
        ],
        title: const Text('Store', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      body: ContainedTabBarView(
        tabs: const [
          Text('Track Car Listing'),
          Text('Store'),
        ],
        views: [
          RefreshIndicator(
            onRefresh: () async {
              return Future.delayed(const Duration(seconds: 1), () {
                controller.fetchCarListings();
              });
            },
            child: CarListingsView(),
          ),
          RefreshIndicator(
            onRefresh: () async {
              return Future.delayed(const Duration(seconds: 1), () {
                controller.fetchProducts();
              });
            },
            child: ProductsView(),
          ),
        ],
      )
    );
  }
}
