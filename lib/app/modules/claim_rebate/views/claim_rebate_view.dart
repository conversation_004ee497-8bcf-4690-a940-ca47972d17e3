import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/constants/app_config.dart';
import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:automoment/app/modules/shared/support_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/claim_rebate_controller.dart';

class ClaimRebateView extends StatelessWidget {
  ClaimRebateView({super.key});
  final controller = Get.put(ClaimRebateController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Claim Rebate', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Obx(() => controller.image.value.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        "${AppConfig.storageUrl}${controller.image.value}",
                        width: double.infinity,
                        height: 200,
                        fit: BoxFit.cover,
                      ),
                    )
                  : const SizedBox.shrink()),
              const SizedBox(height: 24),
              Obx(() {
                if (controller.isSubmitted.value) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Submission Status',
                        style: AppTextStyles.titleText.copyWith(fontSize: 24),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.submissionStatus.value,
                        style: AppTextStyles.normalText,
                      ),
                    ],
                  );
                } else {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Complete the Activity',
                        style: AppTextStyles.titleText.copyWith(fontSize: 24),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.activity.value,
                        style: AppTextStyles.normalText,
                      ),
                      const SizedBox(height: 24),
                      TextField(
                        controller: controller.linkController,
                        decoration: InputDecoration(
                          hintText:
                              'Please input the URL or social media handle',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: Colors.grey[200],
                        ),
                        maxLines: 3,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                            style: ButtonStyle(
                                foregroundColor:
                                    WidgetStateProperty.all<Color>(
                                        Colors.white),
                                backgroundColor:
                                    WidgetStateProperty.all<Color>(
                                        AppColor.primaryButtonColor),
                                shape: WidgetStateProperty.all<
                                        RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(18.0),
                                        side: const BorderSide(
                                            color:
                                                AppColor.primaryButtonColor)))),
                            onPressed: () {
                              controller.submitClaim();
                            },
                            child: Container(
                              padding:
                                  const EdgeInsets.only(left: 20, right: 20),
                              height: 50,
                              child: const Center(
                                child: Text("Claim Rebate",
                                    style: TextStyle(fontSize: 15)),
                              ),
                            )),
                      ),
                    ],
                  );
                }
              }),
            ],
          ),
        ),
      ),
    );
  }
}
