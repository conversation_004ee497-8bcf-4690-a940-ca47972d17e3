import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:group_button/group_button.dart';
import 'package:intl/intl.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../../services/api_client.dart';

class EventsController extends BaseScreenController {
  @override
  String get screenName => "Events";

  var isLoading = false.obs;
  var allEventList = <Event>[].obs;
  var eventList = <Event>[].obs;
  //var filteredEventList = <Event>[].obs;

  final UserController userController = Get.put(UserController());

  var filterOrganisers = [].obs;
  var filterMonths = [].obs;
  // List<String> filterOrganisers = ["Automoment", "MTR", "MC<PERSON>", "JHR", "ABC"];
  // List<String> filterMonths = ["December", "January", "February"];

  final organiserController = GroupButtonController();
  final monthController = GroupButtonController();

  @override
  void onInit() {
    super.onInit();
    isLoading.value = true;
    fetchEvents();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<void> fetchEvents() async {
    EasyLoading.show(status: 'Loading...');
    try {
      var response = await ApiClient.getEvents();
      var list = jsonDecode(response)['data'];
      //var l = <Event>[];

      eventList.clear();
      allEventList.clear();

      list.forEach((dynamic d) {
        var n = Event.fromJson(d);
        eventList.add(n);
        allEventList.add(n);
      });

      filterOrganisers.value =
          allEventList.map((e) => e.organizer).toSet().toList();
      filterMonths.value = allEventList
          .map((e) => DateFormat('MMMM')
              .format(DateTime(e.eventDate!.year, e.eventDate!.month)))
          .toSet()
          .toList();

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  void filterDataAndReload() {
    List<String> selectedFilterOrganisers = [];
    List<String> selectedFilterMonths = [];

    for (var i in organiserController.selectedIndexes) {
      selectedFilterOrganisers.add(filterOrganisers[i]);
    }

    for (var i in monthController.selectedIndexes) {
      selectedFilterMonths.add(filterMonths[i]);
    }

    eventList.clear();

    for (var event in allEventList) {
      if (selectedFilterMonths.isNotEmpty &&
          selectedFilterOrganisers.isNotEmpty) {
        var monthName = DateFormat('MMMM')
            .format(DateTime(event.eventDate!.year, event.eventDate!.month));

        if (selectedFilterOrganisers.contains(event.organizer) &&
            selectedFilterMonths.contains(monthName)) {
          eventList.add(event);
        }
      } else if (selectedFilterMonths.isNotEmpty) {
        var monthName = DateFormat('MMMM')
            .format(DateTime(event.eventDate!.year, event.eventDate!.month));

        if (selectedFilterMonths.contains(monthName)) {
          eventList.add(event);
        }
      } else if (selectedFilterOrganisers.isNotEmpty) {
        if (selectedFilterOrganisers.contains(event.organizer)) {
          debugPrint(event.organizer);
          eventList.add(event);
        }
      } else {
        eventList.add(event);
      }
    }

    debugPrint("Event list: ${allEventList.length}");
    debugPrint("Filtered: ${eventList.length.toString()}");
  }
}
