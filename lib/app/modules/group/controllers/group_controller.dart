import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/driver_lookup_model.dart';
import '../../../models/event_modal.dart';
import '../../../models/participant_model.dart';
import '../../../models/vehicle_model.dart';
import '../../../services/api_client.dart';

class GroupController extends BaseScreenController {
  @override
  String get screenName => 'Group';

  final formKey = GlobalKey<FormState>();
  var event = Event().obs;

  var participants = <Participant>[].obs;

  final ScrollController scrollController = ScrollController();

  final userController = Get.put(UserController());

  var showSubmitButton = false.obs;

  var vehicle = Vehicle().obs;
  var currentIndex = 0.obs;

  var drivers = <MainDriver>[].obs;
  var selectedDriver = MainDriver().obs;
  var filteredDrivers = <MainDriver>[].obs;
  var searchQuery = ''.obs;
  var driverVehicle = "".obs;

  var vehicles = <SimpleVehicle>[].obs;
  var selectedVehicle = SimpleVehicle().obs;

  var currentUser = MainDriver().obs;

  @override
  Future<void> onInit() async {
    super.onInit();

    event.value = Get.arguments;

    await fetchDrivers();

    addParticipant(Participant(
      userId: currentUser.value.id,
      vehicleId: currentUser.value.vehicles?.isNotEmpty == true
          ? currentUser.value.vehicles!.first.id
          : null,
      name: currentUser.value.name ?? '',
      email: StringUtil.maskEmail(currentUser.value.email ?? ''),
      mobileNumber:
          StringUtil.maskMobileNumber(currentUser.value.mobileNumber ?? ''),
      vehicle: currentUser.value.vehicles?.isNotEmpty == true
          ? currentUser.value.vehicles!.first.name
          : null,
    ));

    addParticipant(Participant());
  }

  addParticipant(Participant participant) {
    participants.add(participant);
  }

  void filterDrivers(String query) {
    debugPrint("filterDrivers: $query");
    searchQuery.value = query;
    if (query.isEmpty || query.length < 3) {
      filteredDrivers.clear();
    } else {
      filteredDrivers.assignAll(drivers.where((driver) =>
          (driver.name != null &&
              driver.name!.toLowerCase().contains(query.toLowerCase())) ||
          (driver.mobileNumber != null &&
              driver.mobileNumber!
                  .toLowerCase()
                  .contains(query.toLowerCase())) ||
          (driver.email != null &&
              driver.email!.toLowerCase().contains(query.toLowerCase()))));
    }
  }

  Future<void> fetchDrivers() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      final response =
          await ApiClient.lookupAllDriver(userController.getToken());
      EasyLoading.dismiss();

      bool success = jsonDecode(response)['success'];
      if (success) {
        var list = jsonDecode(response)['drivers'] as List<dynamic>;
        drivers.value = list
            .map((e) => MainDriver.fromJson(e as Map<String, dynamic>))
            .toList()
            .cast<MainDriver>();
        showSubmitButton.value = true;

        // find current user
        try {
          currentUser.value = drivers.firstWhere(
              (element) => element.id == userController.user.value.id);
        } catch (e) {
          debugPrint('Current user not found in drivers list');

          // Handle the case when user is not found - you might want to set a default value or show a message
          currentUser.value =
              MainDriver(); // Assuming MainDriver has a default constructor
        }
      }
    } catch (e) {
      debugPrint('Error fetching drivers: $e');
    } finally {
      EasyLoading.dismiss();
    }
  }

  Future<void> getVehicles() async {
    EasyLoading.show(status: 'Loading...');
    try {
      var response = await ApiClient.getVehicles(
          userController.getToken(), userController.user.value.id!);
      var list = jsonDecode(response)['data'];
      var vehicles = list.map((e) => Vehicle.fromJson(e)).toList();
      vehicle.value = vehicles.first;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
