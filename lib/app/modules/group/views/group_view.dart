import 'package:automoment/app/models/driver_lookup_model.dart';
import 'package:automoment/app/modules/group/views/lookup_user_view.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/participant_model.dart';
import '../../../models/user_model.dart';
import '../../../routes/app_pages.dart';
import '../../shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/group_controller.dart';
import 'lookup_vehicle_view.dart';

class GroupView extends StatelessWidget {
  GroupView({super.key});
  final GroupController controller = Get.put(GroupController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title:
              const Text('Group Registration', style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: Container(
          padding: const EdgeInsets.all(16),
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // FloatingActionButton(
              //   backgroundColor: AppColor.secondaryColor,
              //   foregroundColor: Colors.white,
              //   heroTag: 'addParticipant',
              //   onPressed: () {
              //     controller.addParticipant(Participant());
              //
              //     // Scroll to bottom after the frame is rendered and layout is updated
              //     WidgetsBinding.instance.addPostFrameCallback((_) {
              //       // Use Future.delayed to ensure the layout has been updated
              //       Future.delayed(Duration(milliseconds: 100), () {
              //         if (controller.scrollController.hasClients) {
              //           controller.scrollController.animateTo(
              //             controller.scrollController.position.maxScrollExtent,
              //             duration: Duration(milliseconds: 300),
              //             curve: Curves.easeOut,
              //           );
              //         }
              //       });
              //     });
              //   },
              //   child: const Icon(Icons.person_add),
              // ),
              // const SizedBox(height: 16),
              SupportButton(),
            ],
          ),
        ),
        body: SingleChildScrollView(
          controller: controller.scrollController,
          child: Form(
            key: controller.formKey,
            child: Obx(() {
              return Column(children: [
                for (var i = 0; i < controller.participants.length; i++)
                  participantForm(context, controller.participants[i], i),
                if (controller.showSubmitButton.value) ...[
                  addButton(context),
                  submitButton(context),
                ]
              ]);
            }),
          ),
        ));
  }

  Widget participantForm(
      BuildContext context, Participant participant, int index) {
    int? userId = participant.userId;
    int number = index + 1;
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5),
      ),
      child: Padding(
          padding: const EdgeInsets.all(29),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  Text(
                    'Participant #$number',
                    style: AppTextStyles.titleText.copyWith(fontSize: 16),
                  ),
                  const Spacer(),
                  if (number != 1) ...[
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        showDialog(
                          context: Get.context!,
                          builder: (context) => KConfirmDialogView(
                            content:
                                "Are you sure want to delete participant #$number?",
                            callbackYes: () async {
                              controller.participants.removeAt(index);
                              Get.back();
                            },
                            callbackNo: () {
                              Get.back();
                            },
                          ),
                        );
                      },
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 0),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: buildTextField(
                      label: 'Name',
                      icon: Icons.person,
                      initialValue: participant.name,
                      readOnly: userId != null ? true : false,
                      onSaved: (value) => participant.name = value,
                    ),
                  ),
                  if (number != 1)
                    IconButton(
                      icon: const Icon(Icons.list_alt),
                      onPressed: () async {
                        controller.currentIndex.value = index;
                        controller.filteredDrivers.clear();

                        debugPrint('show UserLookupView');
                        await Get.dialog<User>(UserLookupView());
                      },
                    ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: buildTextField(
                      label: 'Vehicle',
                      icon: Icons.directions_car,
                      initialValue: participant.vehicle,
                      readOnly: userId != null && participant.vehicleId != null,
                      onSaved: (value) => participant.vehicle = value,
                    ),
                  ),
                  if (controller.vehicles.length > 1)
                    IconButton(
                      icon: const Icon(Icons.list_alt),
                      onPressed: () async {
                        controller.currentIndex.value = index;
                        int? userId = controller.participants[index].userId;

                        if (userId != null) {
                          var driver = controller.drivers.firstWhere(
                            (driver) => driver.id == userId,
                            orElse: () => MainDriver(),
                          );

                          controller.vehicles.assignAll(driver.vehicles ?? []);
                        } else {
                          controller.vehicles.clear();
                        }

                        debugPrint('show VehicleLookupView');
                        await Get.dialog(VehicleLookupView());
                      },
                    ),
                ],
              ),
              const SizedBox(height: 16),
              buildTextField(
                label: 'Mobile Number',
                icon: Icons.phone_android,
                initialValue: participant.mobileNumber,
                keyboardType: TextInputType.phone,
                readOnly: userId != null ? true : false,
                onSaved: (value) => participant.mobileNumber = value,
              ),
              const SizedBox(height: 16),
              buildTextField(
                label: 'Email',
                icon: Icons.email,
                initialValue: participant.email,
                keyboardType: TextInputType.emailAddress,
                readOnly: userId != null ? true : false,
                onSaved: (value) => participant.email = value,
              ),
              const SizedBox(height: 16),
            ],
          )),
    );
  }

  Widget buildTextField({
    required String label,
    required IconData icon,
    String? initialValue,
    TextInputType? keyboardType,
    bool? readOnly = false,
    required Function(String?) onSaved,
  }) {
    TextEditingController controller = TextEditingController();
    controller.text = initialValue ?? '';

    return TextFormField(
      controller: controller,
      readOnly: readOnly ?? false,
      autocorrect: false,
      decoration: InputDecoration(
        labelText: label,
        // prefixIcon: Icon(icon),
        // border: OutlineInputBorder(
        //   borderRadius: BorderRadius.circular(8),
        // ),
        // filled: true,
        // fillColor: Colors.grey[200],
      ),
      keyboardType: keyboardType,
      validator: (value) {
        if ((value == null || value.isEmpty) && readOnly == false) {
          return 'Please enter $label';
        }

        if (label == 'Mobile Number') {
          // Allow digits and asterisks
          String pattern = r'^[0-9*]+$';
          RegExp regex = RegExp(pattern);
          if (value != null && !regex.hasMatch(value) && readOnly == false) {
            return 'Please enter only numeric characters';
          }
        }

        if (label == 'Email') {
          // Allow standard email format with asterisks
          String pattern = r'^[\w\-\.*]+@[\w\-\.*]+\.[\w\-\.*]{2,}$';
          RegExp regex = RegExp(pattern);
          if (value != null && !regex.hasMatch(value)) {
            return 'Please enter a valid email address';
          }
        }

        return null;
      },
      onChanged: onSaved,
      onSaved: onSaved,
    );
  }

  Widget addButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0, bottom: 0, left: 20, right: 20),
      child: ElevatedButton(
          onPressed: () async {
            controller.addParticipant(Participant());

            // Scroll to bottom after the frame is rendered and layout is updated
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // Use Future.delayed to ensure the layout has been updated
              Future.delayed(const Duration(milliseconds: 100), () {
                if (controller.scrollController.hasClients) {
                  controller.scrollController.animateTo(
                    controller.scrollController.position.maxScrollExtent,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                }
              });
            });
          },
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Add Participant", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget submitButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10, bottom: 20, left: 20, right: 20),
      child: ElevatedButton(
          onPressed: () async {
            if (controller.formKey.currentState!.validate()) {
              controller.formKey.currentState!.save();

              Get.toNamed(Routes.BOOKING, arguments: {
                "event": controller.event.value,
                "type": "group",
                "participant": controller.participants
              });

              // register as group

              // go to payment page
              // Get.toNamed(Routes.MAKE_PAYMENT, arguments: {"event": controller.event.value, "type": "group", "qty": controller.participants.length - 1});
            }
          },
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Continue", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }
}
