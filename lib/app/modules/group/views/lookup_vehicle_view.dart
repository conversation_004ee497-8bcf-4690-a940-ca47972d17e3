import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_text_styles.dart';
import '../../../models/driver_lookup_model.dart';
import '../controllers/group_controller.dart';

class VehicleLookupView extends StatelessWidget {
  late final GroupController controller;

  VehicleLookupView({super.key}) {
    controller = Get.find<GroupController>();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Select Vehicle',
          style: AppTextStyles.titleText.copyWith(fontSize: 18)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Please select the vehicle from the list below:',
              style: AppTextStyles.normalText),
          const SizedBox(height: 20),
          Obx(() => SizedBox(
                height: 200,
                width: double.maxFinite,
                child: ListView.separated(
                  itemCount: controller.vehicles.length,
                  separatorBuilder: (context, index) =>
                      Divider(height: 1, color: Colors.grey[300]),
                  itemBuilder: (context, index) {
                    SimpleVehicle vehicle = controller.vehicles[index];

                    return Obx(() => Container(
                          decoration: BoxDecoration(
                            color: controller.selectedVehicle.value == vehicle
                                ? Colors.blue.withAlpha(26)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 0, vertical: 0),
                            title: Text(
                              vehicle.name ?? '',
                              style: AppTextStyles.normalText
                                  .copyWith(fontWeight: FontWeight.bold),
                            ),
                            trailing: const Icon(Icons.chevron_right,
                                color: Colors.grey),
                            onTap: () async {
                              controller.selectedVehicle.value = vehicle;
                              debugPrint(
                                  "currentIndex: ${controller.currentIndex.value}");
                              debugPrint(
                                  'Selected vehicle: ${controller.selectedVehicle.value.name}');

                              controller
                                  .participants[controller.currentIndex.value]
                                  .vehicle = vehicle.name;
                              controller
                                  .participants[controller.currentIndex.value]
                                  .vehicleId = vehicle.id;
                              controller.participants.refresh();

                              Get.back();
                            },
                          ),
                        ));
                  },
                ),
              )),
        ],
      ),
    );
  }
}
