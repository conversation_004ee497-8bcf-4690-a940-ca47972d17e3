import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/app_text_styles.dart';
import '../controllers/group_controller.dart';

class UserLookupView extends StatelessWidget {
  late final GroupController controller;

  UserLookupView({super.key}) {
    controller = Get.find<GroupController>();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Select Driver',
          style: AppTextStyles.titleText.copyWith(fontSize: 18)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
              'Please enter the driver\'s name, email, or mobile number and choose from the list below:',
              style: AppTextStyles.normalText),
          const SizedBox(height: 10),
          TextField(
            decoration: InputDecoration(
              hintText: 'name, email or mobile number',
              prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: Colors.grey[500]!),
              ),
              filled: true,
              fillColor: Colors.grey[100],
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              hintStyle: TextStyle(color: Colors.grey[500]),
            ),
            style: const TextStyle(fontSize: 16),
            cursorColor: Colors.blue,
            onChanged: controller.filterDrivers,
          ),
          const SizedBox(height: 15),
          Obx(() => SizedBox(
                height: 184,
                width: double.maxFinite,
                child: ListView.separated(
                  itemCount: controller.filteredDrivers.length,
                  separatorBuilder: (context, index) =>
                      Divider(height: 1, color: Colors.grey[300]),
                  itemBuilder: (context, index) {
                    final user = controller.filteredDrivers[index];

                    String contact = user.email != null
                        ? StringUtil.maskEmail(user.email!)
                        : StringUtil.maskMobileNumber(user.mobileNumber!);

                    return Obx(() => Container(
                          decoration: BoxDecoration(
                            color: controller.selectedDriver.value == user
                                ? Colors.blue.withAlpha(26)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 0, vertical: 0),
                            leading: CircleAvatar(
                              radius:
                                  20, // Adjust this value to change the size of the avatar
                              backgroundColor: Colors
                                  .blue, // This will be visible if the image fails to load
                              child: ClipOval(
                                child: Image.network(
                                  "${user.photo}",
                                  width: 40,
                                  height: 40,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    // This will be shown if the image fails to load
                                    return Text(
                                      user.name?.isNotEmpty == true
                                          ? user.name![0].toUpperCase()
                                          : '?',
                                      style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold),
                                    );
                                  },
                                ),
                              ),
                            ),
                            title: Text(
                              user.name ?? '',
                              style: AppTextStyles.normalText
                                  .copyWith(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 4),
                                Text(contact,
                                    style: AppTextStyles.normalText
                                        .copyWith(fontSize: 12)),
                              ],
                            ),
                            trailing: const Icon(Icons.chevron_right,
                                color: Colors.grey),
                            onTap: () async {
                              controller.selectedDriver.value = user;
                              debugPrint(
                                  "currentIndex: ${controller.currentIndex.value}");
                              debugPrint(
                                  'Selected user: ${controller.selectedDriver.value.name}');

                              controller
                                  .participants[controller.currentIndex.value]
                                  .name = controller.selectedDriver.value.name;

                              controller
                                      .participants[controller.currentIndex.value]
                                      .mobileNumber =
                                  StringUtil.maskMobileNumber(controller
                                          .selectedDriver.value.mobileNumber ??
                                      '');

                              controller
                                  .participants[controller.currentIndex.value]
                                  .email = StringUtil.maskEmail(controller
                                      .selectedDriver.value.email ??
                                  '');

                              controller
                                  .participants[controller.currentIndex.value]
                                  .userId = controller.selectedDriver.value.id;

                              controller
                                  .participants[controller.currentIndex.value]
                                  .vehicle = controller.selectedDriver.value
                                          .vehicles?.isNotEmpty ==
                                      true
                                  ? controller
                                      .selectedDriver.value.vehicles![0].name
                                  : null;

                              controller
                                  .participants[controller.currentIndex.value]
                                  .vehicleId = controller.selectedDriver.value
                                          .vehicles?.isNotEmpty ==
                                      true
                                  ? controller
                                      .selectedDriver.value.vehicles![0].id
                                  : null;

                              controller
                                  .participants[controller.currentIndex.value]
                                  .userPhoto = controller.selectedDriver.value
                                          .vehicles?.isNotEmpty ==
                                      true
                                  ? controller.selectedDriver.value.photo
                                  : null;

                              controller.vehicles.assignAll(
                                  controller.selectedDriver.value.vehicles ??
                                      []);

                              controller.participants.refresh();

                              debugPrint(
                                  "participants: ${controller.participants}");
                              Get.back();
                            },
                          ),
                        ));
                  },
                ),
              )),
        ],
      ),
    );
  }
}
