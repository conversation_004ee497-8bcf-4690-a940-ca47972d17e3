import 'dart:io';

import 'package:automoment/app/modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../constants/app_color.dart';
import '../controllers/force_upgrade_controller.dart';

class ForceUpgradeView extends GetView<ForceUpgradeController> {
  const ForceUpgradeView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.black,
        child: AlertDialog(
            title: const Text("From Automoment"),
            content: const Text("Please kindly update the app to continue using it. You may click the link below for the update. Thank you!"),
            actions: [
              TextButton(
                child: const Text(
                  "Update",
                  style: TextStyle(color: AppColor.secondaryColor),
                ),
                onPressed: () async {
                  if (Platform.isAndroid) {
                    if (!await launchUrl(
                      Uri.parse("https://play.google.com/store/apps/details?id=sg.com.automoment.automoment"),
                      mode: LaunchMode.externalApplication,
                    )) {
                      throw 'Could not launch Play Store';
                    }
                  }
                  else if (Platform.isIOS) {
                    if (!await launchUrl(
                      Uri.parse("https://apps.apple.com/us/app/automoment/id1659173896"),
                      mode: LaunchMode.externalApplication,
                    )) {
                      throw 'Could not launch Play Store';
                    }
                  }
                  else {
                    Get.dialog(const KMessageDialogView(content: "Your device is not supported."));
                  }
                },
              )
            ]
        )
      ),
    );
  }
}
