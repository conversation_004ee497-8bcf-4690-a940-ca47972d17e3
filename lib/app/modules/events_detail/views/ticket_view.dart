import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_text_styles.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../shared/dotted_line_painter.dart';

class TicketView extends StatelessWidget {

  final Event event;
  final String pitNo;
  final String vehicle;
  final String qrcode;
  final String registrationType;
  final String mainDriver;
  final String mainDriverVehicle;
  final userController = Get.put(UserController());

  TicketView({
    super.key,
    required this.event,
    required this.vehicle,
    required this.qrcode,
    required this.pitNo,
    required this.registrationType,
    required this.mainDriver,
    required this.mainDriverVehicle
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 60, bottom: 20),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Your Ticket",
            style: AppTextStyles.bigHeaderText.copyWith(
              fontSize: 22,
              color: AppColor.kTextColor,
            ),
          ),
          Text(
            "Review your ticket",
            style: AppTextStyles.normalText.copyWith(
              fontSize: 14,
              color: AppColor.kTextGrayColor,
              fontStyle: FontStyle.normal,
              fontWeight: FontWeight.bold
            ),
          ),
          const SizedBox(height: 20),
          // blue header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
            decoration: BoxDecoration(
              color: getTicketColor(registrationType),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "${event.title}",
                  style: AppTextStyles.bigHeaderText.copyWith(
                    fontSize: 22,
                    color: Colors.white
                  ),
                ),
                const SizedBox(height: 10),
                event.organizer != null ?
                  Text(
                    "Organised by ${event.organizer}",
                    style: AppTextStyles.normalText.copyWith(
                      fontSize: 16,
                      color: Colors.white
                    ),
                  ) : const SizedBox(),
              ]
            ),
          ),
          // content
          Container(
            padding: const EdgeInsets.all(30),
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // date
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Date",
                          style: AppTextStyles.ticketFieldHeader,
                        ),
                        const SizedBox(height: 5),
                        Text(
                          DateFormat("dd MMMM yyyy").format(event.eventDate!),
                          style: AppTextStyles.ticketFieldValue,
                        )
                      ],
                    ),
                    // time
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Time",
                          style: AppTextStyles.ticketFieldHeader,
                        ),
                        const SizedBox(height: 5),
                        Text(
                          DateFormat("hh:mm a").format(event.eventDate!),
                          style: AppTextStyles.ticketFieldValue,
                        )
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Container(
                  height: 2,
                  decoration: const BoxDecoration(
                    color: AppColor.kLightBgColor,
                  ),
                  child: Container(),
                ),
                const SizedBox(height: 20),

                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const Text(
                      "Registration: ",
                      style: AppTextStyles.ticketFieldHeader,
                    ),
                    const SizedBox(width: 3),
                    Text(
                      registrationType,
                      style: AppTextStyles.ticketFieldValue,
                    ),
                  ],
                ),

                const SizedBox(height: 5),

                // name
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const Text(
                      "Name: ",
                      style: AppTextStyles.ticketFieldHeader,
                    ),
                    const SizedBox(width: 3),
                    Text(
                      userController.user.value.name!,
                      style: AppTextStyles.ticketFieldValue,
                    ),
                  ],
                ),

                if (registrationType != "Passenger") ...[
                  const SizedBox(height: 5),

                  // vehicle
                  Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Text(
                          "Vehicle:",
                          style: AppTextStyles.ticketFieldHeader,
                          maxLines: 1,
                        ),
                        const SizedBox(width: 3),
                        Flexible(
                          child: Text(
                            vehicle,
                            style: AppTextStyles.ticketFieldValue,
                            maxLines: 1,
                          ),
                        ),
                      ]
                  ),
                ],

                if (registrationType == "Additional Driver") ...[

                  // separate line
                  const SizedBox(height: 20),
                  Container(
                    height: 2,
                    decoration: const BoxDecoration(
                      color: AppColor.kLightBgColor,
                    ),
                    child: Container(),
                  ),
                  const SizedBox(height: 20),

                  // main driver

                  Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const Text(
                          "Main Driver:",
                          style: AppTextStyles.ticketFieldHeader,
                          maxLines: 1,
                        ),
                        const SizedBox(width: 3),
                        Flexible(
                          child: Text(
                            mainDriver,
                            style: AppTextStyles.ticketFieldValue,
                            maxLines: 1,
                          ),
                        ),
                      ]
                  ),

                  //const SizedBox(height: 5),

                  // vehicle
                  // Row(
                  //     mainAxisAlignment: MainAxisAlignment.start,
                  //     children: [
                  //       const Text(
                  //         "Vehicle:",
                  //         style: AppTextStyles.ticketFieldHeader,
                  //         maxLines: 1,
                  //       ),
                  //       const SizedBox(width: 3),
                  //       Flexible(
                  //         child: Text(
                  //           mainDriverVehicle,
                  //           style: AppTextStyles.ticketFieldValue,
                  //           maxLines: 1,
                  //         ),
                  //       ),
                  //     ]
                  // ),
                ],

                const SizedBox(height: 20),
                Container(
                  height: 2,
                  decoration: const BoxDecoration(
                    color: AppColor.kLightBgColor,
                  ),
                  child: Container(),
                ),
                const SizedBox(height: 20),

                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    // PIT No.
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "PIT No.",
                          style: AppTextStyles.ticketFieldValue.copyWith(
                              //fontSize: 25,
                              fontWeight: FontWeight.w900
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          pitNo,
                          style: AppTextStyles.ticketFieldValue.copyWith(
                            fontSize: 40,
                            fontWeight: FontWeight.w900
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),

                    // dot separator
                    const SizedBox(width: 0),
                    Padding(
                      padding: const EdgeInsets.only(top: 30),
                      child: Transform.rotate(
                        angle: -pi / 2,
                        child: CustomPaint(
                          size: const Size(60, 1),
                          painter: DottedLinePainter(),
                        ),
                      ),
                    ),

                    //venue
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Venue",
                            style: AppTextStyles.ticketFieldValue.copyWith(
                              //fontSize: 25,
                                fontWeight: FontWeight.w900
                            ),
                          ),
                          const SizedBox(height: 5),
                          event.venue != null ?
                            Text(
                            "${event.venue}",
                            style: AppTextStyles.ticketFieldValue.copyWith(
                                fontWeight: FontWeight.w300
                            ),
                            //softWrap: true,
                            maxLines: 2,
                          ) :
                            Text(
                              "  -",
                              style: AppTextStyles.ticketFieldValue.copyWith(
                                  fontSize: 40,
                                  fontWeight: FontWeight.w900
                              ),
                              maxLines: 1,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.network(
                        qrcode,
                        width: 150,
                        height: 150,
                        errorBuilder: (context, error, stackTrace) {
                          return Image.asset(
                            'assets/images/image_placeholder.png',
                            width: 150,
                            height: 150,
                            fit: BoxFit.fitWidth,
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

              ]
            ),
          ),

        ]
      ),
    );
  }

  Color getTicketColor(String registrationType) {
    switch (registrationType) {
      case "Additional Driver":
        return AppColor.lightGreenColor;
      case "Passenger":
        return AppColor.lightYellowColor;
      default:
        return AppColor.secondaryColor;
    }
  }
}


