import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../controllers/events_detail_controller.dart';

class EventInfoView extends StatelessWidget {
  EventInfoView({super.key});

  final EventsDetailController controller = Get.put(EventsDetailController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: const [
              BoxShadow(
                  offset: Offset(0, 5),
                  //(0,-12) Bóng lên,(0,12) bóng xuống,, tuong tự cho trái phải
                  blurRadius: 10,
                  color: Colors.black26)
            ]),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                left: AppConfig.defaultPadding * 2,
                right: AppConfig.defaultPadding * 2,
                top: AppConfig.defaultPadding * 3,
              ),
              child: Text(controller.event.value.title ?? "",
                  style: AppTextStyles.titleText.copyWith(fontSize: 20)),
            ),
            Padding(
              padding: const EdgeInsets.only(
                left: (AppConfig.defaultPadding * 2) - 6,
                right: (AppConfig.defaultPadding * 2) - 6,
                top: AppConfig.defaultPadding * 2,
                bottom: AppConfig.defaultPadding * 3,
              ),
              child: controller.event.value.id == null
                  ? Container()
                  : SizedBox(
                      height: controller.webViewHeight.value,
                      child: showWebView(),
                    ),
            ),
          ],
        ),
      );
    });
  }

  showWebView() {
    return WebViewWidget(controller: controller.webViewController);
  }
}
