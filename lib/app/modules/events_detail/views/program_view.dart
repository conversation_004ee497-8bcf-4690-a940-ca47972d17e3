import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:timeline_tile/timeline_tile.dart';

import '../../../models/event_modal.dart';

class ProgramView extends StatelessWidget {
  final Event event;
  final String vehicle;
  final List<Program> programs;

  ProgramView({
    super.key,
    required this.event,
    required this.vehicle,
  }) : programs = event.programs ?? [];

  @override
  Widget build(BuildContext context) {

    return Padding(
      padding: const EdgeInsets.only(top: 10, bottom: 20),
      child: (programs.isNotEmpty) ? Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Event Itinerary",
            style: AppTextStyles.bigHeaderText.copyWith(
              fontSize: 22,
              color: AppColor.kTextColor,
            ),
          ),
          Text(
            "Experience the excitement of the race day",
            style: AppTextStyles.normalText.copyWith(
                fontSize: 14,
                color: AppColor.kTextGrayColor,
                fontStyle: FontStyle.normal,
                fontWeight: FontWeight.bold
            ),
          ),

          Column(
            children: programs.asMap().entries.map<Widget>((entry) {
              int idx = entry.key;
              var program = entry.value;
              return TimelineTile(
                alignment: TimelineAlign.manual,
                lineXY: 0.3,

                isFirst: idx == 0, // true if this is the first program
                isLast: idx == programs.length - 1, // true if this is the last program

                beforeLineStyle: const LineStyle(
                  color: AppColor.secondaryColor,
                  thickness: 5,
                ),

                indicatorStyle: IndicatorStyle(
                  width: 20,
                  color: AppColor.secondaryColor,
                  padding: const EdgeInsets.all(0),
                  //indicator: Image.asset('assets/hitchhiker_2.png'),
                  //https://fonts.google.com/icons
                  iconStyle: IconStyle(
                    fontSize: 15,
                    color: AppColor.kLightBgColor,
                    iconData: Icons.circle, //Expand Circle Right
                  ),
                ),

                startChild: Text(
                  program.time ?? "",
                  style: AppTextStyles.normalText.copyWith(
                      fontSize: 16
                  ),
                  textAlign: TextAlign.center,
                ),
                endChild: Container(
                  constraints: const BoxConstraints(
                    minHeight: 100,
                  ),
                  padding: const EdgeInsets.only(left: 20),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          program.title ?? "",
                          style: AppTextStyles.normalTextBold.copyWith(
                              fontSize: 16
                          ),
                          textAlign: TextAlign.left,
                        ),
                        const SizedBox(height: 10),
                        Text(
                          program.description ?? "",
                          style: AppTextStyles.normalText.copyWith(
                              fontSize: 13
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ]
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ) : const SizedBox(),
    );
  }
}
