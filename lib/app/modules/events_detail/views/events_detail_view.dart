import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:automoment/app/modules/events_detail/views/program_view.dart';
import 'package:automoment/app/modules/events_detail/views/ticket_view.dart';
import 'package:automoment/app/modules/events_detail/views/user_lookup_view.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:share_plus/share_plus.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../controllers/nav_history_controller.dart';
import '../../../models/user_model.dart';
import '../../../routes/app_pages.dart';
import '../../shared/custom_popup.dart';
import '../../shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/events_detail_controller.dart';
import '../../../controllers/user_controller.dart';
import 'event_info_view.dart';

class EventsDetailView extends StatelessWidget {
  EventsDetailView({super.key});

  final EventsDetailController controller = Get.put(EventsDetailController());
  final UserController userController = Get.put(UserController());
  final NavHistoryController navHistoryController =
      Get.put(NavHistoryController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColor.kLightBgColor,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          title: const Text(
            "",
            style: TextStyle(
                color: AppColor.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
          elevation: 1,
          actions: [
            GestureDetector(
              onTap: () {
                debugPrint("ontap");
                final box = context.findRenderObject() as RenderBox?;
                double w = box?.size.width ?? 820;
                Share.share(
                    '${controller.event.value.title} at ${controller.event.value.firebaseDynamicLink}',
                    sharePositionOrigin: Rect.fromLTRB(w - 100, 0, w, 0 + 100));
              },
              child: const Padding(
                  padding: EdgeInsets.only(right: 20),
                  child: Icon(
                    Icons.share,
                    color: Colors.white,
                  )),
            ),
          ],
        ),
        floatingActionButton: const SupportButton(),
        body: Obx(
          () => controller.event.value.id == null
              ? Container(
                  color: Colors.black,
                )
              : Stack(
                  children: [
                    // main view
                    ListView(padding: const EdgeInsets.only(top: 0), children: [
                      Stack(children: [
                        Stack(children: [
                          // img background on top
                          Container(
                            padding: const EdgeInsets.only(top: 0),
                            width: (Get.width),
                            height: 230,
                            margin: const EdgeInsets.all(0.0),
                            child: (controller.event.value.id == null)
                                ? Container()
                                : FadeInImage(
                                    placeholder: const AssetImage(
                                        'assets/images/image_placeholder.png'),
                                    image: NetworkImage(
                                        "${AppConfig.storageUrl}${controller.event.value.image}"),
                                    fit: BoxFit.cover,
                                    imageErrorBuilder:
                                        (context, error, stackTrace) {
                                      return const Image(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            'assets/images/image_placeholder.png'),
                                      );
                                    },
                                  ),
                          ),

                          // content
                          Container(
                            padding: const EdgeInsets.only(
                                top: 180,
                                bottom: AppConfig.defaultPadding * 2,
                                left: AppConfig.defaultPadding * 2,
                                right: AppConfig.defaultPadding * 2),
                            child: Column(
                              children: [
                                (controller.bookingStatus.value == 2 ||
                                        controller.bookingStatus.value == 3)
                                    ? TicketView(
                                        event: controller.event.value,
                                        qrcode: controller.qrcode.value,
                                        pitNo: controller.pitNo.value,
                                        vehicle: controller.vehicle.value,
                                        registrationType:
                                            controller.registrationType.value,
                                        mainDriver: "",
                                        mainDriverVehicle: "",
                                      )
                                    : Container(),
                                (controller.bookingStatus.value == 7)
                                    ? TicketView(
                                        event: controller.event.value,
                                        qrcode: controller.qrcode.value,
                                        pitNo: "-",
                                        vehicle: ".",
                                        registrationType:
                                            controller.registrationType.value,
                                        mainDriver: "",
                                        mainDriverVehicle: "",
                                      )
                                    : Container(),
                                (controller.bookingStatus.value == 9)
                                    ? TicketView(
                                        event: controller.event.value,
                                        qrcode: controller.qrcode.value,
                                        pitNo: controller.pitNo.value,
                                        vehicle: controller.vehicle.value,
                                        registrationType:
                                            controller.registrationType.value,
                                        mainDriver: controller.mainDriver.value,
                                        mainDriverVehicle:
                                            controller.mainDriverVehicle.value,
                                      )
                                    : Container(),
                                if (controller.bookingStatus.value == 2 ||
                                    controller.bookingStatus.value == 3 ||
                                    controller.bookingStatus.value == 7 ||
                                    controller.bookingStatus.value == 9)
                                  ProgramView(
                                      event: controller.event.value,
                                      vehicle: controller.vehicle.value),
                                EventInfoView(),
                                const SizedBox(height: 40),
                                if (!controller.isExternalEvent.value) ...[
                                  // 1 : not registered
                                  // 2 : registered not checked in
                                  // 3 : checked in
                                  // 4 : need to make payment -> it mean registered but no payment made
                                  // 5 : in waiting list
                                  // 6 : registration is closed or full
                                  // 7 : registered as passenger
                                  // 8 : registered as passenger but no payment made

                                  // show update my booking button
                                  // if registered
                                  (controller.bookingStatus.value == 2 ||
                                          controller.bookingStatus.value == 3)
                                      ? updateMyBookingButton()
                                      : Container(),

                                  // show addons button
                                  // if registered OR addonsNotRequireBooking not empty
                                  (controller.addonsNotRequireBooking
                                              .isNotEmpty ||
                                          controller.bookingStatus.value == 2 ||
                                          controller.bookingStatus.value == 3)
                                      ? addOnPurchaseButton()
                                      : Container(),

                                  // show indenmity form
                                  // if registered
                                  (controller.bookingStatus.value == 2 ||
                                          controller.bookingStatus.value == 3 ||
                                          controller.bookingStatus.value == 7 ||
                                          controller.bookingStatus.value == 9)
                                      ? viewUnsignedFormButton()
                                      : const SizedBox(),

                                  (controller.bookingStatus.value == 4 ||
                                          controller.bookingStatus.value == 8 ||
                                          controller.bookingStatus.value == 10)
                                      ? payNowButton()
                                      : Container(),

                                  (controller.bookingStatus.value == 2)
                                      ? checkInNowButton()
                                      : Container(),

                                  (controller.bookingStatus.value == 3)
                                      ? alreadyCheckedInButton()
                                      : Container(),

                                  (controller.bookingStatus.value == 6)
                                      ? joinWaitingListButton()
                                      : Container(),

                                  (controller.bookingStatus.value == 5)
                                      ? joinedWaitingListButton()
                                      : Container(),

                                  (controller.bookingStatus.value == 1)
                                      ? bookNowButton()
                                      : Container(),

                                  ((controller.bookingStatus.value == 2 ||
                                              controller.bookingStatus.value ==
                                                  3) &&
                                          controller.isRebateAvailable.value)
                                      ? claimRebateButton()
                                      : Container(),

                                  const SizedBox(height: 20),
                                ]
                              ],
                            ),
                          ),
                        ]),
                      ]),
                      //getBottomButton()
                    ]),

                    // popup menu
                    if (controller.showPopupRegister.value)
                      CustomPopup(
                        onTap: () {
                          debugPrint("onTap");
                          controller.showPopupRegister.value = false;
                        },
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SizedBox(height: 20),
                            Text('Choose Registration Type',
                                style: AppTextStyles.normalText
                                    .copyWith(fontSize: 18)),
                            const SizedBox(height: 30),
                            driverRegistrationButton(),
                            if (controller.event.value.additionalDriver!)
                              additionalDriverRegistrationButton(),
                            if (controller.event.value.passenger!)
                              passengerRegistrationButton(),
                            if (controller.event.value.group!)
                              groupRegistrationButton()
                          ],
                        ),
                      ),
                  ],
                ),
        ));
  }

  Widget updateMyBookingButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            Get.toNamed(Routes.UPDATE_MY_BOOKING,
                arguments: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Update My Registration",
                  style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget addOnPurchaseButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            Get.toNamed(Routes.ADDON, arguments: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Addons", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget bookNowButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            // only show popup if registration is not only driver

            if (controller.event.value.additionalDriver! ||
                controller.event.value.passenger! ||
                controller.event.value.group!) {
              controller.showPopupRegister.value = true;
            } else {
              Get.toNamed(Routes.BOOKING, arguments: controller.event.value);
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Register", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget joinWaitingListButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            Get.toNamed(Routes.JOIN_WAITING_LIST,
                arguments: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Join Waiting List", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget joinedWaitingListButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor: WidgetStateProperty.all<Color>(Colors.black12),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(color: Colors.black12)))),
          onPressed: () {
            //Get.toNamed(Routes.JOIN_WAITING_LIST, arguments: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("You have joined the waiting list",
                  style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget checkInNowButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            Get.toNamed(Routes.CHECK_IN, arguments: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Check In Now", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget alreadyCheckedInButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {},
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("You already checked in",
                  style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget viewSignedFormButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            Get.toNamed(Routes.EVENTS_FORM_SIGNED, arguments: {
              "event": controller.event.value,
              "type": controller.registrationType.value.toLowerCase()
            });
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Indemnity Form", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget viewUnsignedFormButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            Get.toNamed(Routes.EVENTS_FORM_SIGN, arguments: {
              "event": controller.event.value,
              "type": controller.registrationType.value.toLowerCase()
            });
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Indemnity Form", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  Widget payNowButton() {
    return Column(
      children: [
        // make payment button
        Padding(
          padding: const EdgeInsets.only(bottom: 10.0),
          child: ElevatedButton(
              style: ButtonStyle(
                  foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                  backgroundColor: WidgetStateProperty.all<Color>(
                      AppColor.primaryButtonColor),
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(18.0),
                          side: const BorderSide(
                              color: AppColor.primaryButtonColor)))),
              onPressed: () {
                if (userController.isLoggedIn.value) {
                  // passenger
                  if (controller.bookingStatus.value == 8) {
                    Get.toNamed(Routes.MAKE_PAYMENT, arguments: {
                      "event": controller.event.value,
                      "type": "passenger"
                    });
                  }

                  // additional driver
                  else if (controller.bookingStatus.value == 10) {
                    Get.toNamed(Routes.MAKE_PAYMENT, arguments: {
                      "event": controller.event.value,
                      "type": "additional driver",
                      "mainDriver": controller.selectedMainDriver.value
                    });
                  }

                  // driver
                  GetStorage().write('fromEventDetailMakePaymentButton', true);
                  Get.toNamed(Routes.MAKE_PAYMENT,
                      arguments: controller.event.value);
                }
              },
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20),
                height: 50,
                child: const Center(
                  child:
                      Text("Make Payment Now", style: TextStyle(fontSize: 16)),
                ),
              )),
        ),

        // cancel button
        cancelRegistrationButton(),
      ],
    );
  }

  Widget cancelRegistrationButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.redButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(color: AppColor.redButtonColor)))),
          onPressed: () async {
            if (userController.isLoggedIn.value) {
              showDialog(
                  context: Get.context!,
                  builder: (context) => KConfirmDialogView(
                      content: "Are you sure want to cancel this registration?",
                      callbackYes: () async {
                        Get.back();

                        // passenger
                        if (controller.bookingStatus.value == 7) {
                          await controller
                              .cancelNoPaymentPassengerRegistration();
                        }

                        // additional driver
                        else if (controller.bookingStatus.value == 10) {
                          await controller
                              .cancelNoPaymentAdditionalDriverRegistration();
                        }
                        // driver
                        else {
                          await controller.cancelNoPaymentDriverRegistration();
                        }

                        controller.fetchCheckBookingStatus();
                        SnackHelper.showInfo("Registration Cancelled");
                      }));
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child:
                  Text("Cancel Registration", style: TextStyle(fontSize: 16)),
            ),
          )),
    );
  }

  // registration menu buttons

  Widget driverRegistrationButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            controller.showPopupRegister.value = false;

            if (userController.isLoggedIn.value) {
              Get.toNamed(Routes.BOOKING, arguments: controller.event.value);
            } else {
              navHistoryController.backPage = Routes.BOOKING;
              navHistoryController.backPageArgs = controller.event.value;
              Get.toNamed(Routes.GUEST);
            }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Driver", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget additionalDriverRegistrationButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () async {
            controller.showPopupRegister.value = false;
            await controller.fetchMainDrivers();
            controller.filteredDrivers.assignAll(controller.mainDrivers);

            debugPrint('show UserLookupView');

            final selectedUser = await Get.dialog<User>(UserLookupView());
            if (selectedUser != null) {
              debugPrint('Selected user: ${selectedUser.name}');
              // Do something with the selected user
            }

            // if (await controller.registerAsAdditionalDriver()) {
            //   Get.toNamed(Routes.MAKE_PAYMENT, arguments: {"event": controller.event.value, "type": "additional driver"});
            // }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 0, right: 0),
            height: 50,
            child: const Center(
              child: Text("Additional Driver", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget passengerRegistrationButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () async {
            controller.showPopupRegister.value = false;

            Get.toNamed(Routes.BOOKING, arguments: {
              "event": controller.event.value,
              "type": "passenger"
            });

            // if (await controller.registerAsPassenger()) {
            //   controller.fetchCheckBookingStatus();
            //   Get.toNamed(Routes.EVENTS_FORM, arguments: {"event": controller.event.value, "type": "passenger"});
            // }
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Passenger", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget groupRegistrationButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 40, right: 40),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            controller.showPopupRegister.value = false;
            Get.toNamed(Routes.GROUP, arguments: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Group", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }

  Widget claimRebateButton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0, left: 0, right: 0),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
              backgroundColor:
                  WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
              shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(18.0),
                      side: const BorderSide(
                          color: AppColor.primaryButtonColor)))),
          onPressed: () {
            Get.toNamed(Routes.CLAIM_REBATE, arguments: controller.event.value);
          },
          child: Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            height: 50,
            child: const Center(
              child: Text("Claim Rebate", style: TextStyle(fontSize: 15)),
            ),
          )),
    );
  }
}
