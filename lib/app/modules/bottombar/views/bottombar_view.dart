import 'package:automoment/app/constants/app_color.dart';
import 'package:automoment/app/modules/shared/kdrawer/views/kdrawer_view.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../shared/support_button.dart';
import '../controllers/bottombar_controller.dart';

class BottomBarView extends StatelessWidget {
  BottomBarView({super.key}) {
    // Initialize controllers using Get.find() as per project pattern
    controller = Get.find<BottombarController>();
    userController = Get.find<UserController>();
  }

  // Using late final for controllers as per project pattern
  late final BottombarController controller;
  late final UserController userController;
  // Make iconSize final to fix immutability warning
  final double iconSize = 30;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      drawer: SizedBox(width: Get.width, child: <PERSON><PERSON>raw<PERSON><PERSON>ie<PERSON>()),
      body: Obx(() => controller.getCurrentPageWidget()),
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
              bottomLeft: Radius.circular(0),
              bottomRight: Radius.circular(0)),
          boxShadow: [
            BoxShadow(
                offset: Offset(0.0, 1.00), //(x,y)
                blurRadius: 4.00,
                color: Colors.black12,
                spreadRadius: 2.00),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
          child: Obx(() => BottomNavigationBar(
              elevation: 10,
              backgroundColor: Colors.white,
              type: BottomNavigationBarType.fixed,
              onTap: (currentIndex) => controller.changePage(currentIndex),
              selectedLabelStyle: const TextStyle(
                  color: AppColor.secondaryColor, fontWeight: FontWeight.bold),
              unselectedLabelStyle: const TextStyle(
                  color: AppColor.primaryColor, fontWeight: FontWeight.bold),
              unselectedItemColor: AppColor.primaryColor,
              selectedItemColor: AppColor.secondaryColor,
              currentIndex: controller.index.value,
              showSelectedLabels: false,
              showUnselectedLabels: false,
              iconSize: iconSize,
              items: getItemsBottomBarLoggedIn())),
        ),
      ),
      floatingActionButton: const SupportButton(),
    );
  }

  List<BottomNavigationBarItem> getItemsBottomBarLoggedIn() {
    double verticalPadding = 0;
    double horizontalPadding = 5;
    return [
      BottomNavigationBarItem(
          icon: Padding(
            padding: EdgeInsets.symmetric(vertical: verticalPadding, horizontal: horizontalPadding),
            child: Image.asset(
                getCurrentPage() == 0
                    ? 'assets/images/ic_home_on.png'
                    : 'assets/images/ic_home_off.png',
                height: iconSize),
          ),
          label: 'Home'),
      BottomNavigationBarItem(
          icon: Padding(
            padding: EdgeInsets.symmetric(vertical: verticalPadding, horizontal: horizontalPadding),
            child: Image.asset(
                getCurrentPage() == 1
                    ? 'assets/images/ic_news_on.png'
                    : 'assets/images/ic_news_off.png',
                height: iconSize),
          ),
          label: 'News'),
      BottomNavigationBarItem(
          icon: Padding(
            padding: EdgeInsets.symmetric(vertical: verticalPadding, horizontal: horizontalPadding),
            child: Image.asset(
                getCurrentPage() == 2
                    ? 'assets/images/ic_event_on.png'
                    : 'assets/images/ic_event_off.png',
                height: iconSize),
          ),
          label: 'Events'),
      BottomNavigationBarItem(
          icon: Padding(
            padding: EdgeInsets.symmetric(vertical: verticalPadding, horizontal: horizontalPadding),
            child: Image.asset(
                getCurrentPage() == 3
                    ? 'assets/images/ic_results_on.png'
                    : 'assets/images/ic_results_off.png',
                height: iconSize),
          ),
          label: 'Leaderboard'),
      BottomNavigationBarItem(
          icon: Padding(
            padding: EdgeInsets.symmetric(vertical: verticalPadding, horizontal: horizontalPadding),
            child: Image.asset(
                getCurrentPage() == 4
                    ? 'assets/images/ic_reward_on.png'
                    : 'assets/images/ic_reward_off.png',
                height: iconSize),
          ),
          label: 'Reward'),
      BottomNavigationBarItem(
          icon: Padding(
            padding: EdgeInsets.symmetric(vertical: verticalPadding, horizontal: horizontalPadding),
            child: Image.asset(
                getCurrentPage() == 5
                    ? 'assets/images/ic_store_on.png'
                    : 'assets/images/ic_store_off.png',
                height: iconSize),
          ),
          label: 'Store'),
      BottomNavigationBarItem(
          icon: Padding(
            padding: EdgeInsets.symmetric(vertical: verticalPadding, horizontal: horizontalPadding),
            child: Image.asset(
                getCurrentPage() == 6
                    ? 'assets/images/ic_profile_on.png'
                    : 'assets/images/ic_profile_off.png',
                height: iconSize),
          ),
          label: 'Profile'),
    ];
  }

  int getCurrentPage() {
    return controller.index.value;
  }
}
