import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../globals.dart';

class ChatListController extends BaseScreenController {
  @override
  String get screenName => 'Chat List';

  final UserController userController = Get.put(UserController());
  final chatGroups = [].obs;
  var isLoading = false.obs;
  var userChatGroupRef;
  final isEmptyList = true.obs;
  RxList snapshot = RxList();

  @override
  Future<void> onInit() async {
    super.onInit();

    userChatGroupRef = firebaseDatabase
        .ref("userChatGroups/${userController.getUser().id}")
        .orderByChild("is_open")
        .equalTo(1);
    await getAndListenToChatList();
  }

  @override
  Future<void> onReady() async {
    super.onReady();

    final userChatGroupRefe = firebaseDatabase
        .ref("userChatGroups/${userController.getUser().id}")
        .orderByChild("is_open")
        .equalTo(1);

    var snapshot = await userChatGroupRefe.get();
    if (snapshot.exists) {
      isEmptyList.value = false;
      debugPrint("userChatGroupRef get: ${snapshot.value}");
    } else {
      isEmptyList.value = true;
      debugPrint('No data available.');
    }

    userChatGroupRefe.onChildAdded.listen((event) async {
      // A new comment has been added, so add it to the displayed list.
      debugPrint("onChildAdded event.snapshot.value: ${event.snapshot.value}");
      snapshot = await userChatGroupRefe.get();
      isEmptyList.value = (snapshot.exists) ? false : true;
    });
    userChatGroupRefe.onChildChanged.listen((event) async {
      // A comment has changed; use the key to determine if we are displaying this
      // comment and if so displayed the changed comment.
      debugPrint(
          "onChildChanged event.snapshot.value: ${event.snapshot.value}");
      snapshot = await userChatGroupRefe.get();
      isEmptyList.value = (snapshot.exists) ? false : true;
    });
    userChatGroupRefe.onChildRemoved.listen((event) async {
      // A comment has been removed; use the key to determine if we are displaying
      // this comment and if so remove it.
      debugPrint(
          "onChildRemoved event.snapshot.value: ${event.snapshot.value}");
      snapshot = await userChatGroupRefe.get();
      isEmptyList.value = (snapshot.exists) ? false : true;
    });
    userChatGroupRefe.onChildMoved.listen((event) async {
      // A comment has changed position; use the key to determine if we are
      // displaying this comment and if so move it.
      debugPrint("onChildMoved event.snapshot.value: ${event.snapshot.value}");
      snapshot = await userChatGroupRefe.get();
      isEmptyList.value = (snapshot.exists) ? false : true;
    });
  }

  Future<void> getAndListenToChatList() async {
    ///userChatGroups/94
    //var path = "userChatGroups/${userController.getUser().id}";
    //debugPrint("getAndListenToChatList path: $path");
    //DatabaseReference userChatGroupRef = FirebaseDatabase.instance.ref(path);
  }
}
