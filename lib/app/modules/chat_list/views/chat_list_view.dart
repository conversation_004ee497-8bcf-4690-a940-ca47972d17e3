
import 'package:automoment/app/constants/app_config.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_database/ui/firebase_animated_list.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../controllers/user_controller.dart';
import '../../../globals.dart';
import '../../../models/chat_list_model.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/chat_list_controller.dart';

class ChatListView extends StatelessWidget {
  ChatListView({super.key});

  final ChatListController controller = Get.put(ChatListController());
  final UserController userController = Get.put(UserController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        title: const Text(
          "Chat List",
          style: TextStyle(
              color: AppColor.primaryColor,
              fontWeight: FontWeight.bold,
              fontSize: 16
          ),
        ),
        elevation: 1,
        // leading: IconButton(
        //   padding: const EdgeInsets.only(left: 10),
        //   onPressed: () {
        //     Scaffold.of(context).openDrawer();
        //   },
        //   icon: const Icon(Icons.menu),
        //   iconSize: 30,
        //   color: Colors.black,
        // ),
        actions: [
          IconButton(
            padding: const EdgeInsets.only(right: 10),
            onPressed: () {
              Get.toNamed(Routes.NOTIFICATION);
            },
            icon: Obx(() {
              return Stack(
                children: [
                  const Icon(
                      Icons.notifications,
                      color: Colors.black,
                      size: 30
                  ),
                  (controller.userController.unreadNotificationCount.value > 0)
                      ? Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(1),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.all(Radius.circular(6)),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 12,
                        minHeight: 12,
                      ),
                      child: Text(
                        (controller.userController.unreadNotificationCount
                            .value > 99) ? "99+" :
                        controller.userController.unreadNotificationCount.value
                            .toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
                      : const SizedBox()
                ],
              );
            }),
          ),
        ],
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return (controller.isEmptyList.value) ? const Center(
          child: Text(
            "You need to join an event\nto chat with other participants.",
            style: TextStyle(
                color: Colors.grey,
                fontWeight: FontWeight.normal,
                fontSize: 16
            ),
            textAlign: TextAlign.center,
          )
        ) : FirebaseAnimatedList(
              query: //controller.userChatGroupRef,
              firebaseDatabase.ref("userChatGroups/${userController.getUser().id}").orderByChild("is_open").equalTo(1),
              itemBuilder: (BuildContext context, DataSnapshot snapshot,
                  Animation<double> animation, int index) {
                var json = snapshot.value as Map<dynamic, dynamic>;
                ChatList v = ChatList.fromJson(json);
                return InkWell(
                  onTap: () {
                    Get.toNamed(Routes.CHAT, arguments: [v.id, v.name, v.image]);
                  },

                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundImage: NetworkImage(
                          "${AppConfig.storageUrl}/${v.image}"),
                    ),
                    title: Text(
                      v.name ?? "",
                      style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16
                      ),
                    ),
                  ),
                );
              },
        );
      }),
    );
  }
}
