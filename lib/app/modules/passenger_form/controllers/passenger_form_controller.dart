import 'dart:convert';

import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../../services/api_client.dart';

class PassengerFormController extends BaseScreenController {
  @override
  String get screenName => 'Indemnity Form';

  var event = Event().obs;
  var isLoading = true.obs;
  String msg = "";
  var jsonForm = "";
  var isFormFilled = false.obs;
  String signatureBase64 = "";

  Map<String, dynamic> formJson = {};

  final UserController userController = Get.put(UserController());

  @override
  void onInit() {
    super.onInit();

    var data = Get.arguments;
    event.value = data;

    fetchFormJson();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<bool> fetchFormJson() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.getFormAsPassenger(
          userController.getToken(), event.value.id!);
      formJson = jsonDecode(response)['form'];
      isLoading.value = false;
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<bool> submitForm() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      var response = await ApiClient.signFormAsPassenger(
          userController.getToken(),
          event.value.id!,
          jsonForm,
          signatureBase64);
      msg = jsonDecode(response)['message'];
      isLoading.value = false;
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }
}
