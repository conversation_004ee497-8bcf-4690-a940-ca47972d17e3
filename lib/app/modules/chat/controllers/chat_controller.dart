import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:automoment/app/constants/app_config.dart';
import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/helpers/string_util.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:image_picker/image_picker.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

import '../../../controllers/user_controller.dart';
import '../../../globals.dart';
import '../../../models/chat_model.dart';

class ChatController extends BaseScreenController {
  @override
  String get screenName => 'Chat';

  final UserController userController = Get.find();

  var chatId = Get.arguments[0];
  var chatTitle = Get.arguments[1];
  var chatImage = Get.arguments[2];
  RxList<types.Message> messages = RxList();
  late types.User user;

  @override
  Future<void> onInit() async {
    super.onInit();

    user = types.User(
        id: userController.getUser().id.toString(),
        firstName: userController.getUser().name,
        imageUrl:
            "https://avataaars.io/?avatarStyle=Circle&topType=ShortHairTheCaesar&accessoriesType=Prescription02&hairColor=Black&facialHairType=MoustacheMagnum&facialHairColor=Brown&clotheType=ShirtScoopNeck&clotheColor=Pink&eyeType=Side&eyebrowType=RaisedExcitedNatural&mouthType=Serious&skinColor=Light");

    getChatMessagesAndListen();

    debugPrint("Chat Id: $chatId");
  }

  handleSendPressed(types.PartialText message) {
    // send to firebase database
    var path = "chatMessages/$chatId";
    debugPrint("handleSendPressed path: $path");
    DatabaseReference chatMessagesRef = firebaseDatabase.ref(path);

    var chat = Chat(
      id: StringUtil.randomString(10),
      senderId: userController.getUser().id,
      senderName: userController.getUser().name,
      senderImageUrl: userController.getUser().photo,
      message: message.text,
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );

    chatMessagesRef.push().set(chat.toJson());
  }

  void getChatMessagesAndListen() {
    var path = "chatMessages/$chatId";
    debugPrint("getChatMessagesAndListen path: $path");
    DatabaseReference chatMessagesRef = firebaseDatabase.ref(path);

    chatMessagesRef.onChildAdded.listen((event) {
      debugPrint(
          "getChatMessagesAndListen onChildAdded: ${event.snapshot.value}");

      var json = event.snapshot.value as Map<dynamic, dynamic>;
      Chat v = Chat.fromJson(json);

      types.Message message;

      if (v.isImage ?? false) {
        message = types.ImageMessage(
          author: types.User(
              id: v.senderId.toString(),
              imageUrl: "${AppConfig.storageUrl}/${v.senderImageUrl}",
              firstName: v.senderName),
          createdAt: v.timestamp,
          height: v.imageHeight?.toDouble(),
          id: v.id.toString(),
          name: v.imageFilename ?? "${StringUtil.randomString(10)}.jpg",
          size: v.imageSize?.toInt() ?? 0,
          uri: v.imageUrl ?? "",
          width: v.imageWidth?.toDouble(),
        );
      } else if (v.isFile ?? false) {
        message = types.FileMessage(
          author: types.User(
              id: v.senderId.toString(),
              imageUrl: "${AppConfig.storageUrl}/${v.senderImageUrl}",
              firstName: v.senderName),
          createdAt: v.timestamp,
          id: v.id.toString(),
          name: v.fileFilename ?? "",
          size: v.fileSize?.toInt() ?? 0,
          uri: v.fileUrl ?? "",
        );
      } else {
        message = types.TextMessage(
            author: types.User(
                id: v.senderId.toString(),
                imageUrl: "${AppConfig.storageUrl}/${v.senderImageUrl}",
                firstName: v.senderName),
            createdAt: v.timestamp,
            id: v.id.toString(),
            text: v.message ?? "");
      }

      messages.insert(0, message);
    });
  }

  void handleImageSelection() async {
    final result = await ImagePicker().pickImage(
      imageQuality: 70,
      maxWidth: 1440,
      source: ImageSource.gallery,
    );

    if (result != null) {
      final bytes = await result.readAsBytes();
      final image = await decodeImageFromList(bytes);

      await uploadImageToFirebaseStorage(result, image, bytes);
    }
  }

  Future<void> handleCameraSelection() async {
    final result = await ImagePicker().pickImage(
      imageQuality: 70,
      maxWidth: 1440,
      source: ImageSource.camera,
    );

    if (result != null) {
      final bytes = await result.readAsBytes();
      final image = await decodeImageFromList(bytes);

      await uploadImageToFirebaseStorage(result, image, bytes);
    }
  }

  Future<void> handleFileSelection() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
    );

    if (result != null && result.files.single.path != null) {
      // upload

      final filename = result.files.single.path?.split('/').last;
      final storageRef = firebaseStorage.ref();
      var newFilename = "${StringUtil.randomString(10)}_$filename";
      final fileRef = storageRef.child("chat_files/$newFilename");
      var fileUrl = "";

      try {
        await fileRef.putFile(File(result.files.single.path!));
        fileUrl = await fileRef.getDownloadURL();
      } on FirebaseException catch (e) {
        debugPrint("Upload error: ${e.toString()}");
      }

      // send to firebase database
      var path = "chatMessages/$chatId";
      DatabaseReference chatMessagesRef = firebaseDatabase.ref(path);

      var chat = Chat(
        id: StringUtil.randomString(10),
        senderId: userController.getUser().id,
        senderName: userController.getUser().name,
        senderImageUrl: userController.getUser().photo,
        message: "",
        isFile: true,
        fileUrl: fileUrl,
        fileSize: result.files.single.size,
        fileFilename: newFilename,
        timestamp: DateTime.now().millisecondsSinceEpoch,
      );

      chatMessagesRef.push().set(chat.toJson());
    }
  }

  Future<void> uploadImageToFirebaseStorage(
      XFile result, ui.Image image, Uint8List bytes) async {
    final filename = result.path.split('/').last;
    final storageRef = firebaseStorage.ref();
    final imageRef =
        storageRef.child("chat_files/${StringUtil.randomString(10)}_$filename");
    var imageUrl = "";

    try {
      await imageRef.putFile(File(result.path));
      imageUrl = await imageRef.getDownloadURL();
    } on FirebaseException catch (e) {
      debugPrint("Upload error: ${e.toString()}");
    }

    // send to firebase database
    var path = "chatMessages/$chatId";
    DatabaseReference chatMessagesRef = firebaseDatabase.ref(path);

    var chat = Chat(
      id: StringUtil.randomString(10),
      senderId: userController.getUser().id,
      senderName: userController.getUser().name,
      senderImageUrl: userController.getUser().photo,
      message: "",
      isImage: true,
      imageUrl: imageUrl,
      imageHeight: image.height,
      imageWidth: image.width,
      imageSize: bytes.length,
      imageFilename: filename,
      timestamp: DateTime.now().millisecondsSinceEpoch,
    );

    chatMessagesRef.push().set(chat.toJson());
  }

  void handlePreviewDataFetched(
      types.TextMessage message, types.PreviewData previewData) {
    final index = messages.indexWhere((element) => element.id == message.id);
    final updatedMessage = (messages[index] as types.TextMessage).copyWith(
      previewData: previewData,
    );

    messages[index] = updatedMessage;
  }

  Future<void> handleMessageTap(
      BuildContext context, types.Message message) async {
    if (message is types.FileMessage) {
      var localPath = message.uri;

      if (message.uri.startsWith('http')) {
        try {
          final index =
              messages.indexWhere((element) => element.id == message.id);
          var updatedMessage =
              (messages[index] as types.FileMessage).copyWith(isLoading: true);
          messages[index] = updatedMessage;

          // download from firebase storage
          final storageRef = firebaseStorage.ref();
          final fileRef = storageRef.child("chat_files/${message.name}");

          final appDocDir = await getApplicationDocumentsDirectory();
          final filePath = "${appDocDir.path}/${message.name}";
          final file = File(filePath);

          final downloadTask = fileRef.writeToFile(file);
          downloadTask.snapshotEvents.listen((taskSnapshot) async {
            switch (taskSnapshot.state) {
              case TaskState.running:
                debugPrint(
                    "Download progress: ${taskSnapshot.bytesTransferred / taskSnapshot.totalBytes * 100}%");
                break;
              case TaskState.paused:
                debugPrint("Download paused");
                break;
              case TaskState.success:
                debugPrint("Download success");

                localPath = file.path;
                updatedMessage = (messages[index] as types.FileMessage)
                    .copyWith(isLoading: null, uri: localPath);
                messages[index] = updatedMessage;
                await OpenFilex.open(localPath);

                break;
              case TaskState.canceled:
                debugPrint("Download canceled");
                break;
              case TaskState.error:
                debugPrint("Download error");
                break;
            }
          });
        } finally {}
      } else {
        await OpenFilex.open(localPath);
      }
    }
  }
}
