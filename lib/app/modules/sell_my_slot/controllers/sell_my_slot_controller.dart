import 'dart:convert';

import 'package:automoment/app/controllers/user_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:intl/intl.dart';

import '../../../models/event_modal.dart';
import '../../../services/api_client.dart';

class SellMySlotController extends BaseScreenController {
  @override
  String get screenName => 'Sell My Slot';

  UserController userController = Get.put(UserController());

  var event = Event().obs;
  String msg = "";

  var emailController = TextEditingController();

  var isLoading = false.obs;
  var isUserExist = false.obs;
  var buyerName = "".obs;
  var buyerEmail = "".obs;

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();

    event.value = Get.arguments;
  }

  String showEventDate() {
    return DateFormat("dd MMMM yyyy").format(event.value.eventDate!);
  }

  Future<bool> checkUserExist() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.checkUserExists(
          userController.getToken(), emailController.text);
      EasyLoading.dismiss();
      var success = (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
      msg = jsonDecode(response)['message'];

      isUserExist.value = success;

      if (success) {
        buyerName.value = jsonDecode(response)['user']['name'];
        buyerEmail.value = jsonDecode(response)['user']['email'];
      }

      return success;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> sellMySlot() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.sellMySlot(
          userController.getToken(), event.value.id!, emailController.text);
      EasyLoading.dismiss();
      var success = (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
      msg = jsonDecode(response)['message'];

      return success;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
