import 'package:automoment/app/modules/shared/kconfirm_dialog/views/kconfirm_dialog_view.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/sell_my_slot_controller.dart';

class SellMySlotView extends StatelessWidget {
  final SellMySlotController controller;

  SellMySlotView({super.key}) : controller = Get.find<SellMySlotController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Sell My Slot', style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: SingleChildScrollView(
            child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                controller.event.value.title!,
                style: AppTextStyles.titleText,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'Event Date: ${controller.showEventDate()}',
                style: AppTextStyles.normalText,
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'To whom are you transferring your slot? Please make sure they are a registered user of Automoment app.',
                style: AppTextStyles.normalText,
              ),
            ),
            emailLookUpWidget(context),
          ]),
        )));
  }

  emailLookUpWidget(BuildContext context) {
    // show an input box to enter email and check button on the right side to lookup the user details

    return Obx(() {
      return Container(
          margin: const EdgeInsets.symmetric(vertical: 20),
          width: double.infinity,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Form(
              key: controller.formKey,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: Get.width - 150,
                    child: TextFormField(
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                      style: AppTextStyles.normalText.copyWith(
                        fontSize: 14,
                      ),
                      controller: controller.emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: const InputDecoration(
                        //border: OutlineInputBorder(),
                        hintText: 'Enter email address',
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  ElevatedButton(
                      style: ButtonStyle(
                          foregroundColor:
                              WidgetStateProperty.all<Color>(Colors.white),
                          backgroundColor: WidgetStateProperty.all<Color>(
                              AppColor.primaryButtonColor),
                          shape:
                              WidgetStateProperty.all<RoundedRectangleBorder>(
                                  RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(18.0),
                                      side: const BorderSide(
                                          color:
                                              AppColor.primaryButtonColor)))),
                      onPressed: () async {
                        if (controller.formKey.currentState!.validate()) {
                          // check if this is his own email
                          if (controller.userController.user.value.email ==
                              controller.emailController.text) {
                            Get.dialog(KMessageDialogView(
                                content:
                                    "You cannot transfer your slot to yourself.",
                                callback: () {
                                  Get.back();
                                }));
                          } else {
                            if (await controller.checkUserExist()) {
                            } else {
                              Get.dialog(KMessageDialogView(
                                  content:
                                      "The email is not registered. Please check again.",
                                  callback: () {
                                    Get.back();
                                  }));
                            }
                          }
                        }
                      },
                      child: Text('Check',
                          style: AppTextStyles.normalText
                              .copyWith(fontSize: 14, color: Colors.white)))
                ],
              ),
            ),

            // show user info
            controller.isUserExist.value
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                        const SizedBox(height: 30),
                        Text(
                            "Your slot will be transferred to the following user.",
                            style: AppTextStyles.normalText.copyWith(
                              fontSize: 14,
                            )),
                        const SizedBox(height: 20),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 70,
                                child: Text("Name",
                                    style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                              ),
                              SizedBox(
                                width: 20,
                                child: Text(" :",
                                    style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                              ),
                              Flexible(
                                child: Text(controller.buyerName.value,
                                    style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                              ),
                            ]),
                        const SizedBox(height: 10),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 70,
                                child: Text("Email",
                                    style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                              ),
                              SizedBox(
                                width: 20,
                                child: Text(" :",
                                    style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                              ),
                              Flexible(
                                child: Text(controller.buyerEmail.value,
                                    style: AppTextStyles.normalText.copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                              ),
                            ]),
                        const SizedBox(height: 30),
                        ElevatedButton(
                            onPressed: () async {
                              Get.dialog(KConfirmDialogView(
                                content: "Are you sure want to sell your slot?",
                                callbackNo: () {
                                  Get.back();
                                },
                                callbackYes: () async {
                                  if (await controller.sellMySlot()) {
                                    Get.dialog(KMessageDialogView(
                                      content: controller.msg,
                                      callback: () {
                                        //Get.offNamed(Routes.EVENTS, arguments: controller.event.value);
                                        Get.offAllNamed(Routes.BOTTOMBAR,
                                            arguments: 2);
                                      },
                                    ));
                                  } else {
                                    Get.dialog(KMessageDialogView(
                                      content: controller.msg,
                                      callback: () {
                                        Get.back();
                                        Get.back();
                                      },
                                    ));
                                  }
                                },
                              ));
                            },
                            style: ButtonStyle(
                                foregroundColor: WidgetStateProperty.all<Color>(
                                    Colors.white),
                                backgroundColor: WidgetStateProperty.all<Color>(
                                    AppColor.redButtonColor),
                                shape: WidgetStateProperty.all<
                                        RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(18.0),
                                        side: const BorderSide(
                                            color: AppColor.redButtonColor)))),
                            child: Container(
                              padding:
                                  const EdgeInsets.only(left: 20, right: 20),
                              height: 50,
                              child: const Center(
                                child: Text("Sell My Slot",
                                    style: TextStyle(fontSize: 16)),
                              ),
                            )),
                      ])
                : const SizedBox(),
          ]));
    });
  }
}
