
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_text_styles.dart';
import '../../../helpers/string_util.dart';
import '../../shared/support_button.dart';
import '../controllers/notification_detail_controller.dart';

class NotificationDetailView extends GetView<NotificationDetailController> {
  const NotificationDetailView({super.key});

  @override
  Widget build(BuildContext context) {

    controller.urls.value = StringUtil.findUrls(controller.notification.value.body ?? "");

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Notification', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return Column(
          children: [
            Container(
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              padding: const EdgeInsets.all(12.0),
              child: Row(
                children: [
                  Image.asset(
                    'assets/images/notif_info.png',
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          controller.notification.value.title ?? "",
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 5),
                        EasyRichText(
                          controller.notification.value.body ?? "",
                          defaultStyle: const TextStyle(
                            fontSize: 12,
                          ),
                          patternList: [
                            for (String url in controller.urls)
                              EasyRichTextPattern(
                                targetString: url, // need to detect link in text
                                urlType: 'web',
                                style: const TextStyle(
                                  fontSize: 12,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 5),
                        Text(
                          StringUtil.timeAgo(controller.notification.value.createdAt ?? DateTime.now()),
                          style: AppTextStyles.normalText.copyWith(color: Colors.grey[500]),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              height: 1,
              color: Colors.grey[300],
            )
          ],
        );
      }),
    );
  }
}
