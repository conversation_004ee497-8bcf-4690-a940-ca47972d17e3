import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/vehicles_add_controller.dart';

class VehiclesAddView extends StatefulWidget {
  const VehiclesAddView({super.key});

  @override
  State<VehiclesAddView> createState() => _VehiclesAddViewState();
}

class _VehiclesAddViewState extends State<VehiclesAddView> {
  final VehiclesAddController controller = Get.put(VehiclesAddController());

  @override
  void dispose() {
    Get.delete<VehiclesAddController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          title: const Text('Add Vehicle', style: AppTextStyles.titleText),
          elevation: 1,
        ),
        floatingActionButton: const SupportButton(),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
            child: Form(
              key: controller.formKey,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        showImagePicker(context);
                      },
                      child: Obx(() {
                        return Container(
                            clipBehavior: Clip.antiAlias,
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withAlpha(26 * 5),
                                  spreadRadius: 1,
                                  blurRadius: 7,
                                  offset: const Offset(
                                      0, 3), // changes position of shadow
                                ),
                              ],
                            ),
                            child: Center(
                              child: (controller.image64.value == "")
                                  ? Image.asset(
                                      "assets/images/image_placeholder.png",
                                      height: 300,
                                      fit: BoxFit.cover,
                                    )
                                  : Image.memory(
                                      base64Decode(controller.image64.value),
                                      fit: BoxFit.cover,
                                      height: 300,
                                    ),
                            ));
                      }),
                    ),
                    const SizedBox(height: AppConfig.defaultPadding),
                    TextFormField(
                      controller: controller.makeController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      decoration: const InputDecoration(
                        labelText: "Make",
                        hintText: "",
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    ),
                    TextFormField(
                      controller: controller.modelController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      decoration: const InputDecoration(
                        labelText: "Model",
                        hintText: "",
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    ),
                    TextFormField(
                      controller: controller.yearController,
                      keyboardType: TextInputType.number,
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.digitsOnly
                      ],
                      autocorrect: false,
                      decoration: const InputDecoration(
                        labelText: "Year",
                        hintText: "",
                      ),
                      validator: (value) {
                        return null;
                      },
                    ),
                    const SizedBox(height: 5),
                    Obx(() {
                      return DropdownButtonFormField(
                          hint: Text("Choose the vehicle type",
                              style: AppTextStyles.normalText.copyWith(
                                  fontSize: 16,
                                  color: AppColor.primaryButtonColor)),
                          value: (controller.selectedType.value == "")
                              ? null
                              : controller.selectedType.value,
                          items: controller.typeItems.map((String items) {
                            return DropdownMenuItem(
                              value: items,
                              child: Text(items),
                            );
                          }).toList(),
                          validator: (value) {
                            if (value == null) {
                              return 'Required';
                            }
                            return null;
                          },
                          onChanged: (String? value) {
                            debugPrint(value);
                            controller.selectedType.value = value!;
                          });
                    }),
                    const SizedBox(
                      height: 30,
                    ),
                    ElevatedButton(
                        style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.primaryButtonColor),
                            shape: WidgetStateProperty.all<
                                    RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(18.0),
                                    side: const BorderSide(
                                        color: AppColor.primaryButtonColor)))),
                        onPressed: () async {
                          if (controller.formKey.currentState!.validate()) {
                            if (await controller.addVehicle()) {
                              Get.dialog(
                                  KMessageDialogView(
                                      content: controller.msg,
                                      callback: () {
                                        Get.back();
                                        Get.back();
                                      }),
                                  barrierDismissible: false);
                            } else {
                              Get.dialog(
                                  KMessageDialogView(content: controller.msg),
                                  barrierDismissible: false);
                            }
                          } else {
                            debugPrint("not ok");
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          height: 50,
                          child: const Center(
                            child: Text("Add Vehicle",
                                style: TextStyle(fontSize: 16)),
                          ),
                        )),
                  ]),
            ),
          ),
        ));
  }

  void showImagePicker(context) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return Container(
              height: 140,
              decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30))),
              child: Column(children: [
                const SizedBox(
                  height: 20,
                ),
                const Text(
                  "Choose Vehicle Photo",
                  style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColor.primaryColor),
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        controller.getImageFromGallery();
                      },
                      child: const Text(
                        "From Gallery",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryColor),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        controller.getImageFromCamera();
                      },
                      child: const Text(
                        "Take a Photo",
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: AppColor.primaryColor),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ]));
        });
  }
}
