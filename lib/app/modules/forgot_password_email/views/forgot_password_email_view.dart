import 'package:email_validator/email_validator.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../shared/support_button.dart';
import '../controllers/forgot_password_email_controller.dart';

class ForgotPasswordEmailView extends StatefulWidget {
  const ForgotPasswordEmailView({super.key});

  @override
  State<ForgotPasswordEmailView> createState() =>
      _ForgotPasswordEmailViewState();
}

class _ForgotPasswordEmailViewState extends State<ForgotPasswordEmailView> {
  final ForgotPasswordEmailController controller =
      Get.put(ForgotPasswordEmailController());

  @override
  void dispose() {
    Get.delete<ForgotPasswordEmailController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Reset Password Request',
            style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Form(
          key: controller.formKey,
          child: ListView(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Text(
                  'Please enter your email address',
                  style: AppTextStyles.normalText,
                ),
              ),
              TextFormField(
                controller: controller.emailController,
                keyboardType: TextInputType.emailAddress,
                autocorrect: false,
                decoration: const InputDecoration(
                  labelText: "Email",
                  hintText: "",
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Required';
                  }

                  if (!EmailValidator.validate(value)) {
                    return "Invalid email pattern";
                  }
                  return null;
                },
              ),
              const SizedBox(
                height: 30,
              ),
              // black button
              ElevatedButton(
                  onPressed: () async {
                    if (controller.formKey.currentState!.validate()) {
                      if (await controller
                          .sendCode(controller.emailController.value.text)) {
                        Get.dialog(
                          KMessageDialogView(
                              content: controller.msg,
                              callback: () {
                                Get.back();
                                Get.toNamed(Routes.FORGOT_PASSWORD_CHECK_CODE);
                              }),
                          barrierDismissible: false,
                        );
                      } else {
                        Get.dialog(
                          KMessageDialogView(content: controller.msg),
                          barrierDismissible: false,
                        );
                      }
                    }
                  },
                  style: ButtonStyle(
                      foregroundColor:
                          WidgetStateProperty.all<Color>(Colors.white),
                      backgroundColor: WidgetStateProperty.all<Color>(
                          AppColor.primaryButtonColor),
                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18.0),
                              side: const BorderSide(
                                  color: AppColor.primaryButtonColor)))),
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    height: 50,
                    child: const Center(
                      child: Text("Next", style: TextStyle(fontSize: 16)),
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
