import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';

import '../../../services/api_client.dart';

class ForgotPasswordEmailController extends BaseScreenController {
  @override
  String get screenName => 'Reset Password: Entering email';

  TextEditingController emailController = TextEditingController();
  String msg = "";
  final formKey = GlobalKey<FormState>();

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
  }

  Future<bool> sendCode(String email) async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.resetPasswordEmail(email);
      msg = jsonDecode(response)['message'];
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
