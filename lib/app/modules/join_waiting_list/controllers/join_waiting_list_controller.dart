import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:intl/intl.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../../models/vehicle_model.dart';
import '../../../services/api_client.dart';

class JoinWaitingListController extends BaseScreenController {
  @override
  String get screenName => 'Join Waiting List';

  var event = Event().obs;
  String msg = "";
  var isLoading = false.obs;
  var isGetVehicleLoading = false.obs;

  var vehicles = <Vehicle>[].obs;
  var selectedVehicle = 0.obs;
  var selectedIndex = 0.obs;

  TextEditingController plateNumberController = TextEditingController();

  final UserController userController = Get.put(UserController());

  @override
  void onInit() {
    super.onInit();

    var data = Get.arguments;

    if (data.runtimeType == Event) {
      // check it because it is called from add vehicle too
      event.value = data;
    }

    getVehicles(showLoadingIndicator: true);
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<bool> bookEvent() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      String response;
      if (event.value.isRequireVehiclePlateNumber! == true) {
        response = await ApiClient.registerEvent(
            userController.getToken(),
            userController.user.value.id!,
            event.value.id!,
            selectedVehicle.value,
            plateNumberController.text,
            null,
            null);
      } else {
        response = await ApiClient.registerEvent(
            userController.getToken(),
            userController.user.value.id!,
            event.value.id!,
            selectedVehicle.value,
            null,
            null,
            null);
      }

      msg = jsonDecode(response)['message'];
      isLoading.value = false;
      EasyLoading.dismiss();
      return jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  String showEventDate() {
    return DateFormat("dd MMMM yyyy").format(event.value.eventDate!);
  }

  Future<void> getVehicles({bool showLoadingIndicator = false}) async {
    try {
      if (showLoadingIndicator) {
        EasyLoading.show(status: 'Loading...');
      }

      isGetVehicleLoading.value = true;

      var response = await ApiClient.getVehicles(
          userController.getToken(), userController.user.value.id!);
      var list = jsonDecode(response)['data'];
      var l = <Vehicle>[];

      list.forEach((dynamic d) {
        var n = Vehicle.fromJson(d);
        l.add(n);
      });

      vehicles.value = l;
      EasyLoading.dismiss();
      isGetVehicleLoading.value = false;
    } catch (e) {
      EasyLoading.dismiss();
      isGetVehicleLoading.value = false;
      throw Exception(e.toString());
    }
  }

  joinWaitingList() {}
}
