import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/booking_success_controller.dart';

class BookingSuccessView extends StatelessWidget {
  BookingSuccessView({super.key});

  final BookingSuccessController controller = Get.put(BookingSuccessController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Booking Successful', style: AppTextStyles.titleText),
        elevation: 1,
        automaticallyImplyLeading: false,
      ),
      floatingActionButton: const SupportButton(),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: ListView(
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 20),
              child: Text(
                'You have successfully booked this event.',
                style: AppTextStyles.normalText,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                controller.event.value.title!,
                style: AppTextStyles.normalTextBold,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Text(
                'Event Date: ${controller.showEventDate()}',
                style: AppTextStyles.normalText,
              ),
            ),
            const SizedBox(height: 20,),
            const Text(
              'Your Vehicle',
              style: AppTextStyles.normalTextBold,
            ),
            const SizedBox(height: 20,),
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: AppColor.primaryColor),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                children: [
                  // vehicle image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: (controller.vehicle.image == "") ? Image.asset(
                      'assets/images/image_placeholder.png',
                      width: 150,
                      height: 60,
                      fit: BoxFit.fitWidth,
                    ) : Image.network(
                      "${AppConfig.storageUrl}${controller.vehicle.image}",
                      width: 100,
                      height: 60,
                      fit: BoxFit.fitWidth,
                      errorBuilder: (context, error, stackTrace) => Image.asset(
                        'assets/images/image_placeholder.png',
                        width: 150,
                        height: 60,
                        fit: BoxFit.fitWidth,
                      )
                    ),
                  ),

                  const SizedBox(width: 10),
                  // vehicle name
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${controller.vehicle.make!} ${controller.vehicle.model!} ${controller.vehicle.year ?? ""}",
                          style: AppTextStyles.normalTextBold,
                        ),
                        const SizedBox(height: 5),
                        Text(
                          controller.vehicle.type!,
                          style: AppTextStyles.normalText,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20,),
            // black button
            ElevatedButton(
                onPressed: () {
                  controller.updateBookingStatus();
                  Get.close(2);
                  Get.toNamed(Routes.MAKE_PAYMENT, arguments: controller.event.value);
                },
                style: ButtonStyle(
                    foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
                    backgroundColor: WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                        RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18.0),
                            side: const BorderSide(color: AppColor.primaryButtonColor)
                        )
                    )
                ),
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  height: 50,
                  child: const Center(
                    child: Text(
                        "Next",
                        style: TextStyle(fontSize: 16)
                    ),
                  ),
                )
            ),
          ],
        ),
      ),

    );
  }
}
