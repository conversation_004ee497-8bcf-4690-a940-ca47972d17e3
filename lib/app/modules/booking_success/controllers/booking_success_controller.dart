import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:intl/intl.dart';

import '../../../models/event_modal.dart';
import '../../../models/vehicle_model.dart';
import '../../events_detail/controllers/events_detail_controller.dart';

class BookingSuccessController extends BaseScreenController {
  @override
  String get screenName => "Booking Success";

  var event = Event().obs;
  var vehicle = Vehicle();

  final EventsDetailController eventDetailController =
      Get.put(EventsDetailController());

  @override
  void onInit() {
    super.onInit();

    var data = Get.arguments;
    event.value = data[0];
    vehicle = data[1];

    eventDetailController.fetchCheckBookingStatus();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  String showEventDate() {
    return DateFormat("dd MMMM yyyy").format(event.value.eventDate!);
  }

  updateBookingStatus() {
    eventDetailController.fetchCheckBookingStatus();
  }
}
