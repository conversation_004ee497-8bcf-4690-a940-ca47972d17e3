import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../helpers/debug_helper.dart';
import '../../../services/api_client.dart';

class ShippingAddressController extends BaseScreenController {
  @override
  String get screenName => 'Shipping Address';

  final userController = Get.find<UserController>();
  final addresses = <Map<String, dynamic>>[].obs;
  final isLoading = false.obs;
  final selectedAddressIndex = RxInt(-1);

  @override
  void onInit() {
    super.onInit();
    fetchAddresses();
  }

  Future<void> fetchAddresses() async {
    try {
      isLoading.value = true;
      final response =
          await ApiClient.getShippingAddresses(userController.getToken());

      if (response['success'] == true && response['data'] != null) {
        addresses.clear();
        final List<dynamic> addressList = response['data'];
        addresses.addAll(addressList.cast<Map<String, dynamic>>());

        // Find default address
        final defaultIndex =
            addresses.indexWhere((addr) => addr['is_default'] == true);
        if (defaultIndex != -1) {
          selectedAddressIndex.value = defaultIndex;
        } else if (addresses.isNotEmpty) {
          selectedAddressIndex.value = 0;
        }
      }
    } catch (e) {
      DebugHelper.d('Error fetching addresses: $e');
      SnackHelper.showError('Failed to load addresses: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  void addNewAddress() {
    Get.toNamed('/shipping-address-add');
  }

  Future<void> selectAddress(int index) async {
    selectedAddressIndex.value = index;
  }

  Future<void> editAddress(int index) async {
    final address = addresses[index];
    Get.toNamed(
      '/shipping-address-edit',
      arguments: address,
    );
  }

  Future<void> deleteAddress(int index) async {
    try {
      final address = addresses[index];
      final addressId = address['id'];

      final response = await ApiClient.deleteShippingAddress(
        userController.getToken(),
        addressId,
      );

      if (response['success'] == true) {
        addresses.removeAt(index);
        if (selectedAddressIndex.value >= addresses.length) {
          selectedAddressIndex.value = addresses.length - 1;
        }
        SnackHelper.showSuccess('Address deleted successfully');
      }
    } catch (e) {
      DebugHelper.d('Error deleting address: $e');
      SnackHelper.showError('Failed to delete address: ${e.toString()}');
    }
  }

  Future<void> setDefaultAddress(int index) async {
    try {
      final address = addresses[index];
      final addressId = address['id'];

      final response = await ApiClient.setDefaultShippingAddress(
        userController.getToken(),
        addressId,
      );

      if (response['success'] == true) {
        // Create a new list with updated default status
        final List<Map<String, dynamic>> updatedAddresses =
            addresses.map((addr) {
          return {...addr, 'is_default': addr['id'] == addressId};
        }).toList();

        // Update the observable list
        addresses.assignAll(updatedAddresses);
      } else {
        SnackHelper.showError('Failed to update default address');
      }
    } catch (e) {
      DebugHelper.d('Error setting default address: $e');
      SnackHelper.showError(
          'Failed to update default address: ${e.toString()}');
    }
  }

  void confirmSelection() {
    if (addresses.isEmpty) return;

    final selectedAddress = addresses[selectedAddressIndex.value];
    Get.back(result: selectedAddress);
  }
}
