import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/modules/events_detail/controllers/events_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/event_modal.dart';
import '../../../models/pit_model.dart';
import '../../../models/vehicle_model.dart';
import '../../../services/api_client.dart';
import '../../shared/pit_item/pit_item.dart';

class UpdateMyBookingController extends BaseScreenController {
  @override
  String get screenName => 'Update My Booking';

  var event = Event().obs;
  var msg = "".obs;
  var isLoading = false.obs;

  var vehicles = <Vehicle>[].obs;
  var selectedVehicle = 0.obs;
  var selectedIndex = 0.obs;

  TextEditingController plateNumberController = TextEditingController();

  final UserController userController = Get.put(UserController());

  var isVehicleChecked = false.obs;

  var pits = <Pit>[].obs;
  PitOpController pitOpController = Get.put(PitOpController());

  var canChangePit = false.obs;
  var cancelButtonTitle = "Request to Cancel".obs;
  var cancelButtonEnabled = true.obs;
  var cancelButtonHidden = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();

    var data = Get.arguments;

    if (data.runtimeType == Event) {
      // check it because it is called from add vehicle too
      event.value = data;
    }

    await checkCancelMyBookingStatus();

    //getVehicles(showLoadingIndicator: true);
  }

  String showEventDate() {
    return DateFormat("dd MMMM yyyy").format(event.value.eventDate!);
  }

  Future<bool> updateMyBooking() async {
    debugPrint("updateMyBooking");
    isLoading.value = true;
    EasyLoading.show(status: 'Please wait...');
    try {
      String response;

      response = await ApiClient.updateMyBooking(
          userController.getToken(),
          userController.user.value.id!,
          event.value.id!,
          selectedVehicle.value,
          event.value.isRequireVehiclePlateNumber!
              ? plateNumberController.text
              : null,
          pitOpController.selectedPitNumber.value != pitNotSelected
              ? pitOpController.selectedPitNumber.value
              : null,
          pitOpController.selectedPitPosition.value != pitNotSelected
              ? pitOpController.selectedPitPosition.value
              : null);

      msg.value = jsonDecode(response)['message'];
      isLoading.value = false;

      EventsDetailController eventsDetailController =
          Get.put(EventsDetailController());
      eventsDetailController.checkStatus();

      canChangePit.value = false;

      EasyLoading.dismiss();
      return jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      isLoading.value = false;
      throw Exception(e.toString());
    }
  }

  Future<void> getVehicles({bool showLoadingIndicator = false}) async {
    try {
      if (showLoadingIndicator) {
        EasyLoading.show(status: 'Loading...');
      }

      var response = await ApiClient.getVehicles(
          userController.getToken(), userController.user.value.id!);
      var list = jsonDecode(response)['data'];
      var l = <Vehicle>[];

      list.forEach((dynamic d) {
        var n = Vehicle.fromJson(d);
        l.add(n);

        // selected vehicle
        if (n.events!.isNotEmpty) {
          for (int i = 0; i < n.events!.length; i++) {
            if (n.events![i] == event.value.id) {
              selectedVehicle.value = n.id!;
              selectedIndex.value = n.id!;
              break;
            }
          }
        }
      });

      vehicles.value = l;

      isVehicleChecked.value = true;

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> getUserVehiclesAndAvailablePits(
      {bool showLoadingIndicator = false}) async {
    try {
      if (showLoadingIndicator) {
        EasyLoading.show(status: 'Loading...');
      }

      var response = await ApiClient.getUserVehiclesAndAvailablePits(
          userController.getToken(), event.value.id!);

      // --
      var list = jsonDecode(response)['vehicles'];
      var l = <Vehicle>[];

      list.forEach((dynamic d) {
        var n = Vehicle.fromJson(d);
        l.add(n);

        // selected vehicle
        if (n.events!.isNotEmpty) {
          for (int i = 0; i < n.events!.length; i++) {
            if (n.events![i] == event.value.id) {
              selectedVehicle.value = n.id!;
              selectedIndex.value = n.id!;
              break;
            }
          }
        }
      });

      vehicles.value = l;

      // --
      var list2 = jsonDecode(response)['pits'];
      var l2 = <Pit>[];

      list2.forEach((dynamic d) {
        var n = Pit.fromJson(d);
        l2.add(n);
      });

      pits.value = l2;

      //

      isVehicleChecked.value = true;

      canChangePit.value = jsonDecode(response)['can_change_pit'];
      pitOpController.selectedPitNumber.value =
          int.parse(jsonDecode(response)['current_pit_number']);
      pitOpController.selectedPitPosition.value =
          jsonDecode(response)['current_pit_position'];

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<bool> cancelMyBooking() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.cancelMyBooking(
          userController.getToken(), event.value.id!);
      EasyLoading.dismiss();
      return jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> checkCancelMyBookingStatus() async {
    try {
      var response = await ApiClient.checkCancelMyBookingStatus(
          userController.getToken(), event.value.id!);

      var status = jsonDecode(response)['status_code'];

      if (status == 0) {
        cancelButtonTitle.value = "Request to Cancel";
        cancelButtonEnabled.value = true;
        cancelButtonHidden.value = false;
      } else if (status == 1) {
        cancelButtonTitle.value = "Processing Cancellation";
        cancelButtonEnabled.value = false;
        cancelButtonHidden.value = false;
      } else if (status == 2) {
        // approved
        cancelButtonHidden.value = true;
      } else if (status == 3) {
        // rejected, can cancel only once only
        cancelButtonHidden.value = true;
      }

      return jsonDecode(response)['status'];
    } catch (e) {
      throw Exception(e.toString());
    }
  }
}
