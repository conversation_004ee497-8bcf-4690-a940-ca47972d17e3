import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/news_model.dart';
import '../../../services/api_client.dart';

class NewsController extends BaseScreenController {
  @override
  String get screenName => 'News';

  var isLoading = false.obs;
  var newsList = <News>[].obs;
  var newsSliderList = <String>[].obs;

  final UserController userController = Get.put(UserController());

  @override
  void onInit() {
    super.onInit();
    fetchNews();
    fetchNewsSlider();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<void> fetchNews() async {
    isLoading.value = true;
    EasyLoading.show(status: 'Loading...');
    try {
      var response = await ApiClient.getNews();
      var list = jsonDecode(response)['data'];
      var l = <News>[];

      list.forEach((dynamic d) {
        var n = News.fromJson(d);
        l.add(n);
      });

      newsList.value = l;

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      isLoading.value = false;
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> fetchNewsSlider() async {
    try {
      var response = await ApiClient.getNewsSlider();
      var list = jsonDecode(response)['data'];
      var l = <String>[];

      list.forEach((dynamic d) {
        l.add(d);
      });

      newsSliderList.value = l;
    } catch (e) {
      throw Exception(e.toString());
    }
  }
}
