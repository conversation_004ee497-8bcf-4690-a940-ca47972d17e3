import 'package:automoment/app/constants/app_color.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/news_controller.dart';

class NewsView extends StatelessWidget {
  NewsView({super.key});

  final NewsController controller = Get.put(NewsController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        title: const Text(
          "News",
          style: TextStyle(
              color: AppColor.primaryColor,
              fontWeight: FontWeight.bold,
              fontSize: 16),
        ),
        elevation: 1,
        leading: IconButton(
          padding: const EdgeInsets.only(left: 10),
          onPressed: () {
            Scaffold.of(context).openDrawer();
          },
          icon: const Icon(Icons.menu),
          iconSize: 30,
          color: Colors.black,
        ),
        // add notification button on the right side with badge number
        actions: [
          IconButton(
            padding: const EdgeInsets.only(right: 10),
            onPressed: () {
              Get.toNamed(Routes.NOTIFICATION);
            },
            icon: Obx(() {
              return Stack(
                children: [
                  const Icon(Icons.notifications,
                      color: Colors.black, size: 30),
                  (controller.userController.unreadNotificationCount.value > 0)
                      ? Positioned(
                          top: 0,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.all(1),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6)),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 12,
                              minHeight: 12,
                            ),
                            child: Text(
                              (controller.userController.unreadNotificationCount
                                          .value >
                                      99)
                                  ? "99+"
                                  : controller.userController
                                      .unreadNotificationCount.value
                                      .toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )
                      : const SizedBox()
                ],
              );
            }),
          ),
        ],
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() => buildBody()),
    );
  }

  Widget buildBody() {
    return RefreshIndicator(
        onRefresh: () {
          return Future.delayed(const Duration(seconds: 1), () {
            controller.fetchNews();
          });
        },
        child: Column(
          children: [
            Expanded(child: getBodySection()),
          ],
        ));
  }

  getBodySection() {
    return SingleChildScrollView(
      physics: const ScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CarouselSlider(
            options: CarouselOptions(
              height: 330.0,
              aspectRatio: 16 / 9,
              autoPlay: true,
            ),
            items: controller.newsSliderList.map((img) {
              return Builder(
                builder: (BuildContext context) {
                  return Container(
                      width: MediaQuery.of(context).size.width,
                      margin: const EdgeInsets.symmetric(horizontal: 0.5),
                      decoration: const BoxDecoration(color: Colors.white),
                      child: Image.network("${AppConfig.storageUrl}$img",
                          width: Get.width, fit: BoxFit.cover, errorBuilder:
                              (BuildContext context, Object exception,
                                  StackTrace? stackTrace) {
                        return Image.asset(
                          'assets/images/image_placeholder.png',
                          width: Get.width,
                          fit: BoxFit.fitWidth,
                        );
                      }));
                },
              );
            }).toList(),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(
                horizontal: AppConfig.defaultPadding,
                vertical: AppConfig.defaultPadding),
            child: Text(
              'What’s Happening?',
              style: AppTextStyles.smallTitleBold,
            ),
          ),
          getListNews(),
        ],
      ),
    );
  }

  getListNews() {
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.vertical,
        itemCount: controller.newsList.length,
        itemBuilder: (BuildContext context, int itemIndex) {
          return GestureDetector(
            onTap: () {
              Get.toNamed('/home-detail',
                  arguments: [controller.newsList[itemIndex]]);
            },
            child: buildItemList2(itemIndex),
          );
        });
  }

  buildItemList2(int itemIndex) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          alignment: Alignment.center,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 100),
                child: Container(
                  constraints: const BoxConstraints(
                    maxHeight: 200,
                  ),
                  //height: 200,
                  child: FadeInImage(
                    placeholder:
                        const AssetImage('assets/images/image_placeholder.png'),
                    image: NetworkImage(
                        "${AppConfig.storageUrl}${controller.newsList[itemIndex].image}"),
                    fit: BoxFit.fitWidth,
                    width: Get.width,
                    imageErrorBuilder: (context, error, stackTrace) {
                      return const Image(
                        fit: BoxFit.cover,
                        image:
                            AssetImage('assets/images/image_placeholder.png'),
                      );
                    },
                  ),
                ),
              ),
              controller.newsList[itemIndex].title == ""
                  ? const SizedBox()
                  : Container(
                      width: Get.width - 40,
                      decoration: BoxDecoration(
                          boxShadow: const [
                            BoxShadow(
                                offset: Offset(0, 1),
                                blurRadius: 5,
                                color: Colors.black26)
                          ],
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20)),
                      margin: const EdgeInsets.only(bottom: 50),
                      child: Column(
                        //shrinkWrap: true,
                        children: [
                          Row(
                            children: [
                              Flexible(
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: AppConfig.defaultPadding * 2,
                                          left: AppConfig.defaultPadding * 2,
                                          right: AppConfig.defaultPadding * 2,
                                          bottom: AppConfig.defaultPadding),
                                      child: Text(
                                          controller
                                                  .newsList[itemIndex].title ??
                                              "",
                                          maxLines: 3,
                                          style: AppTextStyles.smallTitleBold,
                                          overflow: TextOverflow.ellipsis),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          left: AppConfig.defaultPadding * 2,
                                          right: AppConfig.defaultPadding * 2,
                                          bottom: AppConfig.defaultPadding * 2),
                                      child: ExtendedText(
                                        controller.newsList[itemIndex]
                                                .cleanContent ??
                                            "",
                                        maxLines: 2,
                                        style: AppTextStyles.smallText
                                            .copyWith(color: Colors.black),
                                        overflowWidget:
                                            const TextOverflowWidget(
                                          maxHeight: 40,
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: <Widget>[
                                              Text('\u2026 '),
                                              Text(
                                                "See more",
                                                style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: AppColor
                                                        .secondaryColor),
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(right: 5),
                                child: IconButton(
                                  icon: const Icon(Icons.chevron_right,
                                      color: AppColor.secondaryColor, size: 30),
                                  onPressed: () {},
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
            ],
          ),
        ),
      ],
    );
  }

  Future refreshData() async {
    debugPrint('reloadAPI');
  }
}
