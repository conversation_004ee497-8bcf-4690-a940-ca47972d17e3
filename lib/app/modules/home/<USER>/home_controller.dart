import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/controllers/user_controller.dart';
import 'package:automoment/app/models/vehicle_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../models/event_modal.dart';
import '../../../models/news_model.dart';
import '../../../services/api_client.dart';

class HomeController extends BaseScreenController {
  @override
  String get screenName => 'Home';

  UserController userController = Get.put(UserController());

  var personalBest = "".obs;
  var upcomingEvents = <Event>[].obs;
  var vehicles = <Vehicle>[].obs;
  var news = <News>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchHome();
  }

  Future<void> fetchHome() async {
    EasyLoading.show(status: 'Loading...');
    try {
      var response = await ApiClient.getHome(userController.getToken());

      var list = jsonDecode(response)['upcomingEvents'];
      var l = <Event>[];

      upcomingEvents.clear();

      list.forEach((dynamic d) {
        var n = Event.fromJson(d);
        l.add(n);
      });

      upcomingEvents.addAll(l);

      //

      var list2 = jsonDecode(response)['vehicles'];
      var l2 = <Vehicle>[];

      vehicles.clear();

      list2.forEach((dynamic d) {
        var n = Vehicle.fromJson(d);
        l2.add(n);
      });

      vehicles.addAll(l2);

      // //

      var list3 = jsonDecode(response)['news'];
      var l3 = <News>[];

      news.clear();

      list3.forEach((dynamic d) {
        var n = News.fromJson(d);
        l3.add(n);
      });

      news.addAll(l3);

      //

      personalBest.value = jsonDecode(response)['personalBest'] ??
          "Not yet clocked with Automoment";

      // if (pb == null) {
      //   personalBest.value = "Not yet clocked with Automoment";
      // }
      // else {
      //   personalBest.value = pb;
      // }

      EasyLoading.dismiss();
      debugPrint("fetchHome end");
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint("fetchHome error: ${e.toString()}");
      throw Exception(e.toString());
    }
  }
}
