import 'package:automoment/app/modules/events_form_signed/controllers/events_form_signed_controller.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:intl/intl.dart';

import '../../../models/event_modal.dart';

class EventsFormUpdateSuccessController extends BaseScreenController {
  @override
  String get screenName => 'Update Indemnity Form Success';

  var event = Event().obs;

  final EventsFormSignedController eventsFormSignedController =
      Get.put(EventsFormSignedController());

  @override
  void onInit() {
    super.onInit();

    var data = Get.arguments;
    event.value = data;

    //eventsFormSignedController.reloadForm();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  String showEventDate() {
    return DateFormat("dd MMMM yyyy").format(event.value.eventDate!);
  }
}
