import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../models/personal_result_model.dart';
import '../../../services/api_client.dart';

class PersonalResultsController extends BaseScreenController {
  @override
  String get screenName => 'Personal Results';

  final UserController userController = Get.put(UserController());

  var isLoading = false.obs;
  var personalResultList = <PersonalResult>[].obs;

  @override
  void onInit() {
    super.onInit();

    fetchMyPersonalResults();
  }

  Future<void> fetchMyPersonalResults() async {
    EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    try {
      var response = await ApiClient.myEvents(userController.getToken());
      var list = jsonDecode(response)['results'];
      var l = <PersonalResult>[];

      list.forEach((dynamic d) {
        var n = PersonalResult.fromJson(d);
        l.add(n);
      });

      personalResultList.value = l;

      isLoading.value = false;
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}
