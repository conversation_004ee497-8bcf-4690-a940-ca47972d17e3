import 'package:automoment/app/helpers/string_util.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../constants/app_config.dart';
import '../../../constants/app_text_styles.dart';
import '../../../models/event_modal.dart';
import '../../../routes/app_pages.dart';
import '../../shared/support_button.dart';
import '../controllers/personal_results_controller.dart';

class PersonalResultsView extends GetView<PersonalResultsController> {
  const PersonalResultsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text(
            'My Personal Results', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      floatingActionButton: const SupportButton(),
      body: Obx(() {
        return RefreshIndicator(
          onRefresh: () {
            return Future.delayed(const Duration(seconds: 1), () {
              controller.fetchMyPersonalResults();
            });
          },
          child: ListView.builder(
            //shrinkWrap: true,
            //physics: NeverScrollableScrollPhysics(),
              scrollDirection: Axis.vertical,
              itemCount: controller.personalResultList.length + 1,
              itemBuilder: (BuildContext context, int itemIndex) {
                return GestureDetector(
                  onTap: () {
                    if (itemIndex == 0) return;

                    Get.toNamed(Routes.PERLAPS,
                        arguments: Event(
                          id: controller.personalResultList[itemIndex-1].id,
                          title: controller.personalResultList[itemIndex-1].title,
                          eventDate: controller.personalResultList[itemIndex-1].eventDate,
                        )

                      // Get.toNamed(Routes.LEADERBOARD_DETAIL,
                    //     arguments: Event(
                    //       id: controller.personalResultList[itemIndex-1].id,
                    //       title: controller.personalResultList[itemIndex-1].title,
                    //       eventDate: controller.personalResultList[itemIndex-1].eventDate,
                    //     )
                    );
                    //Get.toNamed('/events-detail',
                    //    arguments: [controller.eventList.value[itemIndex]]);
                  },
                  child: buildItemList(itemIndex),
                );
              }),
        );
      }),
    );
  }

  buildItemList(int index) {
    int itemIndex = index - 1;
    return (index==0) ?
      Padding(
      padding: const EdgeInsets.only(
        left: AppConfig.defaultPadding,
        right: AppConfig.defaultPadding,
        top: AppConfig.defaultPadding / 2,
        //bottom: AppConfig.defaultPadding / 2
      ),
      child: Card(
          elevation: 0,
          color: Colors.transparent,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [

              const SizedBox(height: 13),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "Pos",
                    style: AppTextStyles.titleText.copyWith(color: Colors.black, fontSize: 14),
                  ),
                  const SizedBox(width: 17,),
                  Text(
                    "Event",
                    style: AppTextStyles.titleText.copyWith(color: Colors.black, fontSize: 14),
                  ),
                  const Spacer(),
                  Text(
                    "Best Tm",
                    style: AppTextStyles.titleText.copyWith(color: Colors.black, fontSize: 14),
                  ),
                  const SizedBox(width: 5,),
                ],
              ),

            ],
          )
      ),
    ) :
      Padding(
      padding: const EdgeInsets.only(
          left: AppConfig.defaultPadding,
          right: AppConfig.defaultPadding,
          top: AppConfig.defaultPadding / 2,
          bottom: AppConfig.defaultPadding / 2
      ),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: Text (
                (controller.personalResultList[itemIndex].position == 0) ? "-" :
                controller.personalResultList[itemIndex].position.toString(),
                style: AppTextStyles.titleText.copyWith(color: Colors.black, fontSize: 20),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                        left: AppConfig.defaultPadding,
                        right: AppConfig.defaultPadding,
                        top: AppConfig.defaultPadding,
                        bottom: AppConfig.defaultPadding / 2
                    ),
                    child: Text(controller.personalResultList[itemIndex].title ?? "",
                        maxLines: 1,
                        style: AppTextStyles.smallTitleBold,
                        overflow: TextOverflow.ellipsis
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: AppConfig.defaultPadding,
                        right: AppConfig.defaultPadding,
                        bottom: AppConfig.defaultPadding
                    ),
                    child: Text( //"date here",
                      StringUtil.dateTimeToString(
                          controller.personalResultList[itemIndex].eventDate!),
                      style: AppTextStyles.smallText.copyWith(color: Colors.black),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: Text (
                (controller.personalResultList[itemIndex].time!.isNotEmpty) ?
                controller.personalResultList[itemIndex].time! : "00:00 000",
                style: AppTextStyles.normalText.copyWith(color: Colors.black, fontSize: 12),
              ),
            ),
          ],
        )
      ),
    );
  }
}
