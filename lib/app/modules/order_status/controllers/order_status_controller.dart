import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:automoment/app/helpers/snack_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';

class OrderStatusController extends BaseScreenController {
  @override
  String get screenName => 'Order Status';

  final UserController userController = Get.find<UserController>();
  final isLoading = false.obs;
  final orders = <Map<String, dynamic>>[].obs;
  final currentPage = 1.obs;
  final isLastPage = false.obs;
  final isLoadingMore = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadOrders();
  }

  Future<void> loadOrders({bool refresh = false}) async {
    try {
      if (refresh) {
        currentPage.value = 1;
        isLastPage.value = false;
        isLoading.value = true;
      } else {
        if (isLastPage.value || isLoadingMore.value) return;
        isLoadingMore.value = true;
      }

      final token = userController.getToken();
      if (token == null || token.toString().isEmpty) {
        throw Exception('User token not found');
      }

      final response =
          await ApiClient.getUserOrders(token, page: currentPage.value);
      final data = json.decode(response);

      if (data['success'] == true) {
        final List<Map<String, dynamic>> newOrders =
            List<Map<String, dynamic>>.from(data['data'] ?? []);
        final meta = data['meta'] as Map<String, dynamic>;

        if (refresh) {
          orders.value = newOrders;
        } else {
          orders.addAll(newOrders);
        }

        // Check if we've reached the last page
        if (meta['current_page'] >= meta['last_page']) {
          isLastPage.value = true;
        } else {
          currentPage.value++;
        }
      } else {
        throw Exception(data['message'] ?? 'Failed to load orders');
      }
    } catch (e) {
      SnackHelper.showError('Failed to load orders');
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  String getOrderStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  String getPaymentStatus(String? status) {
    if (status == null) return 'Unknown';

    switch (status.toLowerCase()) {
      case 'paid':
        return 'Paid';
      case 'pending':
        return 'Pending';
      case 'failed':
        return 'Failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  Color getStatusColor(String? status) {
    if (status == null) return Colors.grey;

    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'shipped':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'failed':
        return Colors.red;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
