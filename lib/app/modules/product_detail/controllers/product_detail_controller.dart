import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../controllers/base_screen_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/product_model.dart';
import '../../../services/api_client.dart';
import '../../../modules/cart/controllers/cart_controller.dart';

class ProductDetailController extends BaseScreenController {
  final userController = Get.find<UserController>();
  late final cartController = Get.find<CartController>();
  var product = Product().obs;
  late int productId;

  @override
  String get screenName => 'Product Detail';

  @override
  void onInit() {
    if (Get.arguments is String) {
      debugPrint('Argument is String - Product ID: ${Get.arguments}');
      Map<String, dynamic> jsonData = jsonDecode(Get.arguments);
      productId = jsonData['id'];
    } else {
      debugPrint('Argument is else - Product ID: ${Get.arguments}');
      productId = Get.arguments;
    }

    super.onInit();
    fetchProductDetail();
  }

  Future<void> fetchProductDetail() async {
    try {
      EasyLoading.show(status: 'loading...');
      var response = await ApiClient.getProductDetail(
          userController.getToken(), productId);
      EasyLoading.dismiss();
      product.value = Product.fromJson(jsonDecode(response));

      // Log view item event
      await analytics.logViewItem(
        itemId: product.value.id.toString(),
        itemName: product.value.name ?? 'Unknown Product',
        category: product.value.category ?? 'Uncategorized',
      );
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> addToCart() async {
    try {
      EasyLoading.show(status: 'Adding to cart...');
      await cartController.addToCart(product.value);

      // Log add to cart event
      await analytics.logAddToCart(
        itemId: product.value.id.toString(),
        itemName: product.value.name ?? 'Unknown Product',
        price: product.value.price?.toDouble(),
        quantity: 1,
      );

      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
    }
  }
}
