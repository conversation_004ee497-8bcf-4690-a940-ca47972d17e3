import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../models/event_modal.dart';
import '../../events_detail/controllers/events_detail_controller.dart';

class EventsFormSignSuccessController extends BaseScreenController {
  @override
  String get screenName => 'Indemnity Form Sign Success';

  var event = Event().obs;

  final EventsDetailController eventDetailController =
      Get.put(EventsDetailController());

  @override
  void onInit() {
    super.onInit();

    var data = Get.arguments;
    event.value = data;

    eventDetailController.fetchIsEventFormSigned();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  String showEventDate() {
    return DateFormat("dd MMMM yyyy").format(event.value.eventDate!);
  }
}
