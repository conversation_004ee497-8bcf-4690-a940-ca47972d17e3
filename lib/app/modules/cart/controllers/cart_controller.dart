import 'package:automoment/app/constants/app_config.dart';
import 'package:automoment/app/helpers/debug_helper.dart';
import 'package:get/get.dart';
import '../../../controllers/base_screen_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../../controllers/user_controller.dart';
import '../../../services/api_client.dart';

class CartController extends BaseScreenController {
  @override
  String get screenName => 'Cart';

  static CartController get to => Get.find();
  final cartCount = 0.obs;
  final cartItems = [].obs;
  final isLoading = false.obs;
  late SharedPreferences prefs;
  static const String cartKey = 'cart_items';
  final userController = Get.find<UserController>();

  @override
  void onInit() {
    super.onInit();
    initPrefs();
  }

  Future<void> initPrefs() async {
    prefs = await SharedPreferences.getInstance();
    await fetchCartFromApi();
  }

  String? _getProductImage(dynamic product) {
    // Try featured_image first, if not exist use first image from images array
    if (product['featured_image'] != null) {
      return AppConfig.storageUrl + product['featured_image'];
    }

    final images = product['images'] as List?;
    if (images != null && images.isNotEmpty) {
      return AppConfig.storageUrl + images.first['image'];
    }

    return null;
  }

  Future<void> fetchCartFromApi() async {
    try {
      isLoading.value = true;
      final response = await ApiClient.fetchCart(userController.getToken());
      if (response['items'] != null) {
        final List<dynamic> items = response['items'];
        cartCount.value = items.length;

        // Update local cart items
        cartItems.value = items
            .map((item) => {
                  'id': item['id'],
                  'product_id': item['product']['id'],
                  'name': item['product']['name'],
                  'price':
                      double.tryParse(item['price']['sgd'].toString()) ?? 0.0,
                  'quantity': item['quantity'],
                  'image': _getProductImage(item['product']),
                })
            .toList();

        await saveCartItems();
      }
    } catch (e) {
      DebugHelper.d('Error fetching cart: $e');
      // Load from local storage as fallback
      loadCartItems();
    } finally {
      isLoading.value = false;
    }
  }

  void loadCartItems() {
    final cartData = prefs.getString(cartKey);
    if (cartData != null) {
      final List<dynamic> items = jsonDecode(cartData);
      cartItems.value = items;
      cartCount.value = items.length;
    }
  }

  Future<void> saveCartItems() async {
    final String cartData = jsonEncode(cartItems);
    await prefs.setString(cartKey, cartData);
    cartCount.value = cartItems.length;
  }

  Future<void> addToCart(dynamic product) async {
    try {
      isLoading.value = true;
      final response =
          await ApiClient.addToCart(userController.getToken(), product.id!, 1);

      // Update cart count based on number of items in the response
      if (response['items'] != null) {
        final List<dynamic> items = response['items'];
        cartCount.value = items.length;
      }

      // Check if item already exists in cart
      final existingItemIndex =
          cartItems.indexWhere((item) => item['product_id'] == product.id);

      if (existingItemIndex != -1) {
        // Update existing item quantity
        final updatedItem =
            Map<String, dynamic>.from(cartItems[existingItemIndex]);
        updatedItem['quantity'] = (updatedItem['quantity'] ?? 0) + 1;
        cartItems[existingItemIndex] = updatedItem;
      } else {
        // Add new item with correct price
        cartItems.add({
          'product_id': product.id,
          'name': product.name,
          'price':
              double.tryParse(product.price['sgd']['regular'].toString()) ??
                  0.0,
          'image': product.featuredImage ??
              (product.images?.isNotEmpty == true
                  ? product.images!.first['image']
                  : null),
          'quantity': 1,
        });
      }

      await saveCartItems();
    } catch (e) {
      DebugHelper.d('Error adding to cart: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateQuantity(int index, bool increase) async {
    try {
      isLoading.value = true;
      final item = cartItems[index];
      final newQuantity =
          increase ? (item['quantity'] ?? 0) + 1 : (item['quantity'] ?? 0) - 1;

      if (newQuantity <= 0) {
        await removeFromCart(index);
        return;
      }

      // Update local cart item first for immediate feedback
      final updatedItem = Map<String, dynamic>.from(item);
      updatedItem['quantity'] = newQuantity;
      cartItems[index] = updatedItem;
      await saveCartItems();

      final response = await ApiClient.updateCartItemQuantity(
          userController.getToken(),
          item['id'], // Using cart item id instead of product_id
          newQuantity);

      if (response['items'] != null) {
        // Refresh from API to ensure sync
        await fetchCartFromApi();
      }
    } catch (e) {
      DebugHelper.d('Error updating quantity: $e');
      // Revert local changes on error
      await fetchCartFromApi();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> removeFromCart(int index) async {
    try {
      isLoading.value = true;
      final item = cartItems[index];

      await ApiClient.removeCartItem(userController.getToken(),
          item['id'] // Using cart item id instead of product_id
          );

      // Remove item locally first for immediate feedback
      cartItems.removeAt(index);
      await saveCartItems();

      // Then refresh from API
      await fetchCartFromApi();
    } catch (e) {
      DebugHelper.d('Error removing item: $e');
      // Revert local changes on error
      await fetchCartFromApi();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> clearCart() async {
    cartItems.clear();
    await saveCartItems();
  }

  double calculateTotal() {
    return cartItems.fold(0.0, (sum, item) {
      final price =
          item['price'] is num ? (item['price'] as num).toDouble() : 0.0;
      final quantity =
          item['quantity'] is num ? (item['quantity'] as num).toInt() : 0;
      return sum + (price * quantity);
    });
  }
}
