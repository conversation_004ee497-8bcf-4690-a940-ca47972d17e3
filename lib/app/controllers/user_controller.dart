import 'dart:convert';

import 'package:automoment/app/services/firebase_auth_service.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../models/user_model.dart';
import '../services/api_client.dart';

class UserController extends GetxController {
  var user = User().obs;
  var isLoggedIn = false.obs;
  String token = '';
  var unreadNotificationCount = 0.obs;
  var isLoggedInWithFirebase = false.obs;

  @override
  void onInit() {
    super.onInit();

    isLoggedIn.value = GetStorage().read('isLoggedIn') ?? false;

    GetStorage().listenKey('isLoggedIn', (value) {
      isLoggedIn.value = value;
    });

    user.value = getUser();
  }


  bool isUserLoggedIn() {
    return GetStorage().read('isLoggedIn') ?? false;
  }

  String getBirthDateFormatted() {
    return user.value.birth == null ? "-" : DateFormat("dd MMMM yyyy").format(user.value.birth!);
  }

  DateTime? userController() {
    return user.value.birth;
  }

  setToken(String token) {
    GetStorage().write('token', token);
  }

  getToken() {
    return GetStorage().read('token');
  }

  void setIsLoggedIn(bool value) {
    GetStorage().write('isLoggedIn', value);
  }

  setUser(User user) {
    this.user.value = user;
    GetStorage().write('user', user.toJson());
  }

  User getUser() {
    if (GetStorage().read('user') != null) {
      return User.fromJson(GetStorage().read('user'));
    }
    return User();
  }

  void logout() {
    callLogoutApi();

    GetStorage().remove('user');
    GetStorage().remove('isLoggedIn');
    GetStorage().remove('token');

    isLoggedIn.value = false;
    user.value = User();
    token = '';

    if (isLoggedInWithFirebase.value) {
      FirebaseAuthService.logout();
      isLoggedInWithFirebase.value = false;
    }
  }

  Future<void> callLogoutApi() async {
    EasyLoading.show(status: 'Please wait...');
    try {
      await ApiClient.logout(getToken());
      EasyLoading.dismiss();
    } catch(e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }

  Future<void> callRegisterFcmTokenApi() async {
    try {
      await ApiClient.registerFcmToken(getToken(), GetStorage().read('fcmToken'));
    } catch(e) {
      throw Exception(e.toString());
    }
  }

  Future<void> callGetUnreadNotificationCountApi() async {
    try {
      var response = await ApiClient.getUnreadNotificationCount(getToken(), getUser().id!);
      var success = jsonDecode(response)['success'];
      if (success) {
        unreadNotificationCount.value = jsonDecode(response)['count'];
      }
    } catch(e) {
      throw Exception(e.toString());
    }
  }

}
