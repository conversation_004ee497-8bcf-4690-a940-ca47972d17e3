import 'package:get/get.dart';
import '../services/analytics_service.dart';

/// A base controller that provides analytics functionality
/// All controllers that need analytics tracking should extend this class
class BaseScreenController extends GetxController {
  late final AnalyticsService _analyticsService;

  BaseScreenController() {
    // Get the analytics service from GetX dependency injection
    _analyticsService = Get.find<AnalyticsService>();
  }

  /// Log a screen view event
  /// This should be called in onReady() of each controller
  Future<void> logScreenView(String screenName) async {
    await _analyticsService.logScreenView(screenName: screenName);
  }

  /// Override this in child controllers to provide the screen name
  String get screenName => '';

  @override
  void onReady() {
    super.onReady();
    // Automatically log screen view if screen name is provided
    if (screenName.isNotEmpty) {
      logScreenView(screenName);
    }
  }

  /// Access to the analytics service for custom events
  AnalyticsService get analytics => _analyticsService;
}
