<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="sg.com.automoment.automoment">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

    <application
        android:label="Automoment"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">

       <meta-data android:name="google_analytics_automatic_screen_reporting_enabled" android:value="false" />

       <meta-data
           android:name="com.google.android.gms.wallet.api.enabled"
           android:value="true" />

       <meta-data
           android:name="com.google.firebase.messaging.default_notification_channel_id"
           android:value="high_importance_channel" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="https" />
                <data android:host="automoment.page.link"/>
                <data android:host="link.automoment.com.sg"/>
                <data android:host="automoment.com.sg"/>
                <data android:pathPrefix="/event/"/>
                <data android:pathPrefix="/news/"/>
                <data android:pathPrefix="/lifestyle/"/>
                <data android:pathPrefix="/upcoming-events"/>
                <data android:pathPrefix="/latest-news"/>
                <data android:pathPrefix="/account"/>
                <data android:pathPrefix="/reward"/>
                <data android:pathPrefix="/prod-detail/"/>
                <data android:pathPrefix="/car/"/>
                <data android:pathPrefix="/lb-detail/"/>
                <data android:pathPrefix="/leaderboard-all-time"/>
                <data android:pathPrefix="/leaderboard-this-year"/>
                <data android:pathPrefix="/storehome"/>                
            </intent-filter>
            
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="automoment"/>
                <data android:host="event"/>
                <data android:host="news"/>
                <data android:host="lifestyle"/>
                <data android:host="upcoming-events"/>
                <data android:host="latest-news"/>
                <data android:host="account"/>
                <data android:host="reward"/>
                <data android:host="prod-detail"/>
                <data android:host="car"/>
                <data android:host="lb-detail"/>
                <data android:host="leaderboard-all-time"/>
                <data android:host="leaderboard-this-year"/>
                <data android:host="storehome"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
