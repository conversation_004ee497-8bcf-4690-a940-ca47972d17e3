# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
# https://stackoverflow.com/questions/70037537/proguard-missing-classes-detected-while-running-r8-after-adding-package-names-in
-dontwarn com.google.j2objc.annotations.ReflectionSupport$Level
-dontwarn com.google.j2objc.annotations.ReflectionSupport
-dontwarn com.google.j2objc.annotations.RetainedWith
-dontwarn com.huawei.android.os.BuildEx$VERSION
-dontwarn com.huawei.android.telephony.ServiceStateEx
-dontwarn com.huawei.hms.framework.network.restclient.hianalytics.RCEventListener
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.Interceptor$Chain
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.RealInterceptorChain
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.Request$Builder
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.Request
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.Response
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.plugin.BasePlugin
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.plugin.PluginInterceptor
-dontwarn com.huawei.hms.framework.network.restclient.hwhttp.url.HttpUrl
-dontwarn com.huawei.libcore.io.ExternalStorageFile
-dontwarn com.huawei.libcore.io.ExternalStorageFileInputStream
-dontwarn com.huawei.libcore.io.ExternalStorageFileOutputStream
-dontwarn com.huawei.libcore.io.ExternalStorageRandomAccessFile
-dontwarn com.huawei.secure.android.common.util.SafeBase64
-dontwarn com.huawei.secure.android.common.util.SafeString
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivity$g
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Args
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Error
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider

# Keep scan module resources
-keep class com.chavesgu.scan.** { *; }
-keepclassmembers class com.chavesgu.scan.** { *; }
-keep class com.google.zxing.** { *; }
-keepclassmembers class com.google.zxing.** { *; }