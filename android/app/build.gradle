plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def debug_keystoreProperties = new Properties()
def debug_keystorePropertiesFile = rootProject.file('key_debug.properties')
if (debug_keystorePropertiesFile.exists()) {
    debug_keystoreProperties.load(new FileInputStream(debug_keystorePropertiesFile))
}


android {
    namespace "sg.com.automoment.automoment"
    compileSdk 35
    //flutter.compileSdkVersion
    ndkVersion "27.1.12297006" //flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "sg.com.automoment.automoment"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 29//flutter.minSdkVersion
        targetSdkVersion 35//flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // fix couldn't find "libflutter.so"
        // https://github.com/flutter/flutter/issues/32756
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64'
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
        debug {
            keyAlias debug_keystoreProperties['keyAlias']
            keyPassword debug_keystoreProperties['keyPassword']
            storeFile debug_keystoreProperties['storeFile'] ? file(debug_keystoreProperties['storeFile']) : null
            storePassword debug_keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            shrinkResources false
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            resValue "string", "debug_firebase_analytics", "true"
        }
    }
    buildToolsVersion '35.0.0'


}

flutter {
    source '../..'
}

dependencies {
    implementation 'com.stripe:stripe-android:20.19.2'
    implementation 'com.stripe:stripecardscan:20.19.1'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
}
