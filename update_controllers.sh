#!/bin/bash

# This script updates controller classes to extend BaseScreenController
# and adds the screenName getter

update_controller() {
    local file_path=$1
    local screen_name=$2
    
    # Extract the file name without path and extension
    local file_name=$(basename "$file_path" .dart)
    
    # Check if the file exists
    if [ ! -f "$file_path" ]; then
        echo "File not found: $file_path"
        return
    fi
    
    echo "Updating $file_path with screen name: $screen_name"
    
    # Update the imports and class declaration
    sed -i '' -e 's/import '\''package:get\/get.dart'\'';/import '\''package:get\/get.dart'\'';\nimport '\''..\/..\/..\/controllers\/base_screen_controller.dart'\'';/g' "$file_path"
    sed -i '' -e "s/class $file_name extends GetxController/class $file_name extends BaseScreenController {\n  @override\n  String get screenName => '$screen_name';/g" "$file_path"
    
    # Remove any existing Firebase Analytics screen tracking in onReady
    sed -i '' -e '/FirebaseAnalytics.*logScreenView/d' "$file_path"
    sed -i '' -e '/FirebaseAnalytics.*setCurrentScreen/d' "$file_path"
    
    # Update onReady method if it exists
    if grep -q "void onReady()" "$file_path"; then
        sed -i '' -e 's/void onReady() {/void onReady() {\n    super.onReady();\n    \/\/ BaseScreenController will handle logging screen view/g' "$file_path"
    elif grep -q "Future<void> onReady()" "$file_path"; then
        sed -i '' -e 's/Future<void> onReady() async {/Future<void> onReady() async {\n    super.onReady();\n    \/\/ BaseScreenController will handle logging screen view/g' "$file_path"
    fi
    
    echo "Updated $file_path"
}

# Update controllers with appropriate screen names
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/events_form/controllers/events_form_controller.dart" "Event Registration Form"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/make_payment/controllers/make_payment_controller.dart" "Make Payment"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/events_form_success/controllers/events_form_success_controller.dart" "Registration Success"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/sell_my_slot/controllers/sell_my_slot_controller.dart" "Sell My Slot"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/forgot_password_check_code/controllers/forgot_password_check_code_controller.dart" "Verify Reset Code"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/join_waiting_list/controllers/join_waiting_list_controller.dart" "Join Waiting List"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/passenger_form/controllers/passenger_form_controller.dart" "Passenger Form"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/check_in/controllers/check_in_controller.dart" "Check In"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/events_form_update_success/controllers/events_form_update_success_controller.dart" "Update Success"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/leaderboard_detail/controllers/leaderboard_detail_controller.dart" "Leaderboard Detail"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/events_form_sign/controllers/events_form_sign_controller.dart" "Sign Indemnity Form"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/product_payment/controllers/product_payment_controller.dart" "Product Payment"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/vehicles/controllers/vehicles_controller.dart" "My Vehicles"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/booking_success/controllers/booking_success_controller.dart" "Booking Success"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/social_login_form/controllers/social_login_form_controller.dart" "Complete Profile"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/forgot_password_reset/controllers/forgot_password_reset_controller.dart" "Reset Password"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/cart/controllers/cart_controller.dart" "Shopping Cart"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/store/controllers/store_controller.dart" "Store"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/login/controllers/login_controller.dart" "Login"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/register/controllers/register_controller.dart" "Register"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/forgot_password/controllers/forgot_password_controller.dart" "Forgot Password"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/events/controllers/events_controller.dart" "Events"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/events_detail/controllers/events_detail_controller.dart" "Event Details"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/booking/controllers/booking_controller.dart" "Booking"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/leaderboard/controllers/leaderboard_controller.dart" "Leaderboard"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/car_listing_detail/controllers/car_listing_detail_controller.dart" "Car Details"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/shipping_address/controllers/shipping_address_controller.dart" "Shipping Addresses"
update_controller "/Users/<USER>/workspace/automoment/automoment-flutter/lib/app/modules/checkout/controllers/checkout_controller.dart" "Checkout"

echo "All controllers updated successfully!"