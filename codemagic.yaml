workflows:
  android-workflow:
    name: Build android release
    max_build_duration: 60
    environment:
      flutter: 3.29.2
      xcode: latest
      cocoapods: default
    cache:
      cache_paths: []
    triggering:
      events:
        - push
      branch_patterns:
        - pattern: 'test'
          include: true
          source: true
        - pattern: 'release'
          include: true
          source: true
      tag_patterns:
        - pattern: '*'
          include: true
    scripts:
      - |
        # set up local properties
        echo "flutter.sdk=$HOME/programs/flutter" > "$FCI_BUILD_DIR/android/local.properties"
      - flutter packages pub get
      - flutter build appbundle
    artifacts:
      - build/**/outputs/apk/**/*.apk
      - build/**/outputs/bundle/**/*.aab
      - build/**/outputs/**/mapping.txt
      - '*.snap'
      - build/windows/**/*.msix
      - flutter_drive.log
    publishing:
      email:
        recipients:
          - <EMAIL>
  ios-workflow:
    name: Build ios release
    max_build_duration: 60
    integrations:
      app_store_connect: Dody App Store
    environment:
      flutter: 3.29.2
      xcode: latest
      cocoapods: default
      ios_signing:
        distribution_type: app_store
        bundle_identifier: sg.com.automoment.automoment
    cache:
      cache_paths: []
    triggering:
      events:
        - push
      branch_patterns:
        - pattern: 'test'
          include: true
          source: true
        - pattern: 'release'
          include: true
          source: true
      tag_patterns:
        - pattern: '*'
          include: true
    scripts:
      - name: Set up code signing settings on Xcode project
        script: |
          xcode-project use-profiles
      - name: Get Flutter packages
        script: |
          flutter packages pub get
      #- name: Install pods
      #  script: |
      #    find . -name "Podfile" -execdir pod install \;
      - name: Flutter build ipa
        script: |
          flutter build ipa --release \
          --export-options-plist=/Users/<USER>/export_options.plist
    artifacts:
      - build/ios/ipa/*.ipa
      - /tmp/xcodebuild_logs/*.log
      - flutter_drive.log
    publishing:
      email:
        recipients:
          - <EMAIL>
      app_store_connect:
        auth: integration
        submit_to_testflight: true