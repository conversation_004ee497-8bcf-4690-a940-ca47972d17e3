# Automoment

A Flutter mobile application using GetX state management pattern for efficient and reactive state management.

## Features

- Clean architecture implementation
- GetX state management
- API integration with dedicated service layer
- Organized project structure

## Project Structure

```
lib/
├── app/
│   ├── data/
│   ├── modules/
│   ├── routes/
│   └── services/
│       └── api_client.dart
├── main.dart
```

## Getting Started

### Prerequisites

- Flutter SDK
- Dart SDK
- Your preferred IDE (VS Code, Android Studio, etc.)

### Installation

1. Clone the repository:
```bash
<NAME_EMAIL>:waiwaiautomoment/Automoment_Mobile.git
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the app:
```bash
flutter run
```

## Architecture

This project follows a clean architecture pattern with GetX state management. All API calls are handled through the dedicated service layer in `app/services/api_client.dart`.

### API Integration

- All API calls are centralized in `api_client.dart`
- Methods return String responses
- Raw response handling is maintained at the service layer
- Data mapping is handled in the respective modules
