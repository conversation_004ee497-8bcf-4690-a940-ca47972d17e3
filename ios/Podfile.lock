PODS:
  - app_links (0.0.2):
    - Flutter
  - app_tracking_transparency (0.0.1):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - background_downloader (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase (11.11.0):
    - Firebase/Core (= 11.11.0)
  - Firebase/AnalyticsWithoutAdIdSupport (11.11.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 11.11.0)
  - Firebase/Auth (11.11.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.11.0)
  - Firebase/Core (11.11.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.11.0)
  - Firebase/CoreOnly (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - Firebase/Database (11.11.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 11.11.0)
  - Firebase/Messaging (11.11.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.11.0)
  - Firebase/Storage (11.11.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 11.11.0)
  - firebase_analytics (11.3.2):
    - Firebase/AnalyticsWithoutAdIdSupport (= 11.11.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.5.3):
    - Firebase/Auth (= 11.11.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.11.0)
    - Flutter
  - firebase_database (11.1.3):
    - Firebase/Database (= 11.11.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.1.2):
    - Firebase/Messaging (= 11.11.0)
    - firebase_core
    - Flutter
  - firebase_storage (12.3.1):
    - Firebase/Storage (= 11.11.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.11.0):
    - FirebaseAnalytics/AdIdSupport (= 11.11.0)
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/WithoutAdIdSupport (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.11.0)
  - FirebaseAuth (11.11.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.11.0)
    - FirebaseCoreExtension (~> 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.11.0)
  - FirebaseCore (11.11.0):
    - FirebaseCoreInternal (~> 11.11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.11.0):
    - FirebaseCore (~> 11.11.0)
  - FirebaseCoreInternal (11.11.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseDatabase (11.11.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - leveldb-library (~> 1.22)
  - FirebaseInstallations (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.11.0):
    - FirebaseCore (~> 11.11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseSharedSwift (11.11.0)
  - FirebaseStorage (11.11.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.11.0)
    - FirebaseCoreExtension (~> 11.11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (11.11.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.11.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.11.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities (8.0.2):
    - GoogleUtilities/AppDelegateSwizzler (= 8.0.2)
    - GoogleUtilities/Environment (= 8.0.2)
    - GoogleUtilities/Logger (= 8.0.2)
    - GoogleUtilities/MethodSwizzler (= 8.0.2)
    - GoogleUtilities/Network (= 8.0.2)
    - "GoogleUtilities/NSData+zlib (= 8.0.2)"
    - GoogleUtilities/Privacy (= 8.0.2)
    - GoogleUtilities/Reachability (= 8.0.2)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.0.2)
    - GoogleUtilities/UserDefaults (= 8.0.2)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.0.2):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - libphonenumber_plugin (0.0.1):
    - Flutter
    - PhoneNumberKit
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - mobile_scanner (6.0.2):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 7.0.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pay_ios (0.0.1):
    - Flutter
  - PhoneNumberKit (3.7.11):
    - PhoneNumberKit/PhoneNumberKitCore (= 3.7.11)
    - PhoneNumberKit/UIKit (= 3.7.11)
  - PhoneNumberKit/PhoneNumberKitCore (3.7.11)
  - PhoneNumberKit/UIKit (3.7.11):
    - PhoneNumberKit/PhoneNumberKitCore
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (101.0.0)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.2):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - Stripe (23.28.3):
    - StripeApplePay (= 23.28.3)
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripePaymentsUI (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.28.0)
    - StripeApplePay (~> 23.28.0)
    - StripeFinancialConnections (~> 23.28.0)
    - StripePayments (~> 23.28.0)
    - StripePaymentSheet (~> 23.28.0)
    - StripePaymentsUI (~> 23.28.0)
  - StripeApplePay (23.28.3):
    - StripeCore (= 23.28.3)
  - StripeCore (23.28.3)
  - StripeFinancialConnections (23.28.3):
    - StripeCore (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - StripePayments (23.28.3):
    - StripeCore (= 23.28.3)
    - StripePayments/Stripe3DS2 (= 23.28.3)
  - StripePayments/Stripe3DS2 (23.28.3):
    - StripeCore (= 23.28.3)
  - StripePaymentSheet (23.28.3):
    - StripeApplePay (= 23.28.3)
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripePaymentsUI (= 23.28.3)
  - StripePaymentsUI (23.28.3):
    - StripeCore (= 23.28.3)
    - StripePayments (= 23.28.3)
    - StripeUICore (= 23.28.3)
  - StripeUICore (23.28.3):
    - StripeCore (= 23.28.3)
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (1.7.5):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - background_downloader (from `.symlinks/plugins/background_downloader/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Firebase
  - Firebase/Auth
  - Firebase/CoreOnly
  - Firebase/Database
  - Firebase/Messaging
  - Firebase/Storage
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_database (from `.symlinks/plugins/firebase_database/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - FirebaseAuth
  - FirebaseCore
  - FirebaseDatabase
  - FirebaseMessaging
  - FirebaseStorage
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - GoogleUtilities
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - libphonenumber_plugin (from `.symlinks/plugins/libphonenumber_plugin/ios`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pay_ios (from `.symlinks/plugins/pay_ios/ios`)
  - PhoneNumberKit (~> 3.7.6)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseDatabase
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSharedSwift
    - FirebaseStorage
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleSignIn
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PhoneNumberKit
    - PromisesObjC
    - RecaptchaInterop
    - SDWebImage
    - Sentry
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - SwiftyGif

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  background_downloader:
    :path: ".symlinks/plugins/background_downloader/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_database:
    :path: ".symlinks/plugins/firebase_database/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  libphonenumber_plugin:
    :path: ".symlinks/plugins/libphonenumber_plugin/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pay_ios:
    :path: ".symlinks/plugins/pay_ios/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  app_tracking_transparency: 3d84f147f67ca82d3c15355c36b1fa6b66ca7c92
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  background_downloader: b0572309a68d929c35941f0484e8ad7c65052228
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: 6a8f201c61eda24e98f1ce2b44b1b9c2caf525cc
  firebase_analytics: e8a0dfe490727d8c93dbbf1edbbfafe72d8b2148
  firebase_auth: 9372cb8ebe1b82e8b66a9691ab5fee5062b3c5a2
  firebase_core: 44da53d8cd80ec4cbe58347ba145b4f4b0a8078a
  firebase_database: 9021cf26ed6dc245dacda905364c29288ebd5292
  firebase_messaging: 1e51f146f80a856d9cfacf221fb46e1c65062675
  firebase_storage: 185e15b72abe21202797dfbeb06e6acd77a360c2
  FirebaseAnalytics: acfa848bf81e1a4dbf60ef1f0eddd7328fe6673e
  FirebaseAppCheckInterop: f23709c9ce92d810aa53ff4ce12ad3e666a3c7be
  FirebaseAuth: 783fa4cc0420b0f6eed580721b12d4f0a5306746
  FirebaseAuthInterop: ac22ed402c2f4e3a8c63ebd3278af9a06073c1be
  FirebaseCore: 2321536f9c423b1f857e047a82b8a42abc6d9e2c
  FirebaseCoreExtension: 3a64994969dd05f4bcb7e6896c654eded238e75b
  FirebaseCoreInternal: 31ee350d87b30a9349e907f84bf49ef8e6791e5a
  FirebaseDatabase: d91ea8ab77099cb8dbac6eb0d569ff0d525c4506
  FirebaseInstallations: 781e0e37aa0e1c92b44d00e739aba79ad31b2dba
  FirebaseMessaging: c7be9357fd8ba33bc45b9a6c3cdff0b466e1e2a4
  FirebaseSharedSwift: b1d32c3b29a911dc174bcf363f2f70bda9509d2f
  FirebaseStorage: 1c04b0ac8126b6fada8c9a09458f59c31868f2df
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  google_sign_in_ios: 0ab078e60da6dfe23cbc55c83502b52bba1aad63
  GoogleAppMeasurement: 8a82b93a6400c8e6551c0bcd66a9177f2e067aed
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libphonenumber_plugin: d134f173b22bfa5ede50887071f087f309277f8c
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  mobile_scanner: af8f71879eaba2bbcb4d86c6a462c3c0e7f23036
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: 580e9a5f1b6ca5594e7c9ed5f92d1dfb2a66b5e1
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  pay_ios: 2c48d76615ded0cfaff66185669501b8b1dfc55e
  PhoneNumberKit: ced55861269312a5e3bc2ef82a58d6255b1c976a
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 27892878729f42701297c628eb90e7c6529f3684
  share_plus: 011d6fb4f9d2576b83179a3a5c5e323202cdabcf
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  Stripe: cdf416cf2efe286f532a6306de0fcaa0ecc8c71a
  stripe_ios: 3c4c044a789cecb94d038cc49e7f8b621c742a17
  StripeApplePay: efb62ffc08e6cd4f161d77ddb45de2451075c54e
  StripeCore: 9731f05e327c3dcaf7d7abd116840ceaa9482bbe
  StripeFinancialConnections: 46c0049aaab3a179193502bce4a8096eb7b73f55
  StripePayments: dd1867a620b0b8b5e294e9ff2f1f7b7770765f47
  StripePaymentSheet: d155dfde74e90784d054deffb4f561a1f6dd638f
  StripePaymentsUI: c24f990b03a68a7f6fe704b15dd487e7bb6b603e
  StripeUICore: f2d514e900c37436dc5427fdf2c29d68ab1c2935
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  vibration: 8e2f50fc35bb736f9eecb7dd9f7047fbb6a6e888
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 321b57d3022bcdc6387a5c501b70d72bacb7ff5c

COCOAPODS: 1.16.2
