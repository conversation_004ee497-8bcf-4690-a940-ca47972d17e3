$FirebaseSDKVersion = '11.11.0'
$FirebaseAnalyticsWithoutAdIdSupport = true

# Uncomment this line to define a global platform for your project
platform :ios, '17.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

# Add this function to configure Firebase pods as static frameworks
def firebase_static_frameworks
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseDatabase', :modular_headers => true
  pod 'FirebaseMessaging', :modular_headers => true
  #pod 'FirebaseDynamicLinks', :modular_headers => true
  pod 'FirebaseStorage', :modular_headers => true
  pod 'FirebaseAuth', :modular_headers => true
  
  # Force these pods to be static frameworks
  pod 'Firebase/CoreOnly', :modular_headers => true
  pod 'Firebase/Database', :modular_headers => true
  #pod 'Firebase/DynamicLinks', :modular_headers => true
  pod 'Firebase/Messaging', :modular_headers => true
  pod 'Firebase/Storage', :modular_headers => true
  pod 'Firebase/Auth', :modular_headers => true
end

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  # Switch to static frameworks for iOS
  use_frameworks! :linkage => :static
  use_modular_headers!

  # Call the Firebase static frameworks function
  firebase_static_frameworks
  
  pod 'PhoneNumberKit', '~> 3.7.6'

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end

post_install do |installer|
  # Use a simpler approach - just enable non-modular includes for all targets
  
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      if config.build_settings['WRAPPER_EXTENSION'] == 'bundle'
        config.build_settings['DEVELOPMENT_TEAM'] = '2D88RBYB8H'
      end
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'
      
      # Apply these fixes to ALL targets
      config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      
      # Disable User Script Sandboxing - needed for Xcode 16/iOS 18
      config.build_settings['USER_SCRIPT_SANDBOXING'] = 'NO'
      
      # This is the key fix for the 'file not found' errors
      config.build_settings['HEADER_SEARCH_PATHS'] ||= ['$(inherited)'] 
      config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/Firebase/CoreOnly/Sources'
      config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/FirebaseCore/FirebaseCore/Sources/Public'
      config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/FirebaseDatabase/FirebaseDatabase/Sources/Public'
      config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/FirebaseMessaging/FirebaseMessaging/Sources/Public'
      #config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/FirebaseDynamicLinks/FirebaseDynamicLinks/Sources/Public'
      config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/FirebaseStorage/FirebaseStorage/Sources/Public'
      config.build_settings['HEADER_SEARCH_PATHS'] << '${PODS_ROOT}/FirebaseAuth/FirebaseAuth/Sources/Public'
      
      # Additional Firebase-specific settings
      if target.name.start_with?('Firebase') || target.name.start_with?('Google') || 
         target.name.include?('firebase_messaging') || target.name.include?('firebase_database') || 
         target.name.include?('firebase_dynamic_links') || target.name.include?('firebase_storage') || 
         target.name.include?('firebase_auth')
        config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'
        config.build_settings['APPLICATION_EXTENSION_API_ONLY'] = 'NO'
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', 'FIREBASE_USE_MODULAR_HEADERS=1']
      end
    end

    flutter_additional_ios_build_settings(target)
  end
end
